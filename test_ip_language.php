<?php
/**
 * test_ip_language.php
 * Тест системы определения языка по IP адресу
 */

declare(strict_types=1);

echo "🌍 ТЕСТ СИСТЕМЫ ОПРЕДЕЛЕНИЯ ЯЗЫКА ПО IP\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    echo "1. 🧪 ТЕСТ API ОПРЕДЕЛЕНИЯ ЯЗЫКА:\n";
    
    // Тестируем API определения языка
    $response = file_get_contents('http://argun-clear.loc/api/get_user_language.php');
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data) {
            echo "   ✅ API работает\n";
            echo "   📍 IP адрес: {$data['ip']}\n";
            echo "   🌐 Определенный язык: {$data['language']}\n";
            echo "   🇷🇺 Русскоязычный: " . ($data['is_russian'] ? 'Да' : 'Нет') . "\n";
            
            if (isset($data['error'])) {
                echo "   ⚠️ Предупреждение: {$data['error']}\n";
            }
        } else {
            echo "   ❌ API вернул некорректный JSON\n";
        }
    } else {
        echo "   ❌ API не отвечает\n";
    }
    
    echo "\n2. 🧪 ТЕСТ РАЗЛИЧНЫХ IP АДРЕСОВ:\n";
    
    // Тестируем функции напрямую
    require_once 'api/get_user_language.php';
    
    $testIPs = [
        '127.0.0.1' => 'Локальный IP',
        '***********' => 'Приватный IP',
        '*********' => 'Яндекс DNS (Россия)',
        '*******' => 'Google DNS (США)',
        '*******' => 'Cloudflare DNS (США)',
        '***********' => 'Российский IP',
        '************' => 'Российский IP',
        '***********' => 'Российский IP'
    ];
    
    foreach ($testIPs as $ip => $description) {
        $isLocal = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
        
        echo "   🔍 {$ip} ({$description}):\n";
        echo "     - Локальный: " . ($isLocal ? 'Да' : 'Нет') . "\n";
        
        if ($isLocal) {
            echo "     - Язык: ru (локальный fallback)\n";
        } else {
            echo "     - Требует проверки через внешний API\n";
        }
    }
    
    echo "\n3. 📝 ТЕСТ ПЕРЕВОДОВ СЧЕТЧИКОВ:\n";
    
    // Тестируем переводы для разных значений
    $testCounts = [0, 1, 2, 3, 5, 10, 15, 20];
    
    echo "   🇷🇺 Русские переводы:\n";
    foreach ($testCounts as $count) {
        $text = getCounterTextForTest($count, true);
        echo "     {$count} → \"{$text}\"\n";
    }
    
    echo "\n   🇺🇸 Английские переводы:\n";
    foreach ($testCounts as $count) {
        $text = getCounterTextForTest($count, false);
        echo "     {$count} → \"{$text}\"\n";
    }
    
    echo "\n4. 🔧 ПРОВЕРКА JAVASCRIPT ПАТЧА:\n";
    
    if (file_exists('js/counters-patch.js')) {
        $jsContent = file_get_contents('js/counters-patch.js');
        
        // Проверяем ключевые функции
        $checks = [
            'getUserLanguage' => 'Функция определения языка',
            'getCounterText' => 'Функция переводов',
            'api/get_user_language.php' => 'Вызов API языка',
            'native-banner-counter' => 'Правильные ID элементов',
            'langInfo.isRussian' => 'Использование IP геолокации'
        ];
        
        foreach ($checks as $search => $description) {
            if (strpos($jsContent, $search) !== false) {
                echo "   ✅ {$description}: найдено\n";
            } else {
                echo "   ❌ {$description}: НЕ НАЙДЕНО\n";
            }
        }
        
        $fileSize = filesize('js/counters-patch.js');
        echo "   📊 Размер файла: " . number_format($fileSize) . " bytes\n";
    } else {
        echo "   ❌ Файл js/counters-patch.js не найден\n";
    }
    
    echo "\n5. 🎯 ИНСТРУКЦИИ ДЛЯ ПРИМЕНЕНИЯ:\n";
    
    echo "   1. 📄 Добавьте в index.html перед </body>:\n";
    echo "      <script src='js/counters-patch.js'></script>\n\n";
    
    echo "   2. 🔄 Обновите страницу миниапп (F5)\n\n";
    
    echo "   3. 🧪 Откройте консоль браузера (F12) и выполните:\n";
    echo "      window.forceUpdateCounters()\n\n";
    
    echo "   4. 👀 Проверьте логи в консоли:\n";
    echo "      - Должно появиться сообщение о определении языка по IP\n";
    echo "      - Счетчики должны обновиться с правильными переводами\n\n";
    
    echo "   5. 🌍 Тестирование разных IP:\n";
    echo "      - Российские IP → русские тексты\n";
    echo "      - Зарубежные IP → английские тексты\n";
    echo "      - Локальные IP → русские тексты (fallback)\n\n";
    
    echo "✅ СИСТЕМА ГОТОВА К ИСПОЛЬЗОВАНИЮ!\n";
    
    echo "\n📊 ОЖИДАЕМЫЕ РЕЗУЛЬТАТЫ:\n";
    echo "   🇷🇺 Русский IP: \"осталось 15 показов\"\n";
    echo "   🇺🇸 Английский IP: \"15 ad views left\"\n";
    echo "   🚫 Лимит исчерпан (RU): \"лимит исчерпан\"\n";
    echo "   🚫 Лимит исчерпан (EN): \"limit reached\"\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

/**
 * Функция для тестирования переводов счетчиков
 */
function getCounterTextForTest($remaining, $isRussian) {
    if ($isRussian) {
        // Русский язык с правильными склонениями
        if ($remaining === 0) return 'лимит исчерпан';
        if ($remaining === 1) return 'остался 1 показ';
        if ($remaining >= 2 && $remaining <= 4) return "осталось {$remaining} показа";
        return "осталось {$remaining} показов";
    } else {
        // Английский язык
        if ($remaining === 0) return 'limit reached';
        if ($remaining === 1) return '1 ad view left';
        return "{$remaining} ad views left";
    }
}
?>
