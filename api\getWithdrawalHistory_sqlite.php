<?php
/**
 * api/getWithdrawalHistory_sqlite.php
 * API для получения истории выплат из SQLite базы данных
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Получаем данные запроса
    $input = null;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
    } else {
        $input = $_GET;
    }
    
    if (!$input) {
        throw new Exception('No input data provided');
    }
    
    // Получаем ID пользователя
    $userId = null;
    
    if (isset($input['user_id'])) {
        $userId = (string)$input['user_id'];
    } elseif (isset($input['initData'])) {
        // Парсим initData из Telegram
        parse_str($input['initData'], $initDataParsed);
        if (isset($initDataParsed['user'])) {
            $userData = json_decode($initDataParsed['user'], true);
            if ($userData && isset($userData['id'])) {
                $userId = (string)$userData['id'];
            }
        }
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Инициализируем базу данных
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    // Получаем параметры пагинации
    $limit = isset($input['limit']) ? (int)$input['limit'] : 50;
    $offset = isset($input['offset']) ? (int)$input['offset'] : 0;
    $status = isset($input['status']) ? $input['status'] : null;
    
    // Формируем SQL запрос
    $whereConditions = ['user_id = ?'];
    $params = [$userId];
    
    if ($status) {
        $whereConditions[] = 'status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Получаем выплаты пользователя
    $withdrawals = $db->query(
        "SELECT
            id,
            user_id,
            amount,
            currency,
            wallet_address,
            status,
            transaction_hash,
            requested_at,
            processed_at,
            error_message
         FROM user_withdrawals
         WHERE {$whereClause}
         ORDER BY requested_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, [$limit, $offset])
    );
    
    // Получаем общее количество выплат
    $totalCount = $db->query(
        "SELECT COUNT(*) as count FROM user_withdrawals WHERE {$whereClause}",
        $params
    );
    
    $total = $totalCount[0]['count'] ?? 0;
    
    // Обрабатываем данные выплат
    $processedWithdrawals = [];
    
    foreach ($withdrawals as $withdrawal) {
        $processedWithdrawal = [
            'id' => $withdrawal['id'],
            'user_id' => $withdrawal['user_id'],
            'amount' => (float)$withdrawal['amount'],
            'currency' => $withdrawal['currency'],
            'wallet_address' => $withdrawal['wallet_address'],
            'status' => $withdrawal['status'],
            'transaction_hash' => $withdrawal['transaction_hash'],
            'requested_at' => $withdrawal['requested_at'],
            'processed_at' => $withdrawal['processed_at'],
            'error_message' => $withdrawal['error_message'],
            'timestamp' => strtotime($withdrawal['requested_at']), // Для совместимости
            'formatted_date' => date('d.m.Y', strtotime($withdrawal['requested_at'])),
            'formatted_time' => date('H:i', strtotime($withdrawal['requested_at'])),
            'status_text' => getStatusText($withdrawal['status']),
            'status_class' => getStatusClass($withdrawal['status']),
            'can_cancel' => canCancelWithdrawal($withdrawal['status']),
            // Дополнительные поля для совместимости
            'created_at' => $withdrawal['requested_at'],
            'updated_at' => $withdrawal['processed_at'] ?? $withdrawal['requested_at'],
            'admin_notes' => $withdrawal['error_message']
        ];

        $processedWithdrawals[] = $processedWithdrawal;
    }
    
    // Получаем статистику по статусам
    $statusStats = $db->query(
        "SELECT status, COUNT(*) as count, SUM(amount) as total_amount 
         FROM user_withdrawals 
         WHERE user_id = ? 
         GROUP BY status",
        [$userId]
    );
    
    $stats = [];
    foreach ($statusStats as $stat) {
        $stats[$stat['status']] = [
            'count' => (int)$stat['count'],
            'total_amount' => (float)$stat['total_amount']
        ];
    }
    
    // Формируем ответ
    $response = [
        'success' => true,
        'withdrawals' => $processedWithdrawals,
        'pagination' => [
            'total' => (int)$total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total
        ],
        'stats' => $stats,
        'user_info' => [
            'user_id' => $userId,
            'total_withdrawals' => (int)$total
        ],
        'api_info' => [
            'version' => 'sqlite_v1.0',
            'source' => 'user_withdrawals_table',
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("getWithdrawalHistory_sqlite ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'WITHDRAWAL_HISTORY_ERROR',
        'withdrawals' => [], // Пустой массив для совместимости
        'pagination' => [
            'total' => 0,
            'limit' => 50,
            'offset' => 0,
            'has_more' => false
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Получить текст статуса на русском языке
 */
function getStatusText($status) {
    $statusTexts = [
        'pending' => 'Ожидает обработки',
        'processing' => 'В обработке',
        'completed' => 'Завершена',
        'failed' => 'Ошибка',
        'cancelled' => 'Отменена',
        'expired' => 'Истекла',
        'partially_paid' => 'Частично оплачена',
        'confirming' => 'Подтверждение',
        'confirmed' => 'Подтверждена',
        'sending' => 'Отправка',
        'partially_paid_expired' => 'Частично оплачена (истекла)',
        'refunded' => 'Возвращена',
        'waiting' => 'Ожидание'
    ];
    
    return $statusTexts[$status] ?? ucfirst($status);
}

/**
 * Получить CSS класс для статуса
 */
function getStatusClass($status) {
    $statusClasses = [
        'pending' => 'status-pending',
        'processing' => 'status-processing',
        'completed' => 'status-completed',
        'failed' => 'status-failed',
        'cancelled' => 'status-cancelled',
        'expired' => 'status-expired',
        'confirming' => 'status-confirming',
        'confirmed' => 'status-confirmed',
        'sending' => 'status-sending'
    ];
    
    return $statusClasses[$status] ?? 'status-unknown';
}

/**
 * Проверить, можно ли отменить выплату
 */
function canCancelWithdrawal($status) {
    $cancellableStatuses = ['pending', 'waiting', 'confirming'];
    return in_array($status, $cancellableStatuses);
}
?>
