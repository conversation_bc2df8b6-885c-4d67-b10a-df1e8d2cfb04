<?php
/**
 * test_api_response.php
 * Тест ответа API
 */

// Симулируем сессию
session_start();
$_SESSION['authenticated'] = true;

// Очищаем буфер вывода
ob_start();

// Подключаем API
include 'api/admin/ad_stats_api.php';

// Получаем ответ
$response = ob_get_clean();

echo "🔧 ТЕСТ ОТВЕТА API\n";
echo "=" . str_repeat("=", 30) . "\n\n";

echo "Ответ API:\n";
echo $response;

// Декодируем JSON
$data = json_decode($response, true);

if ($data && isset($data['stats_by_type'])) {
    echo "\n\n📊 РАСШИФРОВКА CTR:\n";
    foreach ($data['stats_by_type'] as $type => $stats) {
        echo "- {$type}: {$stats['views']} просмотров, {$stats['clicks']} кликов, CTR: {$stats['ctr']}%\n";
    }
    
    echo "\n🌍 СТРАНЫ:\n";
    if (isset($data['stats_by_country'])) {
        foreach ($data['stats_by_country'] as $country => $clicks) {
            echo "- {$country}: {$clicks} кликов\n";
        }
    }
} else {
    echo "\n❌ Ошибка декодирования JSON или нет данных\n";
}
?>
