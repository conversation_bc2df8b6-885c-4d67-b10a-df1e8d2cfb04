<?php
/**
 * check_missing_translations.php
 * Проверка отсутствующих переводов
 */

declare(strict_types=1);

echo "🔍 ПРОВЕРКА ОТСУТСТВУЮЩИХ ПЕРЕВОДОВ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    // Ключи, которые используются в HTML
    $htmlKeys = [
        'currency.coins',
        'earnings.amount_to_withdraw',
        'earnings.minimum_withdrawal',
        'earnings.withdrawal_amount',
        'earnings.network_fee',
        'earnings.final_amount',
        'earnings.efficiency',
        'earnings.amount_missing',
        'earnings.enter_amount_for_calculation',
        'earnings.withdrawal_recommendations',
        'earnings.withdrawal_recommendations_tooltip',
        'earnings.selected_crypto',
        'earnings.amount_to_withdraw_coins',
        'earnings.amount_to_receive',
        'earnings.wallet_address',
        'earnings.wallet_warning',
        'earnings.withdrawal_history_title',
        'earnings.withdrawal_history_description',
        'earnings.refresh_history',
        'friends.share_title',
        'friends.share_description',
        'friends.referral_link_title',
        'friends.referral_link_description',
        'friends.total_referrals',
        'friends.earned_from_referrals',
        'friends.no_referrals',
        'friends.invite_friends_hint',
        'friends.my_referrer_title',
        'friends.referrer_description',
        'buttons.refresh_stats'
    ];
    
    echo "1. 📊 ПРОВЕРКА КЛЮЧЕЙ ИЗ HTML:\n";
    
    $missing = [];
    $found = [];
    
    foreach ($htmlKeys as $key) {
        // Проверяем разные варианты ключа
        $variants = [
            $key,
            str_replace('.', '_', $key), // currency.coins -> currency_coins
            explode('.', $key)[1] ?? $key, // currency.coins -> coins
        ];
        
        $keyFound = false;
        foreach ($variants as $variant) {
            $result = $sqlite->query("SELECT text_value FROM bot_texts WHERE text_key = ? AND language_code = 'ru'", [$variant]);
            if (!empty($result)) {
                $found[] = $key . " (найден как: {$variant})";
                $keyFound = true;
                break;
            }
        }
        
        if (!$keyFound) {
            $missing[] = $key;
        }
    }
    
    echo "   ✅ Найдено: " . count($found) . " ключей\n";
    foreach ($found as $f) {
        echo "     - {$f}\n";
    }
    
    echo "\n   ❌ Отсутствует: " . count($missing) . " ключей\n";
    foreach ($missing as $m) {
        echo "     - {$m}\n";
    }
    
    echo "\n2. 📝 СОЗДАНИЕ ОТСУТСТВУЮЩИХ ПЕРЕВОДОВ:\n";
    
    $translations = [
        'currency.coins' => ['ru' => 'монет', 'en' => 'coins'],
        'earnings.amount_to_withdraw' => ['ru' => 'Сумма для вывода:', 'en' => 'Amount to withdraw:'],
        'earnings.minimum_withdrawal' => ['ru' => 'Минимум к выводу:', 'en' => 'Minimum withdrawal:'],
        'earnings.withdrawal_amount' => ['ru' => 'Сумма к выводу:', 'en' => 'Withdrawal amount:'],
        'earnings.network_fee' => ['ru' => 'Сетевая комиссия:', 'en' => 'Network fee:'],
        'earnings.final_amount' => ['ru' => 'Вы получите:', 'en' => 'You will receive:'],
        'earnings.efficiency' => ['ru' => 'Эффективность:', 'en' => 'Efficiency:'],
        'earnings.amount_missing' => ['ru' => 'Не хватает:', 'en' => 'Missing:'],
        'earnings.enter_amount_for_calculation' => ['ru' => 'Введите сумму для расчета', 'en' => 'Enter amount for calculation'],
        'earnings.withdrawal_recommendations' => ['ru' => 'Рекомендации к выводу', 'en' => 'Withdrawal recommendations'],
        'earnings.withdrawal_recommendations_tooltip' => ['ru' => 'Разные биржи и обменные сервисы имеют свои минимальные лимиты для зачисления средств. Убедитесь, что сумма вывода превышает минимальные требования получающей стороны, иначе платёж может зависнуть или не дойти до адресата. Рекомендуем выводить суммы от $5 и выше для надёжного получения средств.', 'en' => 'Different exchanges and exchange services have their own minimum limits for crediting funds. Make sure that the withdrawal amount exceeds the minimum requirements of the receiving party, otherwise the payment may hang or not reach the recipient. We recommend withdrawing amounts from $5 and above for reliable receipt of funds.'],
        'earnings.selected_crypto' => ['ru' => 'Выбранная криптовалюта:', 'en' => 'Selected cryptocurrency:'],
        'earnings.amount_to_withdraw_coins' => ['ru' => 'Сумма для вывода (монеты):', 'en' => 'Amount to withdraw (coins):'],
        'earnings.amount_to_receive' => ['ru' => 'Сумма к получению:', 'en' => 'Amount to receive:'],
        'earnings.wallet_address' => ['ru' => 'Адрес кошелька:', 'en' => 'Wallet address:'],
        'earnings.wallet_warning' => ['ru' => 'Важно: Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.', 'en' => 'Important: Make sure the wallet address is correct. Funds will be sent to the specified address and cannot be returned in case of error.'],
        'earnings.withdrawal_history_title' => ['ru' => 'История выплат', 'en' => 'Withdrawal history'],
        'earnings.withdrawal_history_description' => ['ru' => 'Здесь отображается история всех ваших выплат и их статусы.', 'en' => 'Here is the history of all your withdrawals and their statuses.'],
        'earnings.refresh_history' => ['ru' => 'Обновить историю', 'en' => 'Refresh history'],
        'friends.share_title' => ['ru' => 'Поделиться', 'en' => 'Share'],
        'friends.share_description' => ['ru' => 'Расскажите друзьям об этом крутом кибер-панк приложении и зарабатывайте вместе!', 'en' => 'Tell your friends about this cool cyber-punk app and earn together!'],
        'friends.referral_link_title' => ['ru' => 'Реферальная ссылка', 'en' => 'Referral link'],
        'friends.referral_link_description' => ['ru' => 'Поделитесь своей уникальной ссылкой и получайте 10% от заработка каждого приглашенного друга!', 'en' => 'Share your unique link and get 10% of the earnings of each invited friend!'],
        'friends.total_referrals' => ['ru' => 'Всего рефералов:', 'en' => 'Total referrals:'],
        'friends.earned_from_referrals' => ['ru' => 'Заработано на рефералах:', 'en' => 'Earned from referrals:'],
        'friends.no_referrals' => ['ru' => 'У вас пока нет рефералов', 'en' => 'You have no referrals yet'],
        'friends.invite_friends_hint' => ['ru' => 'Пригласите друзей и начните зарабатывать!', 'en' => 'Invite friends and start earning!'],
        'friends.my_referrer_title' => ['ru' => 'Мой реферер', 'en' => 'My referrer'],
        'friends.referrer_description' => ['ru' => 'Информация о пользователе, который пригласил вас в приложение.', 'en' => 'Information about the user who invited you to the app.'],
        'buttons.refresh_stats' => ['ru' => 'Обновить статистику', 'en' => 'Refresh stats']
    ];
    
    $added = 0;
    foreach ($translations as $key => $langs) {
        foreach ($langs as $lang => $text) {
            // Проверяем, есть ли уже такой ключ
            $existing = $sqlite->query("SELECT id FROM bot_texts WHERE text_key = ? AND language_code = ?", [$key, $lang]);
            
            if (empty($existing)) {
                // Определяем категорию
                $category = explode('.', $key)[0];
                
                // Добавляем перевод
                $sqlite->query("
                    INSERT INTO bot_texts (text_key, text_value, language_code, category)
                    VALUES (?, ?, ?, ?)
                ", [$key, $text, $lang, $category]);
                
                echo "   ✅ Добавлен: [{$lang}] {$key}\n";
                $added++;
            }
        }
    }
    
    echo "\n   📊 Добавлено переводов: {$added}\n";
    
    echo "\n3. 🔄 ПРОВЕРКА ПОСЛЕ ДОБАВЛЕНИЯ:\n";
    
    $stillMissing = [];
    foreach ($htmlKeys as $key) {
        $result = $sqlite->query("SELECT text_value FROM bot_texts WHERE text_key = ? AND language_code = 'ru'", [$key]);
        if (empty($result)) {
            $stillMissing[] = $key;
        }
    }
    
    if (empty($stillMissing)) {
        echo "   ✅ Все ключи найдены!\n";
    } else {
        echo "   ❌ Все еще отсутствует: " . count($stillMissing) . " ключей\n";
        foreach ($stillMissing as $m) {
            echo "     - {$m}\n";
        }
    }
    
    echo "\n4. 🌐 ТЕСТ API ПОСЛЕ ОБНОВЛЕНИЯ:\n";
    
    $response = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=ru');
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['app'])) {
            echo "   ✅ API работает\n";
            echo "   📊 Категорий: " . count($data['app']) . "\n";
            
            // Проверяем несколько ключей
            $testKeys = ['currency.coins', 'earnings.amount_to_withdraw', 'friends.share_title'];
            foreach ($testKeys as $testKey) {
                $parts = explode('.', $testKey);
                $category = $parts[0];
                $key = $parts[1];
                
                if (isset($data['app'][$category][$key])) {
                    echo "   ✅ {$testKey}: {$data['app'][$category][$key]}\n";
                } else {
                    echo "   ❌ {$testKey}: НЕ НАЙДЕН\n";
                }
            }
        }
    }
    
    echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";
    echo "🔧 Теперь обновите страницу в браузере, чтобы увидеть переводы\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
