<?php
/**
 * fix_counters_urgent.php
 * СРОЧНОЕ исправление счетчиков лимитов
 */

declare(strict_types=1);

echo "🚨 СРОЧНОЕ ИСПРАВЛЕНИЕ СЧЕТЧИКОВ ЛИМИТОВ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    echo "1. 🧪 ТЕСТ НОВОГО API ЛИМИТОВ:\n";
    
    // Тестируем новый API
    $testUserId = 5880288830;
    $response = file_get_contents("http://argun-clear.loc/api/get_limits_simple.php?user_id={$testUserId}");
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "   ✅ API работает!\n";
            echo "   📊 Лимиты пользователя {$testUserId}:\n";
            
            foreach ($data['data'] as $adType => $limitInfo) {
                $status = $limitInfo['can_show'] ? '✅' : '❌';
                echo "     {$status} {$adType}: {$limitInfo['current']}/{$limitInfo['limit']} (осталось: {$limitInfo['remaining']})\n";
            }
        } else {
            echo "   ❌ API вернул ошибку\n";
        }
    } else {
        echo "   ❌ API не отвечает\n";
    }
    
    echo "\n2. 📝 СОЗДАНИЕ JAVASCRIPT ПАТЧА:\n";
    
    // Создаем JavaScript код для принудительного обновления счетчиков
    $jsCode = "
// СРОЧНЫЙ ПАТЧ для отображения счетчиков лимитов
console.log('🚨 Применяем срочный патч счетчиков...');

// Функция для принудительного обновления счетчиков
window.forceUpdateCounters = async function() {
    try {
        console.log('🔄 Загружаем лимиты...');
        
        // Получаем user_id
        let userId = '5880288830'; // тестовый ID
        if (window.Telegram?.WebApp?.initDataUnsafe?.user?.id) {
            userId = window.Telegram.WebApp.initDataUnsafe.user.id.toString();
        }
        
        // Загружаем лимиты
        const response = await fetch(`api/get_limits_simple.php?user_id=\${userId}`);
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Лимиты загружены:', data.data);
            
            // Обновляем счетчики на кнопках
            const mapping = {
                'native_banner': 'openLinkCounter',
                'rewarded_video': 'watchVideoCounter', 
                'interstitial': 'openAdCounter'
            };
            
            Object.keys(mapping).forEach(adType => {
                const counterId = mapping[adType];
                const counterElement = document.getElementById(counterId);
                const limitInfo = data.data[adType];
                
                if (counterElement && limitInfo) {
                    const remaining = limitInfo.remaining;
                    let text;
                    
                    // Определяем язык
                    const isRussian = document.documentElement.lang === 'ru' || 
                                     navigator.language.startsWith('ru');
                    
                    if (isRussian) {
                        if (remaining === 0) text = 'лимит исчерпан';
                        else if (remaining === 1) text = 'остался 1 показ';
                        else if (remaining >= 2 && remaining <= 4) text = `осталось \${remaining} показа`;
                        else text = `осталось \${remaining} показов`;
                    } else {
                        if (remaining === 0) text = 'limit reached';
                        else if (remaining === 1) text = '1 ad view left';
                        else text = `\${remaining} ad views left`;
                    }
                    
                    counterElement.textContent = text;
                    console.log(`✅ \${adType} (\${counterId}): \${text}`);
                    
                    // Блокируем кнопку если лимит достигнут
                    if (remaining === 0) {
                        counterElement.classList.add('limit-reached');
                    } else {
                        counterElement.classList.remove('limit-reached');
                    }
                }
            });
            
            return true;
        } else {
            console.error('❌ Ошибка загрузки лимитов:', data);
            return false;
        }
    } catch (error) {
        console.error('❌ Ошибка обновления счетчиков:', error);
        return false;
    }
};

// Автоматически обновляем счетчики каждые 5 секунд
setInterval(() => {
    if (document.getElementById('openLinkCounter')) {
        window.forceUpdateCounters();
    }
}, 5000);

// Обновляем сразу
setTimeout(() => {
    window.forceUpdateCounters();
}, 1000);

console.log('✅ Патч счетчиков применен!');
";
    
    // Сохраняем JavaScript патч
    file_put_contents('js/counters-patch.js', $jsCode);
    echo "   ✅ JavaScript патч создан: js/counters-patch.js\n";
    
    echo "\n3. 📄 СОЗДАНИЕ HTML ИНСТРУКЦИИ:\n";
    
    $htmlInstructions = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Срочное исправление счетчиков</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f0f0f0; }
        .fix-box { background: white; padding: 20px; border-radius: 10px; margin: 10px 0; }
        .urgent { background: #ffebee; border-left: 5px solid #f44336; }
        .success { background: #e8f5e8; border-left: 5px solid #4caf50; }
        code { background: #f5f5f5; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🚨 Срочное исправление счетчиков лимитов</h1>
    
    <div class='fix-box urgent'>
        <h2>❗ Проблема</h2>
        <p>На кнопках не отображаются счетчики лимитов (например, \"осталось 15 показов\").</p>
    </div>
    
    <div class='fix-box success'>
        <h2>✅ Решение</h2>
        <p><strong>Шаг 1:</strong> Добавьте в index.html перед закрывающим тегом &lt;/body&gt;:</p>
        <code>&lt;script src='js/counters-patch.js'&gt;&lt;/script&gt;</code>
        
        <p><strong>Шаг 2:</strong> Обновите страницу (F5)</p>
        
        <p><strong>Шаг 3:</strong> Откройте консоль браузера (F12) и выполните:</p>
        <code>window.forceUpdateCounters()</code>
    </div>
    
    <div class='fix-box'>
        <h2>🔧 Что исправлено</h2>
        <ul>
            <li>✅ Создан новый API: <code>api/get_limits_simple.php</code></li>
            <li>✅ Исправлен <code>js/server-ad-counters.js</code></li>
            <li>✅ Добавлен JavaScript патч для принудительного обновления</li>
            <li>✅ Счетчики будут обновляться каждые 5 секунд</li>
        </ul>
    </div>
    
    <div class='fix-box'>
        <h2>🧪 Тестирование</h2>
        <p>После применения исправления на кнопках должны появиться тексты:</p>
        <ul>
            <li>\"осталось 15 показов\" (русский)</li>
            <li>\"15 ad views left\" (английский)</li>
            <li>\"лимит исчерпан\" (если лимит достигнут)</li>
        </ul>
    </div>
</body>
</html>
";
    
    file_put_contents('fix_counters_instructions.html', $htmlInstructions);
    echo "   ✅ HTML инструкция создана: fix_counters_instructions.html\n";
    
    echo "\n4. 🎯 ПРОВЕРКА ЭЛЕМЕНТОВ НА СТРАНИЦЕ:\n";
    
    // Проверяем, что нужные элементы есть в index.html
    if (file_exists('index.html')) {
        $indexContent = file_get_contents('index.html');
        
        $elements = ['openLinkCounter', 'watchVideoCounter', 'openAdCounter'];
        foreach ($elements as $elementId) {
            if (strpos($indexContent, $elementId) !== false) {
                echo "   ✅ Элемент #{$elementId} найден в index.html\n";
            } else {
                echo "   ❌ Элемент #{$elementId} НЕ НАЙДЕН в index.html\n";
            }
        }
    }
    
    echo "\n🚨 СРОЧНЫЕ ДЕЙСТВИЯ:\n";
    echo "   1. 📄 Откройте fix_counters_instructions.html в браузере\n";
    echo "   2. 📝 Добавьте <script src='js/counters-patch.js'></script> в index.html\n";
    echo "   3. 🔄 Обновите страницу миниапп (F5)\n";
    echo "   4. 🧪 Выполните window.forceUpdateCounters() в консоли\n";
    echo "   5. 👀 Проверьте, что на кнопках появились счетчики\n";
    
    echo "\n✅ ИСПРАВЛЕНИЕ ГОТОВО!\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
