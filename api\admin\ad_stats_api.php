<?php
/**
 * ad_stats_api.php
 * API для получения статистики рекламы из SQLite
 * Обновлено: 2025-07-17 для корректной работы с SQLite
 */

header('Content-Type: application/json');
date_default_timezone_set('UTC');

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Подключаем SQLite менеджер
require_once __DIR__ . '/../../database/real_sqlite_manager.php';

// --- Функции ---

function getCountryCode($ip) {
    static $cache = [];
    static $persistent_cache = null;

    // Инициализируем постоянный кэш из файла
    if ($persistent_cache === null) {
        $cache_file = __DIR__ . '/../../database/ip_country_cache.json';
        if (file_exists($cache_file)) {
            $persistent_cache = json_decode(file_get_contents($cache_file), true) ?: [];
        } else {
            $persistent_cache = [];
        }
    }

    // Проверяем кэш в памяти
    if (isset($cache[$ip])) {
        return $cache[$ip];
    }

    // Проверяем постоянный кэш
    if (isset($persistent_cache[$ip])) {
        $cache[$ip] = $persistent_cache[$ip];
        return $persistent_cache[$ip];
    }

    // Проверяем локальные и приватные IP адреса
    if ($ip === '127.0.0.1' || $ip === 'unknown' || $ip === 'localhost' ||
        !filter_var($ip, FILTER_VALIDATE_IP) ||
        filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        $cache[$ip] = 'Unknown';
        return 'Unknown';
    }

    // Ограничиваем количество новых запросов к API за один вызов
    static $api_calls_count = 0;
    if ($api_calls_count >= 5) {
        $cache[$ip] = 'XX';
        return 'XX';
    }

    // Пробуем только один быстрый API
    $url = "http://ip-api.com/json/{$ip}?fields=status,countryCode";

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1); // Уменьшили таймаут
    curl_setopt($ch, CURLOPT_TIMEOUT, 2); // Уменьшили таймаут
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Stats/1.0)');
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $api_calls_count++;

    if ($response !== false && $httpCode === 200) {
        $data = json_decode($response, true);
        if (isset($data['status']) && $data['status'] === 'success' && isset($data['countryCode'])) {
            $cache[$ip] = $data['countryCode'];
            $persistent_cache[$ip] = $data['countryCode'];

            // Сохраняем в файл кэша (асинхронно)
            $cache_file = __DIR__ . '/../../database/ip_country_cache.json';
            file_put_contents($cache_file, json_encode($persistent_cache, JSON_PRETTY_PRINT));

            return $data['countryCode'];
        }
    }

    // Если API не сработал, возвращаем код по умолчанию
    $cache[$ip] = 'XX';
    return 'XX';
}

function getCountryName($countryCode) {
    $countries = [
        'RU' => 'Россия',
        'US' => 'США',
        'DE' => 'Германия',
        'FR' => 'Франция',
        'GB' => 'Великобритания',
        'CN' => 'Китай',
        'JP' => 'Япония',
        'KR' => 'Южная Корея',
        'IN' => 'Индия',
        'BR' => 'Бразилия',
        'CA' => 'Канада',
        'AU' => 'Австралия',
        'IT' => 'Италия',
        'ES' => 'Испания',
        'NL' => 'Нидерланды',
        'PL' => 'Польша',
        'TR' => 'Турция',
        'UA' => 'Украина',
        'BY' => 'Беларусь',
        'KZ' => 'Казахстан',
        'UZ' => 'Узбекистан',
        'AZ' => 'Азербайджан',
        'AM' => 'Армения',
        'GE' => 'Грузия',
        'MD' => 'Молдова',
        'LT' => 'Литва',
        'LV' => 'Латвия',
        'EE' => 'Эстония',
        'FI' => 'Финляндия',
        'SE' => 'Швеция',
        'NO' => 'Норвегия',
        'DK' => 'Дания',
        'CH' => 'Швейцария',
        'AT' => 'Австрия',
        'BE' => 'Бельгия',
        'CZ' => 'Чехия',
        'SK' => 'Словакия',
        'HU' => 'Венгрия',
        'RO' => 'Румыния',
        'BG' => 'Болгария',
        'HR' => 'Хорватия',
        'SI' => 'Словения',
        'RS' => 'Сербия',
        'BA' => 'Босния и Герцеговина',
        'MK' => 'Северная Македония',
        'AL' => 'Албания',
        'ME' => 'Черногория',
        'XK' => 'Косово',
        'GR' => 'Греция',
        'CY' => 'Кипр',
        'MT' => 'Мальта',
        'PT' => 'Португалия',
        'IE' => 'Ирландия',
        'IS' => 'Исландия',
        'LU' => 'Люксембург',
        'LI' => 'Лихтенштейн',
        'MC' => 'Монако',
        'AD' => 'Андорра',
        'SM' => 'Сан-Марино',
        'VA' => 'Ватикан',
        'XX' => 'Неизвестно',
        'Unknown' => 'Неизвестно'
    ];

    return $countries[$countryCode] ?? $countryCode;
}

function load_json_file($filepath, $limit = null) {
    if (!file_exists($filepath)) return [];
    $content = file_get_contents($filepath);
    if (empty($content)) return [];
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error in file: $filepath - " . json_last_error_msg());
        return [];
    }

    if (!is_array($data)) return [];

    // Ограничиваем количество записей для производительности
    if ($limit && count($data) > $limit) {
        // Берем последние записи (самые свежие)
        $data = array_slice($data, -$limit);
    }

    return $data;
}

// --- Основная логика ---

// Проверяем кэш результатов
$cache_key = md5(serialize($_GET));
$cache_file = __DIR__ . '/../../database/stats_cache_' . $cache_key . '.json';
$cache_ttl = 300; // 5 минут

if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_ttl) {
    $cached_data = json_decode(file_get_contents($cache_file), true);
    if ($cached_data && isset($cached_data['success'])) {
        echo json_encode($cached_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Получение параметров фильтрации
$date_from = $_GET['date_from'] ?? null;
$date_to = $_GET['date_to'] ?? null;
$ad_type_filter = $_GET['ad_type'] ?? 'all';

// Загрузка данных из SQLite
try {
    $sqlite = new RealSQLiteManager();

    // Строим WHERE условия для фильтрации
    $whereConditions = [];
    $params = [];
    $viewParams = [];
    $clickParams = [];

    // Условия для фильтрации просмотров
    $viewWhereConditions = ["ad_type != 'test_banner'"];
    if ($date_from) {
        $viewWhereConditions[] = "DATE(timestamp) >= ?";
        $viewParams[] = $date_from;
    }
    if ($date_to) {
        $viewWhereConditions[] = "DATE(timestamp) <= ?";
        $viewParams[] = $date_to;
    }
    if ($ad_type_filter !== 'all') {
        $viewWhereConditions[] = "ad_type = ?";
        $viewParams[] = $ad_type_filter;
    }

    // Условия для фильтрации кликов
    $clickWhereConditions = ["ad_type != 'test_banner'"];
    if ($date_from) {
        $clickWhereConditions[] = "DATE(timestamp) >= ?";
        $clickParams[] = $date_from;
    }
    if ($date_to) {
        $clickWhereConditions[] = "DATE(timestamp) <= ?";
        $clickParams[] = $date_to;
    }
    if ($ad_type_filter !== 'all') {
        $clickWhereConditions[] = "ad_type = ?";
        $clickParams[] = $ad_type_filter;
    }

    $viewWhereClause = 'WHERE ' . implode(' AND ', $viewWhereConditions);
    $clickWhereClause = 'WHERE ' . implode(' AND ', $clickWhereConditions);

    // Получаем статистику по типам рекламы (исключаем test_banner)
    // ИСПРАВЛЕННЫЙ ЗАПРОС: используем UNION для получения всех типов
    $statsQuery = "
        SELECT
            ad_type,
            SUM(views) as views,
            SUM(clicks) as clicks,
            SUM(rewards) as rewards,
            CASE
                WHEN SUM(clicks) > 0 THEN ROUND((SUM(views) * 1.0 / SUM(clicks)), 4)
                ELSE 0
            END as ctr
        FROM (
            SELECT
                ad_type,
                COUNT(*) as views,
                0 as clicks,
                SUM(reward) as rewards
            FROM ad_views
            {$viewWhereClause}
            GROUP BY ad_type

            UNION ALL

            SELECT
                ad_type,
                0 as views,
                COUNT(*) as clicks,
                0 as rewards
            FROM ad_clicks
            {$clickWhereClause}
            GROUP BY ad_type
        ) combined
        GROUP BY ad_type
        ORDER BY clicks DESC
    ";

    // Объединяем параметры для запроса
    $allParams = array_merge($viewParams, $clickParams);

    $stats_by_type = [];
    $statsResults = $sqlite->query($statsQuery, $allParams);

    foreach ($statsResults as $row) {
        $stats_by_type[$row['ad_type']] = [
            'views' => (int)$row['views'],
            'clicks' => (int)$row['clicks'],
            'rewards' => (float)$row['rewards'],
            'ctr' => round((float)$row['ctr'] * 100, 2) // Исправленная формула уже дает проценты
        ];
    }

    // Получаем статистику по странам (из кликов, исключаем test_banner)
    $countryQuery = "
        SELECT
            ip_address,
            COUNT(*) as clicks
        FROM ad_clicks
        {$clickWhereClause} AND ip_address IS NOT NULL AND ip_address != ''
        GROUP BY ip_address
        ORDER BY clicks DESC
        LIMIT 100
    ";

    $countryResults = $sqlite->query($countryQuery, $clickParams);
    $stats_by_country = [];

    foreach ($countryResults as $row) {
        $countryCode = getCountryCode($row['ip_address']);
        if ($countryCode) {
            $countryName = getCountryName($countryCode);
            if (!isset($stats_by_country[$countryName])) {
                $stats_by_country[$countryName] = 0;
            }
            $stats_by_country[$countryName] += (int)$row['clicks'];
        }
    }

    arsort($stats_by_country);

    // Получаем почасовую статистику (исключаем test_banner)
    $hourlyQuery = "
        SELECT
            CAST(strftime('%H', timestamp) AS INTEGER) as hour,
            COUNT(*) as views
        FROM ad_views
        {$viewWhereClause}
        GROUP BY hour
        ORDER BY hour
    ";

    $hourlyResults = $sqlite->query($hourlyQuery, $viewParams);
    $hourly_stats = array_fill(0, 24, ['clicks' => 0, 'views' => 0]);

    foreach ($hourlyResults as $row) {
        $hour = (int)$row['hour'];
        if ($hour >= 0 && $hour < 24) {
            $hourly_stats[$hour]['views'] = (int)$row['views'];
        }
    }

    // Добавляем клики по часам (исключаем test_banner)
    $hourlyClicksQuery = "
        SELECT
            CAST(strftime('%H', timestamp) AS INTEGER) as hour,
            COUNT(*) as clicks
        FROM ad_clicks
        {$clickWhereClause}
        GROUP BY hour
        ORDER BY hour
    ";

    $hourlyClicksResults = $sqlite->query($hourlyClicksQuery, $clickParams);

    foreach ($hourlyClicksResults as $row) {
        $hour = (int)$row['hour'];
        if ($hour >= 0 && $hour < 24) {
            $hourly_stats[$hour]['clicks'] = (int)$row['clicks'];
        }
    }

} catch (Exception $e) {
    error_log("Ошибка загрузки данных из SQLite: " . $e->getMessage());
    $stats_by_type = [];
    $stats_by_country = [];
    $hourly_stats = array_fill(0, 24, ['clicks' => 0, 'views' => 0]);
}

// Все данные уже получены в блоке try-catch выше

$response = [
    'success' => true,
    'stats_by_type' => $stats_by_type,
    'stats_by_country' => $stats_by_country,
    'hourly_stats' => $hourly_stats,
    'last_updated' => gmdate('Y-m-d H:i:s') . ' UTC',
    'data_info' => [
        'total_views' => array_sum(array_column($stats_by_type, 'views')),
        'total_clicks' => array_sum(array_column($stats_by_type, 'clicks')),
        'total_rewards' => array_sum(array_column($stats_by_type, 'rewards')),
        'total_countries' => count($stats_by_country),
        'filters_applied' => [
            'date_from' => $date_from,
            'date_to' => $date_to,
            'ad_type' => $ad_type_filter
        ]
    ]
];

// Кэширование результата
$cache_file = __DIR__ . '/../../database/stats_cache_' . md5(serialize($_GET)) . '.json';
file_put_contents($cache_file, json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);