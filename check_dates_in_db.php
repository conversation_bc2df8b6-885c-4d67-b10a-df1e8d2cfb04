<?php
require_once 'database/real_sqlite_manager.php';
$sqlite = new RealSQLiteManager();

echo "ПРОВЕРКА ДАТ В БАЗЕ:\n";

$today = date('Y-m-d');
echo "Сегодня: {$today}\n\n";

echo "ДАТЫ В AD_VIEWS:\n";
$viewDates = $sqlite->query("SELECT DATE(timestamp) as date, COUNT(*) as count FROM ad_views GROUP BY DATE(timestamp) ORDER BY date DESC LIMIT 10");
foreach($viewDates as $d) {
    echo "- {$d['date']}: {$d['count']} просмотров\n";
}

echo "\nДАТЫ В AD_CLICKS:\n";
$clickDates = $sqlite->query("SELECT DATE(timestamp) as date, COUNT(*) as count FROM ad_clicks GROUP BY DATE(timestamp) ORDER BY date DESC LIMIT 10");
foreach($clickDates as $d) {
    echo "- {$d['date']}: {$d['count']} кликов\n";
}

echo "\nСТАТИСТИКА ЗА СЕГОДНЯ:\n";
$todayViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE DATE(timestamp) = ? AND ad_type != 'test_banner'", [$today])[0]['count'];
$todayClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE DATE(timestamp) = ? AND ad_type != 'test_banner'", [$today])[0]['count'];

echo "- Просмотры за сегодня: {$todayViews}\n";
echo "- Клики за сегодня: {$todayClicks}\n";

echo "\nСТАТИСТИКА ЗА ПОСЛЕДНИЕ 7 ДНЕЙ:\n";
$weekViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE DATE(timestamp) >= DATE('now', '-7 days') AND ad_type != 'test_banner'")[0]['count'];
$weekClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE DATE(timestamp) >= DATE('now', '-7 days') AND ad_type != 'test_banner'")[0]['count'];

echo "- Просмотры за неделю: {$weekViews}\n";
echo "- Клики за неделю: {$weekClicks}\n";
?>
