<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо: Интеграция фронтенда с SQLite API баланса</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        .demo-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-title {
            color: var(--cyber-accent-neon);
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .demo-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .demo-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .status-message {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .status-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        .balance-showcase {
            text-align: center;
            padding: 30px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 15px;
            margin: 20px 0;
        }
        .balance-amount {
            font-size: 36px;
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 15px var(--cyber-glow);
            margin-bottom: 10px;
        }
        .balance-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .balance-detail-item {
            background: rgba(255, 107, 53, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .detail-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--cyber-accent-neon);
            margin-bottom: 5px;
        }
        .detail-label {
            font-size: 12px;
            color: var(--cyber-text-secondary);
        }
        .api-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .api-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--cyber-border);
            border-radius: 10px;
            padding: 15px;
        }
        .api-card.active {
            border-color: var(--cyber-accent-neon);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }
        .api-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--cyber-text-primary);
        }
        .api-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .api-features {
            font-size: 12px;
            color: var(--cyber-text-secondary);
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--cyber-border);
            border-radius: 50%;
            border-top-color: var(--cyber-accent-neon);
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); margin-bottom: 30px;">
            💰 Демо: Новая система баланса
        </h1>

        <!-- Статус подключения -->
        <div class="demo-section">
            <div class="demo-title">
                📡 Статус API подключения
            </div>
            <div class="api-comparison">
                <div class="api-card" id="sqlite-api-card">
                    <div class="api-title">SQLite API (Новый)</div>
                    <div id="sqlite-status" class="api-status">Проверка...</div>
                    <div class="api-features">
                        ✅ Атомарные операции<br>
                        ✅ Детальная статистика<br>
                        ✅ Резервирование средств<br>
                        ✅ История транзакций
                    </div>
                </div>
                <div class="api-card" id="json-api-card">
                    <div class="api-title">JSON API (Старый)</div>
                    <div id="json-status" class="api-status">Fallback</div>
                    <div class="api-features">
                        ⚠️ Базовый функционал<br>
                        ⚠️ Ограниченная статистика<br>
                        ❌ Нет резервирования<br>
                        ❌ Простая история
                    </div>
                </div>
            </div>
        </div>

        <!-- Отображение баланса -->
        <div class="demo-section">
            <div class="demo-title">
                💰 Отображение баланса
            </div>
            <div class="balance-showcase">
                <div id="main-balance" class="balance-amount">0 монет</div>
                <div id="balance-usd" style="color: var(--cyber-text-secondary);">≈ $0.00</div>
            </div>
            
            <div class="balance-details">
                <div class="balance-detail-item">
                    <div id="available-balance" class="detail-value">0</div>
                    <div class="detail-label">Доступно</div>
                </div>
                <div class="balance-detail-item">
                    <div id="reserved-balance" class="detail-value">0</div>
                    <div class="detail-label">Зарезервировано</div>
                </div>
                <div class="balance-detail-item">
                    <div id="total-earned" class="detail-value">0</div>
                    <div class="detail-label">Заработано</div>
                </div>
                <div class="balance-detail-item">
                    <div id="total-withdrawn" class="detail-value">0</div>
                    <div class="detail-label">Выведено</div>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="demo-button" onclick="loadBalance()">
                    <span id="load-btn-text">Загрузить баланс</span>
                </button>
                <button class="demo-button" onclick="refreshBalance()">Обновить</button>
            </div>
        </div>

        <!-- Дневная статистика -->
        <div class="demo-section">
            <div class="demo-title">
                📈 Дневная статистика
            </div>
            <div id="daily-stats" class="status-message status-info">
                Загрузите баланс для просмотра статистики
            </div>
        </div>

        <!-- Тестирование функций -->
        <div class="demo-section">
            <div class="demo-title">
                🧪 Тестирование функций
            </div>
            <div style="text-align: center;">
                <button class="demo-button" onclick="testBalanceManager()">Тест BalanceManager</button>
                <button class="demo-button" onclick="testApiClient()">Тест ApiClient</button>
                <button class="demo-button" onclick="simulateAdReward()">Симуляция награды</button>
            </div>
            <div id="test-results" class="status-message status-info">
                Готов к тестированию
            </div>
        </div>

        <!-- Лог операций -->
        <div class="demo-section">
            <div class="demo-title">
                📝 Лог операций
            </div>
            <div id="operation-log" style="background: rgba(0, 0, 0, 0.5); padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; border: 1px solid var(--cyber-border);">
                Ожидание операций...<br>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="demo-button" onclick="clearLog()" style="font-size: 12px; padding: 8px 16px;">Очистить лог</button>
            </div>
        </div>
    </div>

    <!-- Подключение скриптов -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="js/balance-api-client.js"></script>
    <script src="js/balance-manager.js"></script>

    <script>
        // Эмуляция Telegram WebApp
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22Demo%22%2C%22username%22%3A%22demouser%22%7D&auth_date=1640995200&hash=demo_hash',
                    initDataUnsafe: {
                        user: { id: 5880288830, first_name: 'Demo User', username: 'demouser' }
                    },
                    ready: () => log('Telegram WebApp готов (эмуляция)', 'success'),
                    expand: () => log('Telegram WebApp развернут (эмуляция)', 'info')
                }
            };
        }

        let balanceManager = null;
        let currentBalanceData = null;

        function log(message, type = 'info') {
            const logEl = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#74c0fc',
                success: '#51cf66',
                error: '#ff6b6b',
                warning: '#ffd43b'
            };
            const color = colors[type] || colors.info;
            logEl.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('operation-log').innerHTML = 'Лог очищен...<br>';
        }

        function setLoadingState(isLoading) {
            const btn = document.getElementById('load-btn-text');
            if (isLoading) {
                btn.innerHTML = '<span class="loading"></span> Загрузка...';
            } else {
                btn.textContent = 'Загрузить баланс';
            }
        }

        async function checkApiStatus() {
            try {
                log('Проверка доступности SQLite API...', 'info');
                
                const sqliteCard = document.getElementById('sqlite-api-card');
                const jsonCard = document.getElementById('json-api-card');
                const sqliteStatus = document.getElementById('sqlite-status');
                const jsonStatus = document.getElementById('json-status');

                if (window.balanceApiClient) {
                    const isAvailable = await window.balanceApiClient.checkSqliteApiAvailability();
                    
                    if (isAvailable) {
                        sqliteCard.classList.add('active');
                        sqliteStatus.textContent = '✅ Доступен';
                        sqliteStatus.style.background = 'rgba(76, 175, 80, 0.2)';
                        sqliteStatus.style.color = '#4caf50';
                        jsonStatus.textContent = '💤 Резерв';
                        log('SQLite API доступен', 'success');
                    } else {
                        jsonCard.classList.add('active');
                        sqliteStatus.textContent = '❌ Недоступен';
                        sqliteStatus.style.background = 'rgba(244, 67, 54, 0.2)';
                        sqliteStatus.style.color = '#f44336';
                        jsonStatus.textContent = '🔄 Активен';
                        jsonStatus.style.background = 'rgba(255, 193, 7, 0.2)';
                        jsonStatus.style.color = '#ffc107';
                        log('SQLite API недоступен, используется JSON API', 'warning');
                    }
                } else {
                    throw new Error('BalanceApiClient не загружен');
                }

            } catch (error) {
                log(`Ошибка проверки API: ${error.message}`, 'error');
            }
        }

        async function loadBalance() {
            try {
                setLoadingState(true);
                log('Загрузка баланса...', 'info');

                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const data = await window.balanceApiClient.getUserBalance();
                
                if (data.success) {
                    currentBalanceData = data;
                    updateBalanceDisplay(data);
                    log('Баланс успешно загружен', 'success');
                } else {
                    throw new Error('Сервер вернул ошибку');
                }

            } catch (error) {
                log(`Ошибка загрузки баланса: ${error.message}`, 'error');
                document.getElementById('main-balance').textContent = `Ошибка: ${error.message}`;
            } finally {
                setLoadingState(false);
            }
        }

        function updateBalanceDisplay(data) {
            document.getElementById('main-balance').textContent = `${data.balance.toLocaleString()} монет`;
            document.getElementById('balance-usd').textContent = `≈ $${data.balance_usd.toFixed(4)}`;
            document.getElementById('available-balance').textContent = data.available_balance.toLocaleString();
            document.getElementById('reserved-balance').textContent = data.reserved_balance.toLocaleString();
            document.getElementById('total-earned').textContent = data.total_earned.toLocaleString();
            document.getElementById('total-withdrawn').textContent = data.total_withdrawn.toLocaleString();

            // Дневная статистика
            const dailyStatsEl = document.getElementById('daily-stats');
            if (data.daily_stats) {
                const stats = data.daily_stats;
                const progress = Math.round((stats.earned_today / stats.limit) * 100);
                dailyStatsEl.innerHTML = `
                    <strong>Заработано сегодня:</strong> ${stats.earned_today || 0} из ${stats.limit || 0} монет (${progress}%)<br>
                    <strong>Осталось:</strong> ${(stats.limit || 0) - (stats.earned_today || 0)} монет
                `;
                dailyStatsEl.className = 'status-message status-success';
            } else {
                dailyStatsEl.textContent = 'Дневная статистика недоступна';
                dailyStatsEl.className = 'status-message status-info';
            }

            log(`Баланс обновлен: ${data.balance} монет (доступно: ${data.available_balance})`, 'success');
        }

        async function refreshBalance() {
            if (balanceManager && balanceManager.loadBalanceFromServer) {
                try {
                    log('Обновление через BalanceManager...', 'info');
                    await balanceManager.loadBalanceFromServer();
                    log('Баланс обновлен через BalanceManager', 'success');
                } catch (error) {
                    log(`Ошибка BalanceManager: ${error.message}`, 'error');
                    await loadBalance(); // Fallback
                }
            } else {
                await loadBalance();
            }
        }

        function testBalanceManager() {
            const testEl = document.getElementById('test-results');
            
            try {
                if (!balanceManager) {
                    balanceManager = new BalanceManager();
                }

                const tests = [
                    { name: 'getCurrentBalance', test: () => typeof balanceManager.getCurrentBalance() === 'number' },
                    { name: 'formatBalance', test: () => typeof balanceManager.formatBalance === 'function' },
                    { name: 'canWithdraw', test: () => typeof balanceManager.canWithdraw === 'function' },
                    { name: 'loadBalanceFromServer', test: () => typeof balanceManager.loadBalanceFromServer === 'function' }
                ];

                const results = tests.map(({ name, test }) => {
                    try {
                        return { name, passed: test() };
                    } catch (e) {
                        return { name, passed: false, error: e.message };
                    }
                });

                const passed = results.filter(r => r.passed).length;
                
                testEl.className = passed === tests.length ? 'status-message status-success' : 'status-message status-error';
                testEl.textContent = `BalanceManager: ${passed}/${tests.length} тестов пройдено`;
                
                log(`BalanceManager тесты: ${passed}/${tests.length} пройдено`, passed === tests.length ? 'success' : 'error');

            } catch (error) {
                testEl.className = 'status-message status-error';
                testEl.textContent = `Ошибка тестирования BalanceManager: ${error.message}`;
                log(`Ошибка тестирования BalanceManager: ${error.message}`, 'error');
            }
        }

        function testApiClient() {
            const testEl = document.getElementById('test-results');
            
            try {
                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const tests = [
                    { name: 'getUserBalance', test: () => typeof window.balanceApiClient.getUserBalance === 'function' },
                    { name: 'formatBalance', test: () => typeof window.balanceApiClient.formatBalance === 'function' },
                    { name: 'convertToUSD', test: () => typeof window.balanceApiClient.convertToUSD === 'function' },
                    { name: 'getUserDataForAPI', test: () => window.balanceApiClient.getUserDataForAPI() !== null }
                ];

                const results = tests.map(({ name, test }) => {
                    try {
                        return { name, passed: test() };
                    } catch (e) {
                        return { name, passed: false, error: e.message };
                    }
                });

                const passed = results.filter(r => r.passed).length;
                
                testEl.className = passed === tests.length ? 'status-message status-success' : 'status-message status-error';
                testEl.textContent = `ApiClient: ${passed}/${tests.length} тестов пройдено`;
                
                log(`ApiClient тесты: ${passed}/${tests.length} пройдено`, passed === tests.length ? 'success' : 'error');

            } catch (error) {
                testEl.className = 'status-message status-error';
                testEl.textContent = `Ошибка тестирования ApiClient: ${error.message}`;
                log(`Ошибка тестирования ApiClient: ${error.message}`, 'error');
            }
        }

        function simulateAdReward() {
            if (currentBalanceData) {
                const reward = 10;
                currentBalanceData.balance += reward;
                currentBalanceData.available_balance += reward;
                currentBalanceData.total_earned += reward;
                if (currentBalanceData.daily_stats) {
                    currentBalanceData.daily_stats.earned_today += reward;
                }
                
                updateBalanceDisplay(currentBalanceData);
                log(`Симуляция награды: +${reward} монет`, 'success');
            } else {
                log('Сначала загрузите баланс', 'warning');
            }
        }

        // Автоматическая инициализация
        window.addEventListener('load', () => {
            log('Демо страница загружена', 'info');
            
            setTimeout(() => {
                checkApiStatus();
            }, 500);
            
            setTimeout(() => {
                loadBalance();
            }, 1500);
        });
    </script>
</body>
</html>
