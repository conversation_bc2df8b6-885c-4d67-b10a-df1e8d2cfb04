<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест интеграции фронтенда с SQLite API баланса</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4a8a4a;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #8a4a4a;
        }
        .info {
            background: #2d4a5a;
            border: 1px solid #4a7a8a;
        }
        button {
            background: #4a7a8a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a8a9a;
        }
        .balance-display {
            font-size: 24px;
            font-weight: bold;
            color: #4a8a4a;
            text-align: center;
            padding: 20px;
            background: #1a3a1a;
            border-radius: 10px;
            margin: 10px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #4a8a4a;
        }
        .stat-label {
            font-size: 12px;
            color: #aaa;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 Тест интеграции фронтенда с SQLite API баланса</h1>
    
    <div class="test-section">
        <h2>📡 Статус API</h2>
        <div id="api-status" class="test-result info">Проверка...</div>
        <button onclick="checkApiStatus()">Проверить API</button>
    </div>

    <div class="test-section">
        <h2>💰 Текущий баланс</h2>
        <div id="balance-display" class="balance-display">Загрузка...</div>
        <button onclick="loadBalance()">Загрузить баланс</button>
        <button onclick="refreshBalance()">Обновить баланс</button>
    </div>

    <div class="test-section">
        <h2>📊 Детальная статистика</h2>
        <div id="balance-stats" class="stats-grid">
            <div class="stat-item">
                <div id="total-balance" class="stat-value">0</div>
                <div class="stat-label">Общий баланс</div>
            </div>
            <div class="stat-item">
                <div id="available-balance" class="stat-value">0</div>
                <div class="stat-label">Доступно</div>
            </div>
            <div class="stat-item">
                <div id="reserved-balance" class="stat-value">0</div>
                <div class="stat-label">Зарезервировано</div>
            </div>
            <div class="stat-item">
                <div id="total-earned" class="stat-value">0</div>
                <div class="stat-label">Всего заработано</div>
            </div>
            <div class="stat-item">
                <div id="total-withdrawn" class="stat-value">0</div>
                <div class="stat-label">Всего выведено</div>
            </div>
            <div class="stat-item">
                <div id="balance-usd" class="stat-value">$0.00</div>
                <div class="stat-label">Баланс в USD</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📈 Дневная статистика</h2>
        <div id="daily-stats" class="test-result info">Загрузка...</div>
    </div>

    <div class="test-section">
        <h2>🔧 Тесты функций</h2>
        <div id="function-tests" class="test-result info">Готов к тестированию</div>
        <button onclick="testBalanceManager()">Тест BalanceManager</button>
        <button onclick="testApiClient()">Тест ApiClient</button>
        <button onclick="testFormatting()">Тест форматирования</button>
    </div>

    <div class="test-section">
        <h2>📝 Лог операций</h2>
        <div id="operation-log" style="background: #111; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            Лог операций...<br>
        </div>
        <button onclick="clearLog()">Очистить лог</button>
    </div>

    <!-- Подключаем необходимые скрипты -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="js/balance-api-client.js"></script>
    <script src="js/balance-manager.js"></script>

    <script>
        // Эмуляция Telegram WebApp для тестирования
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1640995200&hash=test_hash',
                    initDataUnsafe: {
                        user: {
                            id: 5880288830,
                            first_name: 'Test User',
                            username: 'testuser'
                        }
                    },
                    ready: () => console.log('Telegram WebApp ready (emulated)'),
                    expand: () => console.log('Telegram WebApp expanded (emulated)')
                }
            };
        }

        // Инициализация
        let balanceManager = null;
        let apiClient = null;

        function log(message, type = 'info') {
            const logEl = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEl.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[Test] ${message}`);
        }

        function clearLog() {
            document.getElementById('operation-log').innerHTML = 'Лог очищен...<br>';
        }

        async function checkApiStatus() {
            const statusEl = document.getElementById('api-status');
            
            try {
                log('Проверка доступности SQLite API...', 'info');
                
                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const isAvailable = await window.balanceApiClient.checkSqliteApiAvailability();
                
                if (isAvailable) {
                    statusEl.className = 'test-result success';
                    statusEl.textContent = '✅ SQLite API доступен';
                    log('SQLite API доступен', 'success');
                } else {
                    statusEl.className = 'test-result error';
                    statusEl.textContent = '❌ SQLite API недоступен';
                    log('SQLite API недоступен', 'error');
                }

            } catch (error) {
                statusEl.className = 'test-result error';
                statusEl.textContent = `❌ Ошибка: ${error.message}`;
                log(`Ошибка проверки API: ${error.message}`, 'error');
            }
        }

        async function loadBalance() {
            try {
                log('Загрузка баланса...', 'info');
                
                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const data = await window.balanceApiClient.getUserBalance();
                
                if (data.success) {
                    updateBalanceDisplay(data);
                    log('Баланс успешно загружен', 'success');
                } else {
                    throw new Error('Сервер вернул ошибку');
                }

            } catch (error) {
                document.getElementById('balance-display').textContent = `Ошибка: ${error.message}`;
                log(`Ошибка загрузки баланса: ${error.message}`, 'error');
            }
        }

        function updateBalanceDisplay(data) {
            document.getElementById('balance-display').textContent = `${data.balance.toLocaleString()} монет`;
            document.getElementById('total-balance').textContent = data.balance.toLocaleString();
            document.getElementById('available-balance').textContent = data.available_balance.toLocaleString();
            document.getElementById('reserved-balance').textContent = data.reserved_balance.toLocaleString();
            document.getElementById('total-earned').textContent = data.total_earned.toLocaleString();
            document.getElementById('total-withdrawn').textContent = data.total_withdrawn.toLocaleString();
            document.getElementById('balance-usd').textContent = `$${data.balance_usd.toFixed(4)}`;

            // Дневная статистика
            const dailyStatsEl = document.getElementById('daily-stats');
            if (data.daily_stats) {
                const stats = data.daily_stats;
                dailyStatsEl.innerHTML = `
                    <strong>Заработано сегодня:</strong> ${stats.earned_today || 0} монет<br>
                    <strong>Дневной лимит:</strong> ${stats.limit || 0} монет<br>
                    <strong>Осталось:</strong> ${(stats.limit || 0) - (stats.earned_today || 0)} монет
                `;
                dailyStatsEl.className = 'test-result success';
            } else {
                dailyStatsEl.textContent = 'Дневная статистика недоступна';
                dailyStatsEl.className = 'test-result info';
            }
        }

        async function refreshBalance() {
            if (window.balanceManager && window.balanceManager.loadBalanceFromServer) {
                try {
                    log('Обновление баланса через BalanceManager...', 'info');
                    await window.balanceManager.loadBalanceFromServer();
                    log('Баланс обновлен через BalanceManager', 'success');
                } catch (error) {
                    log(`Ошибка обновления через BalanceManager: ${error.message}`, 'error');
                }
            } else {
                await loadBalance();
            }
        }

        function testBalanceManager() {
            const testEl = document.getElementById('function-tests');
            
            try {
                if (!window.balanceManager) {
                    window.balanceManager = new BalanceManager();
                }

                const tests = [
                    () => window.balanceManager.getCurrentBalance() >= 0,
                    () => typeof window.balanceManager.formatBalance === 'function',
                    () => typeof window.balanceManager.canWithdraw === 'function',
                    () => typeof window.balanceManager.loadBalanceFromServer === 'function'
                ];

                const results = tests.map(test => {
                    try {
                        return test();
                    } catch (e) {
                        return false;
                    }
                });

                const passed = results.filter(r => r).length;
                
                testEl.className = passed === tests.length ? 'test-result success' : 'test-result error';
                testEl.textContent = `BalanceManager: ${passed}/${tests.length} тестов пройдено`;
                
                log(`BalanceManager тесты: ${passed}/${tests.length} пройдено`, passed === tests.length ? 'success' : 'error');

            } catch (error) {
                testEl.className = 'test-result error';
                testEl.textContent = `Ошибка тестирования BalanceManager: ${error.message}`;
                log(`Ошибка тестирования BalanceManager: ${error.message}`, 'error');
            }
        }

        function testApiClient() {
            const testEl = document.getElementById('function-tests');
            
            try {
                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const tests = [
                    () => typeof window.balanceApiClient.getUserBalance === 'function',
                    () => typeof window.balanceApiClient.formatBalance === 'function',
                    () => typeof window.balanceApiClient.convertToUSD === 'function',
                    () => window.balanceApiClient.getUserDataForAPI() !== null
                ];

                const results = tests.map(test => {
                    try {
                        return test();
                    } catch (e) {
                        return false;
                    }
                });

                const passed = results.filter(r => r).length;
                
                testEl.className = passed === tests.length ? 'test-result success' : 'test-result error';
                testEl.textContent = `ApiClient: ${passed}/${tests.length} тестов пройдено`;
                
                log(`ApiClient тесты: ${passed}/${tests.length} пройдено`, passed === tests.length ? 'success' : 'error');

            } catch (error) {
                testEl.className = 'test-result error';
                testEl.textContent = `Ошибка тестирования ApiClient: ${error.message}`;
                log(`Ошибка тестирования ApiClient: ${error.message}`, 'error');
            }
        }

        function testFormatting() {
            const testEl = document.getElementById('function-tests');
            
            try {
                const tests = [
                    () => window.balanceApiClient.formatBalance(1000) === '1,000',
                    () => window.balanceApiClient.convertToUSD(1000, 0.001) === '1.0000',
                    () => window.balanceApiClient.canWithdraw(1000, 1500, 500) === true,
                    () => window.balanceApiClient.canWithdraw(2000, 1500, 500) === false
                ];

                const results = tests.map(test => {
                    try {
                        return test();
                    } catch (e) {
                        return false;
                    }
                });

                const passed = results.filter(r => r).length;
                
                testEl.className = passed === tests.length ? 'test-result success' : 'test-result error';
                testEl.textContent = `Форматирование: ${passed}/${tests.length} тестов пройдено`;
                
                log(`Тесты форматирования: ${passed}/${tests.length} пройдено`, passed === tests.length ? 'success' : 'error');

            } catch (error) {
                testEl.className = 'test-result error';
                testEl.textContent = `Ошибка тестирования форматирования: ${error.message}`;
                log(`Ошибка тестирования форматирования: ${error.message}`, 'error');
            }
        }

        // Автоматическая инициализация при загрузке
        window.addEventListener('load', () => {
            log('Страница загружена, начинаем тесты...', 'info');
            
            setTimeout(() => {
                checkApiStatus();
            }, 1000);
            
            setTimeout(() => {
                loadBalance();
            }, 2000);
        });
    </script>
</body>
</html>
