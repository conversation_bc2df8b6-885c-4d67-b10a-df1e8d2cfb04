<?php
/**
 * api/coins_manager.php
 * Менеджер системы монет - основной класс для работы с балансами и транзакциями
 * Использует существующую базу данных UniQPaid
 */

declare(strict_types=1);

class CoinsManager {
    private RealSQLiteManager $db;

    public function __construct() {
        require_once __DIR__ . '/../database/real_sqlite_manager.php';
        $this->db = new RealSQLiteManager();
        $this->initializeCoinsExtension();
    }

    /**
     * Инициализация расширений для системы монет
     */
    private function initializeCoinsExtension(): void {
        $extensionFile = __DIR__ . '/../database/coins_extension.sql';
        if (file_exists($extensionFile)) {
            $schema = file_get_contents($extensionFile);
            $statements = explode(';', $schema);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !str_starts_with($statement, '--')) {
                    try {
                        $this->db->query($statement);
                    } catch (Exception $e) {
                        // Игнорируем ошибки добавления колонок (если уже существуют)
                        if (!str_contains($e->getMessage(), 'duplicate column name')) {
                            error_log("Error executing coins extension: " . $e->getMessage());
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Получить баланс пользователя из существующей таблицы users
     */
    public function getUserBalance(string $userId): array {
        $result = $this->db->query(
            "SELECT balance, COALESCE(reserved_balance, 0) as reserved_balance, total_earned,
                    (SELECT COALESCE(SUM(amount), 0) FROM user_withdrawals WHERE user_id = ? AND status = 'completed') as total_withdrawn
             FROM users WHERE telegram_id = ?",
            [$userId, $userId]
        );

        if (empty($result)) {
            // Пользователь не найден, возвращаем нули
            return [
                'balance' => 0.0,
                'reserved_balance' => 0.0,
                'available_balance' => 0.0,
                'total_earned' => 0.0,
                'total_withdrawn' => 0.0
            ];
        }

        $data = $result[0];
        $balance = (float)$data['balance'];
        $reserved = (float)$data['reserved_balance'];

        return [
            'balance' => $balance,
            'reserved_balance' => $reserved,
            'available_balance' => $balance - $reserved,
            'total_earned' => (float)$data['total_earned'],
            'total_withdrawn' => (float)$data['total_withdrawn']
        ];
    }
    
    /**
     * Начислить монеты пользователю
     */
    public function creditCoins(
        string $userId,
        float $amount,
        string $transactionType = 'earn',
        string $sourceType = 'ad_view',
        ?int $sourceId = null,
        string $description = ''
    ): bool {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        try {
            $this->db->beginTransaction();

            // Получаем текущий баланс
            $currentBalance = $this->getUserBalance($userId);
            $balanceBefore = $currentBalance['balance'];
            $balanceAfter = $balanceBefore + $amount;

            // Обновляем баланс в таблице users
            $this->db->query(
                "UPDATE users SET balance = ?, total_earned = total_earned + ? WHERE telegram_id = ?",
                [$balanceAfter, $amount, $userId]
            );

            // Записываем транзакцию
            $this->recordTransactionInternal(
                $userId,
                $transactionType,
                $amount,
                'credit',
                $balanceBefore,
                $balanceAfter,
                $sourceType,
                $sourceId,
                $description
            );

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error crediting coins: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Списать монеты с баланса пользователя
     */
    public function debitCoins(
        string $userId,
        float $amount,
        string $transactionType = 'withdraw',
        string $sourceType = 'withdrawal',
        ?int $sourceId = null,
        string $description = ''
    ): bool {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        try {
            $this->db->beginTransaction();

            // Проверяем баланс
            $currentBalance = $this->getUserBalance($userId);
            if ($currentBalance['available_balance'] < $amount) {
                throw new Exception('Insufficient balance');
            }

            $balanceBefore = $currentBalance['balance'];
            $balanceAfter = $balanceBefore - $amount;

            // Обновляем баланс в таблице users
            $this->db->query(
                "UPDATE users SET balance = ? WHERE telegram_id = ?",
                [$balanceAfter, $userId]
            );

            // Записываем транзакцию
            $this->recordTransactionInternal(
                $userId,
                $transactionType,
                $amount,
                'debit',
                $balanceBefore,
                $balanceAfter,
                $sourceType,
                $sourceId,
                $description
            );

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error debiting coins: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Зарезервировать средства для вывода
     */
    public function reserveCoins(string $userId, float $amount, int $withdrawalId): bool {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        try {
            $this->db->beginTransaction();

            // Проверяем доступный баланс
            $currentBalance = $this->getUserBalance($userId);
            if ($currentBalance['available_balance'] < $amount) {
                throw new Exception('Insufficient available balance');
            }

            // Резервируем средства в таблице users
            $this->db->query(
                "UPDATE users SET reserved_balance = COALESCE(reserved_balance, 0) + ? WHERE telegram_id = ?",
                [$amount, $userId]
            );

            // Записываем транзакцию резервирования
            $this->recordTransactionInternal(
                $userId,
                'reserve',
                $amount,
                'debit',
                $currentBalance['balance'],
                $currentBalance['balance'], // баланс не меняется, только резерв
                'withdrawal',
                $withdrawalId,
                "Reserved for withdrawal #{$withdrawalId}"
            );

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error reserving coins: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Снять резерв средств
     */
    public function unreserveCoins(string $userId, float $amount, int $withdrawalId): bool {
        try {
            $this->db->beginTransaction();

            $currentBalance = $this->getUserBalance($userId);

            // Снимаем резерв в таблице users
            $this->db->query(
                "UPDATE users SET reserved_balance = COALESCE(reserved_balance, 0) - ?
                 WHERE telegram_id = ? AND COALESCE(reserved_balance, 0) >= ?",
                [$amount, $userId, $amount]
            );

            // Проверяем что операция прошла успешно
            if ($this->db->getAffectedRows() === 0) {
                throw new Exception('Insufficient reserved balance');
            }

            // Записываем транзакцию снятия резерва
            $this->recordTransactionInternal(
                $userId,
                'unreserve',
                $amount,
                'credit',
                $currentBalance['balance'],
                $currentBalance['balance'], // баланс не меняется, только резерв
                'withdrawal',
                $withdrawalId,
                "Unreserved from withdrawal #{$withdrawalId}"
            );

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error unreserving coins: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Получить историю транзакций пользователя
     */
    public function getUserTransactions(string $userId, int $limit = 50, int $offset = 0): array {
        return $this->db->query(
            "SELECT * FROM coin_transactions 
             WHERE user_id = ? 
             ORDER BY created_at DESC 
             LIMIT ? OFFSET ?",
            [$userId, $limit, $offset]
        );
    }
    
    /**
     * Получить настройку системы
     */
    public function getSetting(string $key, $default = null) {
        $result = $this->db->query(
            "SELECT setting_value FROM coin_settings WHERE setting_key = ?",
            [$key]
        );

        return empty($result) ? $default : $result[0]['setting_value'];
    }

    /**
     * Установить настройку системы
     */
    public function setSetting(string $key, string $value, string $description = ''): void {
        $this->db->query(
            "INSERT OR REPLACE INTO coin_settings (setting_key, setting_value, description, updated_at)
             VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
            [$key, $value, $description]
        );
    }

    /**
     * Получить награду за тип рекламы
     */
    public function getAdReward(string $adType): float {
        $settingKey = "ad_reward_{$adType}";
        $reward = $this->getSetting($settingKey, '0');
        return (float)$reward;
    }

    /**
     * Проверить дневной лимит заработка
     */
    public function checkDailyEarnLimit(string $userId): array {
        $dailyLimit = (float)$this->getSetting('daily_earn_limit', '200');

        $result = $this->db->query(
            "SELECT COALESCE(coins_earned_today, 0) as earned_today
             FROM user_daily_stats
             WHERE user_id = ? AND date = date('now')",
            [$userId]
        );

        $earnedToday = empty($result) ? 0 : (float)$result[0]['earned_today'];

        return [
            'daily_limit' => $dailyLimit,
            'earned_today' => $earnedToday,
            'remaining' => max(0, $dailyLimit - $earnedToday),
            'can_earn' => $earnedToday < $dailyLimit
        ];
    }
    
    /**
     * Записать транзакцию (публичный метод)
     */
    public function recordTransaction(
        string $userId,
        string $transactionType,
        float $amount,
        string $operation,
        float $balanceBefore,
        float $balanceAfter,
        ?string $sourceType = null,
        ?int $sourceId = null,
        string $description = '',
        ?array $metadata = null
    ): void {
        $this->recordTransactionInternal(
            $userId,
            $transactionType,
            $amount,
            $operation,
            $balanceBefore,
            $balanceAfter,
            $sourceType,
            $sourceId,
            $description,
            $metadata
        );
    }

    /**
     * Записать транзакцию (внутренний метод)
     */
    private function recordTransactionInternal(
        string $userId,
        string $transactionType,
        float $amount,
        string $operation,
        float $balanceBefore,
        float $balanceAfter,
        ?string $sourceType = null,
        ?int $sourceId = null,
        string $description = '',
        ?array $metadata = null
    ): void {
        $this->db->query(
            "INSERT INTO coin_transactions
             (user_id, transaction_type, amount, operation, balance_before, balance_after,
              source_type, source_id, description, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $userId,
                $transactionType,
                $amount,
                $operation,
                $balanceBefore,
                $balanceAfter,
                $sourceType,
                $sourceId,
                $description,
                $metadata ? json_encode($metadata) : null
            ]
        );
    }
}
?>
