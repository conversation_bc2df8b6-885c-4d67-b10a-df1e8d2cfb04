<?php
/**
 * test_admin_quick.php
 * Быстрая проверка админки
 */

declare(strict_types=1);

echo "🔍 БЫСТРАЯ ПРОВЕРКА АДМИНКИ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Проверяем основные файлы
$adminFiles = [
    'api/admin/index.php' => 'Главная страница',
    'api/admin/users.php' => 'Пользователи',
    'api/admin/stats.php' => 'Статистика',
    'api/admin/withdrawals.php' => 'Выводы средств',
    'api/admin/balance.php' => 'Баланс',
    'api/admin/monitor.php' => 'Мониторинг',
    'api/admin/ad_statistics.php' => 'Статистика рекламы'
];

$okCount = 0;
$errorCount = 0;

foreach ($adminFiles as $file => $description) {
    echo "📁 Проверяем: {$description}\n";
    
    if (!file_exists($file)) {
        echo "   ❌ Файл не найден!\n";
        $errorCount++;
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Проверяем использование SQLite
    if (strpos($content, 'db_mock_final_sqlite.php') !== false) {
        echo "   ✅ Использует SQLite API\n";
    } elseif (strpos($content, 'db_mock.php') !== false) {
        echo "   ⚠️ Использует старый JSON API\n";
    } else {
        echo "   ℹ️ Не использует базу данных\n";
    }
    
    // Проверяем отключение вывода ошибок
    if (strpos($content, "ini_set('display_errors', 0)") !== false) {
        echo "   ✅ Ошибки отключены\n";
    } else {
        echo "   ⚠️ Ошибки могут выводиться в браузер\n";
    }
    
    $okCount++;
    echo "\n";
}

echo "📊 РЕЗУЛЬТАТЫ:\n";
echo "  ✅ Проверено файлов: {$okCount}\n";
echo "  ❌ Ошибок: {$errorCount}\n\n";

// Проверяем ключевые файлы системы
echo "🔧 ПРОВЕРКА СИСТЕМНЫХ ФАЙЛОВ:\n";

$systemFiles = [
    'api/db_mock_final_sqlite.php' => 'SQLite API',
    'database/app.sqlite' => 'SQLite база данных',
    'database/bot_settings.json' => 'Настройки бота',
    'api/config.php' => 'Конфигурация'
];

foreach ($systemFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "  ✅ {$description}: " . number_format($size) . " bytes\n";
    } else {
        echo "  ❌ ОТСУТСТВУЕТ: {$description}\n";
    }
}

echo "\n🎯 ГОТОВО!\n";
?>
