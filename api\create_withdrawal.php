<?php
/**
 * api/create_withdrawal.php
 * API для создания заявки на вывод средств
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    $requiredFields = ['initData', 'amount', 'currency', 'wallet_address'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $initData = $input['initData'];
    $amount = (float)$input['amount'];
    $currency = strtoupper(trim($input['currency']));
    $walletAddress = trim($input['wallet_address']);
    $notes = $input['notes'] ?? '';
    
    // Валидируем сумму
    if ($amount <= 0) {
        throw new Exception('Amount must be positive');
    }
    
    // Валидируем валюту
    $supportedCurrencies = ['USDT', 'BTC', 'ETH', 'TRX', 'TON'];
    if (!in_array($currency, $supportedCurrencies)) {
        throw new Exception('Unsupported currency. Supported: ' . implode(', ', $supportedCurrencies));
    }
    
    // Парсим initData для получения user_id
    require_once __DIR__ . '/../api/telegram_auth.php';
    $telegramAuth = new TelegramAuth();
    $userData = $telegramAuth->validateInitData($initData);
    
    if (!$userData || !isset($userData['user']['id'])) {
        throw new Exception('Invalid Telegram data');
    }
    
    $userId = (string)$userData['user']['id'];
    
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Валидируем адрес кошелька
    $walletValidation = validateWalletAddress($walletAddress, $currency);
    if (!$walletValidation['valid']) {
        throw new Exception('Invalid wallet address: ' . $walletValidation['error']);
    }
    
    // Получаем настройки системы
    $minWithdrawal = (float)$coinsManager->getSetting('min_withdrawal_coins', '1000');
    $coinRate = (float)$coinsManager->getSetting('coin_rate_usd', '0.001');
    $withdrawalFeePercent = (float)$coinsManager->getSetting('withdrawal_fee_percent', '0');
    
    // Проверяем минимальную сумму
    if ($amount < $minWithdrawal) {
        throw new Exception("Minimum withdrawal amount is {$minWithdrawal} coins");
    }
    
    // Получаем баланс пользователя
    $balance = $coinsManager->getUserBalance($userId);
    
    if ($balance['available_balance'] < $amount) {
        throw new Exception('Insufficient balance. Available: ' . $balance['available_balance'] . ' coins');
    }
    
    // Проверяем лимиты на вывод
    $withdrawalLimits = checkWithdrawalLimits($userId, $amount, $currency, $db);
    if (!$withdrawalLimits['allowed']) {
        throw new Exception($withdrawalLimits['error']);
    }
    
    // Рассчитываем комиссию и итоговую сумму
    $fee = $withdrawalFeePercent > 0 ? ($amount * $withdrawalFeePercent / 100) : 0;
    $finalAmount = $amount - $fee;
    
    // Конвертируем в криптовалюту
    $cryptoAmount = convertCoinsToFiat($finalAmount, $coinRate, $currency);
    $networkFee = getNetworkFee($currency);
    $finalCryptoAmount = $cryptoAmount - $networkFee;
    
    if ($finalCryptoAmount <= 0) {
        throw new Exception('Amount too small after fees');
    }
    
    try {
        $db->beginTransaction();
        
        // Резервируем средства
        $reserveSuccess = $coinsManager->reserveCoins($userId, $amount, 0); // Временный ID
        
        if (!$reserveSuccess) {
            throw new Exception('Failed to reserve coins');
        }
        
        // Создаем заявку на вывод
        $withdrawalId = $db->query(
            "INSERT INTO user_withdrawals 
             (user_id, amount, currency, wallet_address, status, crypto_amount, network_fee, final_amount, admin_notes, requested_at) 
             VALUES (?, ?, ?, ?, 'pending', ?, ?, ?, ?, CURRENT_TIMESTAMP)",
            [
                $userId,
                $amount,
                $currency,
                $walletAddress,
                number_format($cryptoAmount, 8),
                number_format($networkFee, 8),
                number_format($finalCryptoAmount, 8),
                $notes
            ]
        );
        
        // Получаем ID созданной заявки
        $withdrawalId = $db->query("SELECT last_insert_rowid() as id")[0]['id'];
        
        // Обновляем резервирование с правильным ID
        $db->query(
            "UPDATE coin_transactions SET source_id = ?, description = ? 
             WHERE user_id = ? AND transaction_type = 'reserve' AND source_id = 0 
             ORDER BY created_at DESC LIMIT 1",
            [$withdrawalId, "Reserved for withdrawal #{$withdrawalId}", $userId]
        );
        
        // Записываем транзакцию создания заявки
        $coinsManager->recordTransaction(
            $userId,
            'withdraw_request',
            $amount,
            'debit',
            $balance['balance'],
            $balance['balance'], // Баланс не меняется, только резерв
            'withdrawal',
            $withdrawalId,
            "Withdrawal request #{$withdrawalId} for {$finalCryptoAmount} {$currency}",
            [
                'withdrawal_id' => $withdrawalId,
                'currency' => $currency,
                'wallet_address' => $walletAddress,
                'crypto_amount' => $finalCryptoAmount,
                'network_fee' => $networkFee,
                'fee_percent' => $withdrawalFeePercent
            ]
        );
        
        // Обновляем счетчик выводов пользователя
        $db->query(
            "UPDATE users SET withdrawals_count = withdrawals_count + 1 WHERE telegram_id = ?",
            [$userId]
        );
        
        $db->commit();
        
        // Получаем обновленный баланс
        $newBalance = $coinsManager->getUserBalance($userId);
        
        // Отправляем уведомление администраторам (если настроено)
        notifyAdminsAboutWithdrawal($withdrawalId, $userId, $amount, $currency, $walletAddress);
        
        // Возвращаем успешный ответ
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request created successfully',
            'data' => [
                'withdrawal_id' => (int)$withdrawalId,
                'amount' => $amount,
                'currency' => $currency,
                'wallet_address' => $walletAddress,
                'crypto_amount' => $finalCryptoAmount,
                'network_fee' => $networkFee,
                'fee_percent' => $withdrawalFeePercent,
                'status' => 'pending',
                'estimated_processing_time' => '24-48 hours',
                'balance' => [
                    'current' => $newBalance['balance'],
                    'available' => $newBalance['available_balance'],
                    'reserved' => $newBalance['reserved_balance']
                ]
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'CREATE_WITHDRAWAL_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Валидация адреса кошелька
 */
function validateWalletAddress(string $address, string $currency): array {
    $address = trim($address);
    
    switch ($currency) {
        case 'USDT':
        case 'TRX':
            // TRON адреса начинаются с T и имеют длину 34 символа
            if (preg_match('/^T[A-Za-z0-9]{33}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid TRON address format'];
            
        case 'BTC':
            // Bitcoin адреса (Legacy, SegWit, Bech32)
            if (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) || // Legacy
                preg_match('/^3[a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||   // SegWit
                preg_match('/^bc1[a-z0-9]{39,59}$/', $address)) {             // Bech32
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid Bitcoin address format'];
            
        case 'ETH':
            // Ethereum адреса начинаются с 0x и имеют длину 42 символа
            if (preg_match('/^0x[a-fA-F0-9]{40}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid Ethereum address format'];
            
        case 'TON':
            // TON адреса (упрощенная проверка)
            if (preg_match('/^[A-Za-z0-9_-]{48}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid TON address format'];
            
        default:
            return ['valid' => false, 'error' => 'Unsupported currency'];
    }
}

/**
 * Проверка лимитов на вывод
 */
function checkWithdrawalLimits(string $userId, float $amount, string $currency, RealSQLiteManager $db): array {
    // Проверяем дневной лимит
    $dailyWithdrawals = $db->query(
        "SELECT COALESCE(SUM(amount), 0) as total 
         FROM user_withdrawals 
         WHERE user_id = ? AND date(requested_at) = date('now') AND status != 'cancelled'",
        [$userId]
    )[0]['total'];
    
    $dailyLimit = 10000; // 10000 монет в день
    if (($dailyWithdrawals + $amount) > $dailyLimit) {
        return [
            'allowed' => false,
            'error' => "Daily withdrawal limit exceeded. Used: {$dailyWithdrawals}/{$dailyLimit} coins"
        ];
    }
    
    // Проверяем количество заявок в день
    $dailyRequestsCount = $db->query(
        "SELECT COUNT(*) as count 
         FROM user_withdrawals 
         WHERE user_id = ? AND date(requested_at) = date('now')",
        [$userId]
    )[0]['count'];
    
    $maxDailyRequests = 3;
    if ($dailyRequestsCount >= $maxDailyRequests) {
        return [
            'allowed' => false,
            'error' => "Maximum {$maxDailyRequests} withdrawal requests per day"
        ];
    }
    
    // Проверяем есть ли незавершенные заявки
    $pendingWithdrawals = $db->query(
        "SELECT COUNT(*) as count 
         FROM user_withdrawals 
         WHERE user_id = ? AND status = 'pending'",
        [$userId]
    )[0]['count'];
    
    $maxPendingRequests = 2;
    if ($pendingWithdrawals >= $maxPendingRequests) {
        return [
            'allowed' => false,
            'error' => "Maximum {$maxPendingRequests} pending withdrawal requests allowed"
        ];
    }
    
    return ['allowed' => true];
}

/**
 * Конвертация монет в фиатную валюту
 */
function convertCoinsToFiat(float $coins, float $coinRate, string $currency): float {
    $usdAmount = $coins * $coinRate;
    
    // Курсы криптовалют к USD (в реальном проекте получать из API)
    $rates = [
        'USDT' => 1.0,
        'BTC' => 30000.0,
        'ETH' => 2000.0,
        'TRX' => 0.08,
        'TON' => 2.5
    ];
    
    if (!isset($rates[$currency])) {
        throw new Exception('Currency rate not found');
    }
    
    return $usdAmount / $rates[$currency];
}

/**
 * Получение сетевой комиссии
 */
function getNetworkFee(string $currency): float {
    $fees = [
        'USDT' => 1.0,   // 1 USDT
        'BTC' => 0.0001, // 0.0001 BTC
        'ETH' => 0.001,  // 0.001 ETH
        'TRX' => 0.0,    // Бесплатно
        'TON' => 0.01    // 0.01 TON
    ];
    
    return $fees[$currency] ?? 0.0;
}

/**
 * Уведомление администраторов
 */
function notifyAdminsAboutWithdrawal(int $withdrawalId, string $userId, float $amount, string $currency, string $walletAddress): void {
    // В реальном проекте здесь будет отправка уведомлений
    error_log("New withdrawal request #{$withdrawalId}: User {$userId}, {$amount} coins to {$currency} address {$walletAddress}");
}
?>
