<?php
/**
 * check_ad_stats.php
 * Проверка статистики рекламы в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "📊 ПРОВЕРКА СТАТИСТИКИ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Проверяем просмотры рекламы
    echo "👁️ ПРОСМОТРЫ РЕКЛАМЫ:\n";
    $views = $sqlite->query('SELECT COUNT(*) as count FROM ad_views');
    echo "  Всего просмотров: " . $views[0]['count'] . "\n";
    
    $viewsByType = $sqlite->query('SELECT ad_type, COUNT(*) as count FROM ad_views GROUP BY ad_type ORDER BY count DESC');
    foreach ($viewsByType as $view) {
        echo "  - {$view['ad_type']}: {$view['count']} просмотров\n";
    }
    
    // Проверяем клики по рекламе
    echo "\n🖱️ КЛИКИ ПО РЕКЛАМЕ:\n";
    $clicks = $sqlite->query('SELECT COUNT(*) as count FROM ad_clicks');
    echo "  Всего кликов: " . $clicks[0]['count'] . "\n";
    
    $clicksByType = $sqlite->query('SELECT ad_type, COUNT(*) as count FROM ad_clicks GROUP BY ad_type ORDER BY count DESC');
    foreach ($clicksByType as $click) {
        echo "  - {$click['ad_type']}: {$click['count']} кликов\n";
    }
    
    // CTR по типам рекламы
    echo "\n📈 CTR ПО ТИПАМ РЕКЛАМЫ:\n";
    $ctrStats = $sqlite->query("
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COUNT(c.id) as clicks,
            ROUND((COUNT(c.id) * 100.0 / COUNT(v.id)), 2) as ctr
        FROM ad_views v
        LEFT JOIN ad_clicks c ON v.ad_type = c.ad_type AND v.user_id = c.user_id
        GROUP BY v.ad_type
        ORDER BY ctr DESC
    ");
    
    foreach ($ctrStats as $stat) {
        echo "  - {$stat['ad_type']}: {$stat['views']} просмотров, {$stat['clicks']} кликов, CTR: {$stat['ctr']}%\n";
    }
    
    // Статистика по странам (если есть IP данные)
    echo "\n🌍 СТАТИСТИКА ПО IP:\n";
    $ipStats = $sqlite->query("
        SELECT 
            CASE 
                WHEN ip_address IS NULL OR ip_address = '' THEN 'Неизвестно'
                ELSE ip_address
            END as ip_group,
            COUNT(*) as count
        FROM ad_views 
        GROUP BY ip_group 
        ORDER BY count DESC 
        LIMIT 10
    ");
    
    foreach ($ipStats as $ip) {
        echo "  - {$ip['ip_group']}: {$ip['count']} просмотров\n";
    }
    
    // Последние просмотры
    echo "\n🕒 ПОСЛЕДНИЕ ПРОСМОТРЫ:\n";
    $recent = $sqlite->query("
        SELECT user_id, ad_type, reward, timestamp, ip_address
        FROM ad_views 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    foreach ($recent as $r) {
        echo "  - User {$r['user_id']}: {$r['ad_type']}, награда {$r['reward']}, {$r['timestamp']}\n";
    }
    
    // Последние клики
    echo "\n🖱️ ПОСЛЕДНИЕ КЛИКИ:\n";
    $recentClicks = $sqlite->query("
        SELECT user_id, ad_type, click_type, timestamp, ip_address
        FROM ad_clicks 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    foreach ($recentClicks as $c) {
        echo "  - User {$c['user_id']}: {$c['ad_type']} ({$c['click_type']}), {$c['timestamp']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
