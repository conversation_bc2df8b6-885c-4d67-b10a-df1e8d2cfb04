<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Миграция данных в SQLite</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #000;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #00ffff;
        }
        .output {
            background: #111;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #333;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.4;
            max-height: 600px;
            overflow-y: auto;
        }
        .button {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            margin: 10px;
        }
        .button:hover {
            background: #00cc00;
        }
        .button.danger {
            background: #ff0000;
            color: #fff;
        }
        .button.danger:hover {
            background: #cc0000;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #004400;
            border: 1px solid #00ff00;
        }
        .status.error {
            background: #440000;
            border: 1px solid #ff0000;
            color: #ff0000;
        }
        .status.info {
            background: #004444;
            border: 1px solid #00ffff;
            color: #00ffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 МИГРАЦИЯ ДАННЫХ В SQLITE</h1>
            <p>Перенос актуальных данных из JSON файлов в SQLite базу данных</p>
        </div>

        <?php if (!isset($_GET['action'])): ?>
            <div class="status info">
                <h3>📋 Что будет мигрировано:</h3>
                <ul>
                    <li><strong>user_data.json</strong> → таблица users</li>
                    <li><strong>ad_views.json</strong> → таблица coin_transactions</li>
                    <li><strong>ad_clicks.json</strong> → таблица ad_clicks</li>
                    <li><strong>Тестовые выплаты</strong> → таблица user_withdrawals</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="?action=migrate" class="button">
                    ▶️ ЗАПУСТИТЬ МИГРАЦИЮ
                </a>
                <a href="?action=check" class="button">
                    📊 ПРОВЕРИТЬ ДАННЫЕ
                </a>
            </div>

            <div class="status info">
                <h3>⚠️ ВНИМАНИЕ:</h3>
                <p>Миграция обновит существующих пользователей и добавит новые транзакции.</p>
                <p>Дубликаты будут автоматически пропущены.</p>
            </div>

        <?php elseif ($_GET['action'] === 'migrate'): ?>
            <div class="status info">
                <h3>🔄 Выполняется миграция...</h3>
            </div>

            <div class="output">
<?php
            // Запускаем миграцию
            ob_start();
            include 'migrate_full_data.php';
            $output = ob_get_clean();
            echo htmlspecialchars($output);
?>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <a href="demo_withdrawal_history.html" class="button">
                    📋 ТЕСТИРОВАТЬ ВЫПЛАТЫ
                </a>
                <a href="?" class="button">
                    🔙 НАЗАД
                </a>
            </div>

        <?php elseif ($_GET['action'] === 'check'): ?>
            <div class="status info">
                <h3>📊 Проверка текущих данных...</h3>
            </div>

            <div class="output">
<?php
            try {
                require_once 'database/real_sqlite_manager.php';
                $db = new RealSQLiteManager();
                
                echo "📊 ТЕКУЩЕЕ СОСТОЯНИЕ БАЗЫ ДАННЫХ\n";
                echo "================================\n\n";
                
                // Проверяем пользователей
                $users = $db->query("SELECT COUNT(*) as count FROM users")[0]['count'];
                $usersWithBalance = $db->query("SELECT COUNT(*) as count FROM users WHERE balance > 0")[0]['count'];
                $totalBalance = $db->query("SELECT SUM(balance) as total FROM users")[0]['total'] ?? 0;
                
                echo "👥 ПОЛЬЗОВАТЕЛИ:\n";
                echo "   - Всего: {$users}\n";
                echo "   - С балансом > 0: {$usersWithBalance}\n";
                echo "   - Общий баланс: " . number_format($totalBalance, 2) . " монет\n\n";
                
                // Проверяем транзакции
                $transactions = $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'];
                $adRewards = $db->query("SELECT COUNT(*) as count FROM coin_transactions WHERE transaction_type = 'ad_reward'")[0]['count'];
                $totalRewards = $db->query("SELECT SUM(amount) as total FROM coin_transactions WHERE transaction_type = 'ad_reward'")[0]['total'] ?? 0;
                
                echo "💰 ТРАНЗАКЦИИ:\n";
                echo "   - Всего: {$transactions}\n";
                echo "   - Награды за рекламу: {$adRewards}\n";
                echo "   - Сумма наград: " . number_format($totalRewards, 2) . " монет\n\n";
                
                // Проверяем клики
                $clicks = $db->query("SELECT COUNT(*) as count FROM ad_clicks")[0]['count'] ?? 0;
                if ($clicks > 0) {
                    $clickTypes = $db->query("SELECT click_type, COUNT(*) as count FROM ad_clicks GROUP BY click_type");
                    echo "🖱️  КЛИКИ ПО РЕКЛАМЕ:\n";
                    echo "   - Всего: {$clicks}\n";
                    foreach ($clickTypes as $type) {
                        echo "   - {$type['click_type']}: {$type['count']}\n";
                    }
                    echo "\n";
                }
                
                // Проверяем выплаты
                $withdrawals = $db->query("SELECT COUNT(*) as count FROM user_withdrawals")[0]['count'] ?? 0;
                if ($withdrawals > 0) {
                    $withdrawalStats = $db->query("SELECT status, COUNT(*) as count, SUM(amount) as total FROM user_withdrawals GROUP BY status");
                    echo "💸 ВЫПЛАТЫ:\n";
                    echo "   - Всего: {$withdrawals}\n";
                    foreach ($withdrawalStats as $stat) {
                        echo "   - {$stat['status']}: {$stat['count']} шт. ({$stat['total']} TON)\n";
                    }
                    echo "\n";
                }
                
                // Проверяем JSON файлы
                echo "📁 JSON ФАЙЛЫ:\n";
                $files = ['user_data.json', 'ad_views.json', 'ad_clicks.json'];
                foreach ($files as $file) {
                    $path = "database/{$file}";
                    if (file_exists($path)) {
                        $data = json_decode(file_get_contents($path), true);
                        $count = is_array($data) ? count($data) : 0;
                        echo "   - {$file}: {$count} записей\n";
                    } else {
                        echo "   - {$file}: НЕ НАЙДЕН\n";
                    }
                }
                
                echo "\n✅ Проверка завершена\n";
                
            } catch (Exception $e) {
                echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
            }
?>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <a href="?action=migrate" class="button">
                    🚀 ЗАПУСТИТЬ МИГРАЦИЮ
                </a>
                <a href="?" class="button">
                    🔙 НАЗАД
                </a>
            </div>

        <?php endif; ?>

        <div class="status info" style="margin-top: 30px;">
            <h3>🔗 Полезные ссылки после миграции:</h3>
            <ul>
                <li><a href="demo_withdrawal_history.html" style="color: #00ffff;">📋 Тест истории выплат</a></li>
                <li><a href="test_ad_reward_integration.html" style="color: #00ffff;">📺 Тест начислений за рекламу</a></li>
                <li><a href="demo_admin_rewards.html" style="color: #00ffff;">⚙️ Демо настроек админки</a></li>
                <li><a href="demo_simple_balance.html" style="color: #00ffff;">💰 Демо упрощенного баланса</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
