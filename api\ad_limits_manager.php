<?php
/**
 * api/ad_limits_manager_unified.php
 * Унифицированная версия менеджера лимитов с использованием единой JSON структуры
 */

declare(strict_types=1);

require_once __DIR__ . "/../database/unified_json_manager.php";

class AdLimitsManagerUnified 
{
    private UnifiedJsonManager $manager;
    private array $configLimits;
    
    public function __construct() 
    {
        $this->manager = UnifiedJsonManager::getInstance();
        $this->configLimits = $this->getConfigLimits();
    }
    
    private function getConfigLimits(): array 
    {
        return [
            "native_banner" => defined("USER_AD_LIMIT_NATIVE_BANNER") ? USER_AD_LIMIT_NATIVE_BANNER : 20,
            "rewarded_video" => defined("USER_AD_LIMIT_REWARDED_VIDEO") ? USER_AD_LIMIT_REWARDED_VIDEO : 20,
            "interstitial" => defined("USER_AD_LIMIT_INTERSTITIAL") ? USER_AD_LIMIT_INTERSTITIAL : 20
        ];
    }
    
    public function getUserAdCount(int $userId, string $adType): int 
    {
        $limits = $this->manager->getUserAdLimits($userId);
        return $limits[$adType] ?? 0;
    }
    
    public function incrementUserAdCount(int $userId, string $adType): int 
    {
        $currentCount = $this->getUserAdCount($userId, $adType);
        $newCount = $currentCount + 1;
        
        $this->manager->updateUserAdLimit($userId, $adType, $newCount);
        
        return $newCount;
    }
    
    public function getRemainingCount(int $userId, string $adType): int 
    {
        $currentCount = $this->getUserAdCount($userId, $adType);
        $limit = $this->configLimits[$adType] ?? 20;
        
        return max(0, $limit - $currentCount);
    }
    
    public function isLimitReached(int $userId, string $adType): bool 
    {
        $currentCount = $this->getUserAdCount($userId, $adType);
        $limit = $this->configLimits[$adType] ?? 20;
        
        return $currentCount >= $limit;
    }
    
    public function getConfigLimits(): array 
    {
        return $this->configLimits;
    }
}
?>