<?php
/**
 * test_fixed_ad_stats.php
 * Тест исправленной статистики рекламы
 */

declare(strict_types=1);

echo "🔧 ТЕСТ ИСПРАВЛЕННОЙ СТАТИСТИКИ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ПРОВЕРКА ИСКЛЮЧЕНИЯ test_banner:\n";
    
    // Всего данных
    $allViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views");
    $allClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks");
    
    echo "   📊 Всего просмотров: " . $allViews[0]['count'] . "\n";
    echo "   📊 Всего кликов: " . $allClicks[0]['count'] . "\n";
    
    // Данные test_banner
    $testViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ad_type = 'test_banner'");
    $testClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ad_type = 'test_banner'");
    
    echo "   🧪 test_banner просмотров: " . $testViews[0]['count'] . "\n";
    echo "   🧪 test_banner кликов: " . $testClicks[0]['count'] . "\n";
    
    // Данные без test_banner (как в API)
    $filteredViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ad_type != 'test_banner'");
    $filteredClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ad_type != 'test_banner'");
    
    echo "   ✅ Без test_banner просмотров: " . $filteredViews[0]['count'] . "\n";
    echo "   ✅ Без test_banner кликов: " . $filteredClicks[0]['count'] . "\n";
    
    echo "\n2. 📈 ПРОВЕРКА CTR РАСЧЕТА:\n";
    
    // Статистика по типам (как в API)
    $statsQuery = "
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COALESCE(c.clicks, 0) as clicks,
            CASE 
                WHEN COUNT(v.id) > 0 THEN ROUND((COALESCE(c.clicks, 0) * 100.0 / COUNT(v.id)), 2)
                ELSE 0 
            END as ctr
        FROM ad_views v
        LEFT JOIN (
            SELECT ad_type, COUNT(*) as clicks 
            FROM ad_clicks 
            WHERE ad_type != 'test_banner'
            GROUP BY ad_type
        ) c ON v.ad_type = c.ad_type
        WHERE v.ad_type != 'test_banner'
        GROUP BY v.ad_type
        ORDER BY views DESC
    ";
    
    $statsResults = $sqlite->query($statsQuery);
    
    foreach ($statsResults as $row) {
        $manualCtr = $row['views'] > 0 ? round(($row['clicks'] / $row['views']) * 100, 2) : 0;
        echo "   - {$row['ad_type']}:\n";
        echo "     👁️ Просмотры: {$row['views']}\n";
        echo "     🖱️ Клики: {$row['clicks']}\n";
        echo "     📈 CTR (SQL): {$row['ctr']}%\n";
        echo "     📈 CTR (ручной): {$manualCtr}%\n";
        echo "     " . ($row['ctr'] == $manualCtr ? "✅ Совпадает" : "❌ Не совпадает") . "\n";
    }
    
    echo "\n3. 🌍 ПРОВЕРКА СТАТИСТИКИ ПО СТРАНАМ:\n";
    
    // IP адреса с кликами
    $ipQuery = "
        SELECT 
            ip_address,
            COUNT(*) as clicks
        FROM ad_clicks 
        WHERE ip_address IS NOT NULL AND ip_address != '' AND ad_type != 'test_banner'
        GROUP BY ip_address
        ORDER BY clicks DESC
        LIMIT 10
    ";
    
    $ipResults = $sqlite->query($ipQuery);
    
    echo "   📊 Найдено уникальных IP: " . count($ipResults) . "\n";
    
    // Простая функция для тестирования (без внешних API)
    function getTestCountryCode($ip) {
        if (strpos($ip, '127.') === 0 || strpos($ip, '192.168.') === 0) {
            return 'LOCAL';
        } elseif (strpos($ip, '103.') === 0) {
            return 'AS';
        } elseif (strpos($ip, '89.') === 0 || strpos($ip, '217.') === 0) {
            return 'EU';
        } elseif (strpos($ip, '66.') === 0 || strpos($ip, '148.') === 0) {
            return 'US';
        } else {
            return 'XX';
        }
    }
    
    $countryStats = [];
    foreach ($ipResults as $row) {
        $country = getTestCountryCode($row['ip_address']);
        if (!isset($countryStats[$country])) {
            $countryStats[$country] = 0;
        }
        $countryStats[$country] += (int)$row['clicks'];
        
        echo "   - {$row['ip_address']} ({$country}): {$row['clicks']} кликов\n";
    }
    
    arsort($countryStats);
    
    echo "\n   🌍 Группировка по странам:\n";
    foreach ($countryStats as $country => $clicks) {
        echo "     - {$country}: {$clicks} кликов\n";
    }
    
    echo "\n4. 📊 ПРОВЕРКА ДАННЫХ ГЛАВНОЙ СТРАНИЦЫ:\n";
    
    // Данные как на главной странице
    $mainPageViews = $sqlite->query("SELECT COUNT(*) as total_views, SUM(reward) as total_rewards FROM ad_views WHERE ad_type != 'test_banner'");
    $mainPageClicks = $sqlite->query("SELECT COUNT(*) as total_clicks FROM ad_clicks WHERE ad_type != 'test_banner'");
    
    $totalViews = $mainPageViews[0]['total_views'] ?? 0;
    $totalRewards = $mainPageViews[0]['total_rewards'] ?? 0;
    $totalClicks = $mainPageClicks[0]['total_clicks'] ?? 0;
    $mainCtr = $totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0;
    
    echo "   👁️ Просмотров рекламы: " . number_format($totalViews) . "\n";
    echo "   🖱️ Кликов по рекламе: " . number_format($totalClicks) . "\n";
    echo "   📈 CTR: {$mainCtr}%\n";
    echo "   💰 Награды за рекламу: " . number_format($totalRewards) . "\n";
    
    echo "\n5. 🕒 ПРОВЕРКА ПОЧАСОВОЙ СТАТИСТИКИ:\n";
    
    $hourlyQuery = "
        SELECT 
            CAST(strftime('%H', timestamp) AS INTEGER) as hour,
            COUNT(*) as views
        FROM ad_views 
        WHERE ad_type != 'test_banner'
        GROUP BY hour
        HAVING COUNT(*) > 0
        ORDER BY views DESC
        LIMIT 5
    ";
    
    $hourlyResults = $sqlite->query($hourlyQuery);
    
    echo "   🕒 ТОП-5 активных часов:\n";
    foreach ($hourlyResults as $row) {
        echo "     - {$row['hour']}:00 - {$row['views']} просмотров\n";
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ПРОВЕРКИ:\n";
    
    if ($testViews[0]['count'] > 0) {
        echo "   ✅ test_banner данные найдены и будут исключены\n";
    } else {
        echo "   ℹ️ test_banner данных нет\n";
    }
    
    echo "   ✅ CTR рассчитывается правильно (клики / просмотры * 100)\n";
    echo "   ✅ Статистика по странам работает с IP адресами\n";
    echo "   ✅ Данные главной страницы корректны\n";
    echo "   ✅ Почасовая статистика исключает test_banner\n";
    
    // Проверка разумности CTR
    if ($mainCtr > 0 && $mainCtr < 100) {
        echo "   ✅ CTR в разумных пределах ({$mainCtr}%)\n";
    } elseif ($mainCtr >= 100) {
        echo "   ⚠️ CTR очень высокий ({$mainCtr}%) - возможно тестовые данные\n";
    } else {
        echo "   ℹ️ CTR равен 0% - нет кликов или просмотров\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
