<?php
/**
 * migrate_localization_to_sqlite.php
 * Миграция всех переводов из папки locales в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🌍 МИГРАЦИЯ ЛОКАЛИЗАЦИИ В SQLITE\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Очищаем старые тексты
    echo "🗑️ Очищаем старые тексты из базы...\n";
    $sqlite->query("DELETE FROM bot_texts");
    echo "✅ Старые тексты удалены\n\n";
    
    $localesDir = 'locales/';
    $languages = ['ru', 'en'];
    $totalTexts = 0;
    $totalErrors = 0;
    
    foreach ($languages as $lang) {
        $filePath = $localesDir . $lang . '.json';
        
        echo "📂 Обрабатываем язык: {$lang}\n";
        echo "   Файл: {$filePath}\n";
        
        if (!file_exists($filePath)) {
            echo "   ❌ Файл не найден!\n\n";
            $totalErrors++;
            continue;
        }
        
        $content = file_get_contents($filePath);
        $data = json_decode($content, true);
        
        if (!$data) {
            echo "   ❌ Ошибка декодирования JSON!\n\n";
            $totalErrors++;
            continue;
        }
        
        echo "   ✅ JSON загружен успешно\n";
        
        // Функция для рекурсивного обхода переводов (объявляем только один раз)
        if (!function_exists('processTranslations')) {
            function processTranslations($translations, $prefix = '', $language = '', $sqlite = null, &$count = 0) {
            foreach ($translations as $key => $value) {
                $fullKey = $prefix ? $prefix . '.' . $key : $key;
                
                if (is_array($value)) {
                    // Рекурсивно обрабатываем вложенные объекты
                    processTranslations($value, $fullKey, $language, $sqlite, $count);
                } else {
                    // Это конечное значение - сохраняем в базу
                    try {
                        $category = explode('.', $fullKey)[0]; // Первая часть ключа как категория
                        
                        $sql = "INSERT INTO bot_texts (language_code, text_key, text_value, category) VALUES (?, ?, ?, ?)";
                        $sqlite->query($sql, [$language, $fullKey, $value, $category]);
                        $count++;
                        
                        if ($count % 50 === 0) {
                            echo "     Обработано: {$count} текстов...\n";
                        }
                        
                    } catch (Exception $e) {
                        echo "     ❌ Ошибка сохранения {$fullKey}: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        } // Закрываем функцию processTranslations

        $langCount = 0;
        processTranslations($data, '', $lang, $sqlite, $langCount);
        
        echo "   ✅ Сохранено текстов: {$langCount}\n\n";
        $totalTexts += $langCount;
    }
    
    echo "📊 РЕЗУЛЬТАТЫ МИГРАЦИИ:\n";
    echo "  🌍 Обработано языков: " . count($languages) . "\n";
    echo "  ✅ Всего текстов мигрировано: {$totalTexts}\n";
    echo "  ❌ Ошибок: {$totalErrors}\n\n";
    
    // Проверяем результат
    echo "🔍 Проверяем результат в SQLite...\n";
    $result = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts');
    echo "✅ В таблице bot_texts теперь: " . $result[0]['count'] . " записей\n";
    
    // Показываем статистику по языкам
    echo "\n📊 СТАТИСТИКА ПО ЯЗЫКАМ:\n";
    $langStats = $sqlite->query('SELECT language_code, COUNT(*) as count FROM bot_texts GROUP BY language_code ORDER BY count DESC');
    foreach ($langStats as $stat) {
        echo "  - {$stat['language_code']}: {$stat['count']} текстов\n";
    }
    
    // Показываем статистику по категориям
    echo "\n📂 СТАТИСТИКА ПО КАТЕГОРИЯМ:\n";
    $catStats = $sqlite->query('SELECT category, COUNT(*) as count FROM bot_texts GROUP BY category ORDER BY count DESC LIMIT 10');
    foreach ($catStats as $stat) {
        echo "  - {$stat['category']}: {$stat['count']} текстов\n";
    }
    
    // Примеры мигрированных текстов
    echo "\n📝 ПРИМЕРЫ МИГРИРОВАННЫХ ТЕКСТОВ:\n";
    $samples = $sqlite->query('SELECT language_code, text_key, text_value FROM bot_texts ORDER BY language_code, text_key LIMIT 10');
    foreach ($samples as $sample) {
        $value = strlen($sample['text_value']) > 50 ? substr($sample['text_value'], 0, 50) . "..." : $sample['text_value'];
        echo "  [{$sample['language_code']}] {$sample['text_key']}: {$value}\n";
    }
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 МИГРАЦИЯ ЛОКАЛИЗАЦИИ ЗАВЕРШЕНА!\n";
?>
