<?php
/**
 * api/db_mock_sqlite_new.php
 * Новая версия db_mock.php с использованием SQLite базы данных
 */

declare(strict_types=1);

require_once __DIR__ . '/../database/sqlite_api.php';

/**
 * Загрузка данных пользователей из SQLite
 */
function loadUserData(): array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        $users = $sqlite->getAllUsers();
        
        // Преобразуем в формат, совместимый со старым API
        $userData = [];
        foreach ($users as $user) {
            $telegramId = (string)$user['telegram_id'];
            $userData[$telegramId] = [
                'balance' => (float)$user['balance'],
                'total_earned' => (float)$user['total_earned'],
                'withdrawals' => [], // Загружаем отдельно если нужно
                'withdrawal_log' => [], // Загружаем отдельно если нужно
                'referrer_id' => $user['referrer_id'],
                'referrals' => [], // Загружаем отдельно если нужно
                'referral_earnings' => (float)$user['referral_earnings'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'username' => $user['username'],
                'language' => $user['language'],
                'registered_at' => $user['registered_at'],
                'last_activity' => $user['last_activity'],
                'suspicious_activity_count' => $user['suspicious_activity_count'],
                'withdrawals_count' => $user['withdrawals_count'],
                'blocked' => (bool)$user['blocked'],
                'blocked_at' => $user['blocked_at'],
                'referrals_count' => $user['referrals_count'],
                'joined' => $user['joined'],
                'suspicious_activity' => $user['suspicious_activity']
            ];
        }
        
        return $userData;
        
    } catch (Exception $e) {
        error_log("Ошибка загрузки пользователей из SQLite: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение деталей пользователя
 */
function getUserDetails(int $telegramId): ?array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        $user = $sqlite->getUserByTelegramId($telegramId);
        
        if (!$user) {
            return null;
        }
        
        return [
            'telegram_id' => $user['telegram_id'],
            'balance' => (float)$user['balance'],
            'total_earned' => (float)$user['total_earned'],
            'referrer_id' => $user['referrer_id'],
            'referral_earnings' => (float)$user['referral_earnings'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'username' => $user['username'],
            'language' => $user['language'],
            'registered_at' => $user['registered_at'],
            'last_activity' => $user['last_activity'],
            'suspicious_activity_count' => $user['suspicious_activity_count'],
            'withdrawals_count' => $user['withdrawals_count'],
            'blocked' => (bool)$user['blocked'],
            'blocked_at' => $user['blocked_at'],
            'referrals_count' => $user['referrals_count'],
            'joined' => $user['joined'],
            'suspicious_activity' => $user['suspicious_activity']
        ];
        
    } catch (Exception $e) {
        error_log("Ошибка получения пользователя {$telegramId} из SQLite: " . $e->getMessage());
        return null;
    }
}

/**
 * Логирование просмотра рекламы
 */
function logAdView(int $userId, string $adType, float $reward): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        
        return $sqlite->addAdView($userId, $adType, $reward, $ipAddress);
        
    } catch (Exception $e) {
        error_log("Ошибка логирования просмотра рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Логирование клика по рекламе
 */
function logAdClick(int $userId, string $adType, string $clickType, string $reason = ''): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        
        return $sqlite->addAdClick($userId, $adType, $clickType, $reason, $ipAddress);
        
    } catch (Exception $e) {
        error_log("Ошибка логирования клика по рекламе: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение лимитов рекламы пользователя
 */
function getUserAdLimits(int $userId): array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->getUserAdLimits($userId);
        
    } catch (Exception $e) {
        error_log("Ошибка получения лимитов рекламы: " . $e->getMessage());
        return [];
    }
}

/**
 * Обновление лимита рекламы пользователя
 */
function updateUserAdLimit(int $userId, string $adType, int $count): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->updateUserAdLimit($userId, $adType, $count);
        
    } catch (Exception $e) {
        error_log("Ошибка обновления лимита рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Создание токена рекламы
 */
function createAdToken(string $token, int $userId, string $adType, int $expiresIn = 3600): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->createAdToken($token, $userId, $adType, $expiresIn);
        
    } catch (Exception $e) {
        error_log("Ошибка создания токена рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Использование токена рекламы
 */
function useAdToken(string $token): ?array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->useAdToken($token);
        
    } catch (Exception $e) {
        error_log("Ошибка использования токена рекламы: " . $e->getMessage());
        return null;
    }
}

/**
 * Получение настроек бота
 */
function getBotSettingsSQLite(): array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->getBotSettings();
        
    } catch (Exception $e) {
        error_log("Ошибка получения настроек бота: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение текстов бота
 */
function getBotTextsSQLite(string $language = 'ru'): array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->getBotTexts($language);
        
    } catch (Exception $e) {
        error_log("Ошибка получения текстов бота: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение статистики базы данных
 */
function getDatabaseStats(): array
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->getDatabaseStats();
        
    } catch (Exception $e) {
        error_log("Ошибка получения статистики базы данных: " . $e->getMessage());
        return [
            'users_count' => 0,
            'ad_views_count' => 0,
            'ad_clicks_count' => 0,
            'ad_tokens_count' => 0,
            'support_messages_count' => 0,
            'file_size' => 0,
            'last_updated' => date('Y-m-d H:i:s'),
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Тестирование подключения к базе данных
 */
function testDatabaseConnection(): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->testConnection();
        
    } catch (Exception $e) {
        error_log("Ошибка тестирования подключения к базе данных: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение количества просмотров рекламы пользователя за день
 */
function getUserAdViewsCount(int $userId, string $date = null): int
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->getUserAdViewsCount($userId, $date);
        
    } catch (Exception $e) {
        error_log("Ошибка получения количества просмотров рекламы: " . $e->getMessage());
        return 0;
    }
}

/**
 * Обновление баланса пользователя
 */
function updateUserBalance(int $telegramId, float $newBalance): bool
{
    try {
        $sqlite = SQLiteAPI::getInstance();
        return $sqlite->updateUserBalance($telegramId, $newBalance);
        
    } catch (Exception $e) {
        error_log("Ошибка обновления баланса пользователя: " . $e->getMessage());
        return false;
    }
}

// Функции getSQLiteConnection() и executeSQLiteQuery() уже определены в sqlite_api.php
?>
