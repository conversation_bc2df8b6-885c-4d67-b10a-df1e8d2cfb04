<?php
/**
 * check_texts_structure.php
 * Проверка структуры текстов бота
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ПРОВЕРКА СТРУКТУРЫ ТЕКСТОВ БОТА\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Получаем примеры текстов
    echo "📋 ПРИМЕРЫ ТЕКСТОВ ИЗ БАЗЫ:\n";
    $sample = $sqlite->query('SELECT * FROM bot_texts LIMIT 10');
    foreach ($sample as $text) {
        $value = strlen($text['text_value']) > 50 ? substr($text['text_value'], 0, 50) . "..." : $text['text_value'];
        echo "  Language: {$text['language_code']}\n";
        echo "  Key: {$text['text_key']}\n";
        echo "  Value: {$value}\n";
        echo "  Category: " . ($text['category'] ?? 'NULL') . "\n";
        echo "  ---\n";
    }
    
    // Проверяем уникальные языки
    echo "\n🌍 УНИКАЛЬНЫЕ ЯЗЫКИ:\n";
    $languages = $sqlite->query('SELECT DISTINCT language_code FROM bot_texts ORDER BY language_code');
    foreach ($languages as $lang) {
        echo "  - {$lang['language_code']}\n";
    }
    
    // Проверяем уникальные ключи
    echo "\n🔑 УНИКАЛЬНЫЕ КЛЮЧИ (первые 20):\n";
    $keys = $sqlite->query('SELECT DISTINCT text_key FROM bot_texts ORDER BY text_key LIMIT 20');
    foreach ($keys as $key) {
        echo "  - {$key['text_key']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
