
// СРОЧНЫЙ ПАТЧ для отображения счетчиков лимитов
console.log('🚨 Применяем срочный патч счетчиков...');

// Функция для принудительного обновления счетчиков
window.forceUpdateCounters = async function() {
    try {
        console.log('🔄 Загружаем лимиты...');
        
        // Получаем user_id
        let userId = '5880288830'; // тестовый ID
        if (window.Telegram?.WebApp?.initDataUnsafe?.user?.id) {
            userId = window.Telegram.WebApp.initDataUnsafe.user.id.toString();
        }
        
        // Загружаем лимиты
        const response = await fetch(`api/get_limits_simple.php?user_id=${userId}`);
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Лимиты загружены:', data.data);
            
            // Обновляем счетчики на кнопках
            const mapping = {
                'native_banner': 'native-banner-counter',
                'rewarded_video': 'rewarded-video-counter',
                'interstitial': 'interstitial-counter'
            };
            
            Object.keys(mapping).forEach(adType => {
                const counterId = mapping[adType];
                const counterElement = document.getElementById(counterId);
                const limitInfo = data.data[adType];
                
                if (counterElement && limitInfo) {
                    const remaining = limitInfo.remaining;
                    let text;
                    
                    // Определяем язык
                    const isRussian = document.documentElement.lang === 'ru' || 
                                     navigator.language.startsWith('ru');
                    
                    if (isRussian) {
                        if (remaining === 0) text = 'лимит исчерпан';
                        else if (remaining === 1) text = 'остался 1 показ';
                        else if (remaining >= 2 && remaining <= 4) text = `осталось ${remaining} показа`;
                        else text = `осталось ${remaining} показов`;
                    } else {
                        if (remaining === 0) text = 'limit reached';
                        else if (remaining === 1) text = '1 ad view left';
                        else text = `${remaining} ad views left`;
                    }
                    
                    counterElement.textContent = text;
                    console.log(`✅ ${adType} (${counterId}): ${text}`);
                    
                    // Блокируем кнопку если лимит достигнут
                    if (remaining === 0) {
                        counterElement.classList.add('limit-reached');
                    } else {
                        counterElement.classList.remove('limit-reached');
                    }
                }
            });
            
            return true;
        } else {
            console.error('❌ Ошибка загрузки лимитов:', data);
            return false;
        }
    } catch (error) {
        console.error('❌ Ошибка обновления счетчиков:', error);
        return false;
    }
};

// Автоматически обновляем счетчики каждые 5 секунд
setInterval(() => {
    if (document.getElementById('openLinkCounter')) {
        window.forceUpdateCounters();
    }
}, 5000);

// Обновляем сразу
setTimeout(() => {
    window.forceUpdateCounters();
}, 1000);

console.log('✅ Патч счетчиков применен!');
