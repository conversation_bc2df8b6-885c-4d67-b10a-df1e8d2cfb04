
// СРОЧНЫЙ ПАТЧ для отображения счетчиков лимитов с определением языка по IP
console.log('🚨 Применяем срочный патч счетчиков с геолокацией...');

// Кэш для языка пользователя
let userLanguageCache = null;

// Функция для определения языка пользователя по IP
async function getUserLanguage() {
    if (userLanguageCache !== null) {
        return userLanguageCache;
    }

    try {
        console.log('🌍 Определяем язык по IP адресу...');
        const response = await fetch('api/get_user_language.php');
        const data = await response.json();

        if (data.success) {
            userLanguageCache = {
                language: data.language,
                isRussian: data.is_russian,
                ip: data.ip
            };
            console.log(`🌍 Язык определен: ${data.language} (IP: ${data.ip})`);
        } else {
            // Fallback: определяем по браузеру
            const browserLang = navigator.language || navigator.userLanguage || 'en';
            const isRussian = browserLang.startsWith('ru') ||
                             document.documentElement.lang === 'ru';

            userLanguageCache = {
                language: isRussian ? 'ru' : 'en',
                isRussian: isRussian,
                ip: 'unknown'
            };
            console.log(`🌍 Fallback язык: ${userLanguageCache.language} (браузер: ${browserLang})`);
        }
    } catch (error) {
        console.warn('⚠️ Ошибка определения языка по IP:', error);
        // Fallback: русский по умолчанию
        userLanguageCache = {
            language: 'ru',
            isRussian: true,
            ip: 'error'
        };
    }

    return userLanguageCache;
}

// Функция для получения переведенного текста счетчика
function getCounterText(remaining, isRussian) {
    if (isRussian) {
        // Русский язык с правильными склонениями
        if (remaining === 0) return 'лимит исчерпан';
        if (remaining === 1) return 'остался 1 показ';
        if (remaining >= 2 && remaining <= 4) return `осталось ${remaining} показа`;
        return `осталось ${remaining} показов`;
    } else {
        // Английский язык
        if (remaining === 0) return 'limit reached';
        if (remaining === 1) return '1 ad view left';
        return `${remaining} ad views left`;
    }
}

// Функция для принудительного обновления счетчиков
window.forceUpdateCounters = async function() {
    try {
        console.log('🔄 Загружаем лимиты и определяем язык...');

        // Определяем язык пользователя
        const langInfo = await getUserLanguage();

        // Получаем user_id
        let userId = '5880288830'; // тестовый ID
        if (window.Telegram?.WebApp?.initDataUnsafe?.user?.id) {
            userId = window.Telegram.WebApp.initDataUnsafe.user.id.toString();
        }

        // Загружаем лимиты
        const response = await fetch(`api/get_limits_simple.php?user_id=${userId}`);
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Лимиты загружены:', data.data);
            console.log(`🌍 Используем язык: ${langInfo.language} (IP: ${langInfo.ip})`);
            
            // Обновляем счетчики на кнопках
            const mapping = {
                'native_banner': 'native-banner-counter',
                'rewarded_video': 'rewarded-video-counter',
                'interstitial': 'interstitial-counter'
            };
            
            Object.keys(mapping).forEach(adType => {
                const counterId = mapping[adType];
                const counterElement = document.getElementById(counterId);
                const limitInfo = data.data[adType];
                
                if (counterElement && limitInfo) {
                    const remaining = limitInfo.remaining;

                    // Получаем переведенный текст на основе IP геолокации
                    const text = getCounterText(remaining, langInfo.isRussian);

                    counterElement.textContent = text;
                    console.log(`✅ ${adType} (${counterId}): "${text}" [${langInfo.language}, IP: ${langInfo.ip}]`);
                    
                    // Блокируем кнопку если лимит достигнут
                    if (remaining === 0) {
                        counterElement.classList.add('limit-reached');
                    } else {
                        counterElement.classList.remove('limit-reached');
                    }
                } else {
                    if (!counterElement) {
                        console.warn(`❌ Элемент ${counterId} не найден в DOM`);
                    }
                    if (!limitInfo) {
                        console.warn(`❌ Лимиты для ${adType} не найдены`);
                    }
                }
            });
            
            return true;
        } else {
            console.error('❌ Ошибка загрузки лимитов:', data);
            return false;
        }
    } catch (error) {
        console.error('❌ Ошибка обновления счетчиков:', error);
        return false;
    }
};

// Автоматически обновляем счетчики каждые 5 секунд
setInterval(() => {
    if (document.getElementById('openLinkCounter')) {
        window.forceUpdateCounters();
    }
}, 5000);

// Обновляем сразу
setTimeout(() => {
    window.forceUpdateCounters();
}, 1000);

console.log('✅ Патч счетчиков применен!');
