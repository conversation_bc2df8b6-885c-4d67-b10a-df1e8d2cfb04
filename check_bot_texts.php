<?php
/**
 * check_bot_texts.php
 * Проверка переводов (текстов бота) в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ПРОВЕРКА ПЕРЕВОДОВ (ТЕКСТОВ БОТА)\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Проверяем общее количество текстов
    echo "📊 ОБЩАЯ СТАТИСТИКА:\n";
    $total = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts');
    echo "  Всего текстов в базе: " . $total[0]['count'] . "\n";
    
    // Проверяем языки
    echo "\n🌍 ЯЗЫКИ:\n";
    $languages = $sqlite->query('SELECT language_code, COUNT(*) as count FROM bot_texts GROUP BY language_code ORDER BY count DESC');
    foreach ($languages as $lang) {
        echo "  - {$lang['language_code']}: {$lang['count']} текстов\n";
    }
    
    // Проверяем категории
    echo "\n📂 КАТЕГОРИИ:\n";
    $categories = $sqlite->query('SELECT category, COUNT(*) as count FROM bot_texts WHERE category IS NOT NULL GROUP BY category ORDER BY count DESC');
    foreach ($categories as $cat) {
        echo "  - {$cat['category']}: {$cat['count']} текстов\n";
    }
    
    // Примеры текстов на русском
    echo "\n📝 ПРИМЕРЫ ТЕКСТОВ НА РУССКОМ:\n";
    $ruTexts = $sqlite->query("SELECT text_key, text_value FROM bot_texts WHERE language_code = 'ru' LIMIT 10");
    foreach ($ruTexts as $text) {
        $value = strlen($text['text_value']) > 60 ? substr($text['text_value'], 0, 60) . "..." : $text['text_value'];
        echo "  - {$text['text_key']}: {$value}\n";
    }
    
    // Примеры текстов на английском
    echo "\n📝 ПРИМЕРЫ ТЕКСТОВ НА АНГЛИЙСКОМ:\n";
    $enTexts = $sqlite->query("SELECT text_key, text_value FROM bot_texts WHERE language_code = 'en' LIMIT 10");
    foreach ($enTexts as $text) {
        $value = strlen($text['text_value']) > 60 ? substr($text['text_value'], 0, 60) . "..." : $text['text_value'];
        echo "  - {$text['text_key']}: {$value}\n";
    }
    
    // Проверяем ключевые тексты
    echo "\n🔑 КЛЮЧЕВЫЕ ТЕКСТЫ:\n";
    $keyTexts = [
        'welcome_message',
        'balance_info',
        'withdrawal_success',
        'withdrawal_error',
        'ad_reward_received'
    ];
    
    foreach ($keyTexts as $key) {
        $result = $sqlite->query("SELECT language_code, text_value FROM bot_texts WHERE text_key = ? ORDER BY language_code", [$key]);
        if (!empty($result)) {
            echo "  📌 {$key}:\n";
            foreach ($result as $text) {
                $value = strlen($text['text_value']) > 50 ? substr($text['text_value'], 0, 50) . "..." : $text['text_value'];
                echo "    [{$text['language_code']}] {$value}\n";
            }
        } else {
            echo "  ⚠️ {$key}: НЕ НАЙДЕН\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
