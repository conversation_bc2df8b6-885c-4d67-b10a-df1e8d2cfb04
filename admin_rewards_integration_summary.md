# ⚙️ Интеграция наград за рекламу с настройками админки

## 🎯 Задача
Пользователь указал, что в кнопках рекламы в кружочках отображается количество монет, которое настраивается в админке. Нужно было сделать так, чтобы система начислений брала эти значения из настроек, а не использовала захардкоженные значения.

## ✅ Выполненные изменения

### 1. 🔍 Анализ существующей системы настроек

**Найдено:**
- Таблица `coin_settings` в SQLite базе данных
- Настройки хранятся как ключ-значение:
  - `ad_reward_native_banner` → 10 монет
  - `ad_reward_rewarded_video` → 1 монета  
  - `ad_reward_interstitial` → 10 монет

**Админка:**
- Файл `api/admin/settings.php` - интерфейс настроек
- Файл `api/admin/save_settings.php` - сохранение настроек
- Файл `api/config.php` - константы для fallback

### 2. 📡 Обновление API начислений (`api/recordAdView_sqlite.php`)

**Было (захардкожено):**
```php
$adRewards = [
    'banner' => 5,
    'video' => 10,
    'interstitial' => 8,
    'rewarded' => 15,
    'default' => 10
];
```

**Стало (из базы данных):**
```php
// Получаем награду из настроек базы данных
$reward = $coinsManager->getAdReward($adType);

// Fallback если настройка не найдена
if ($reward <= 0) {
    $fallbackRewards = [
        'native_banner' => 10,
        'rewarded_video' => 1, 
        'interstitial' => 10,
        // ...
    ];
    $reward = $fallbackRewards[$adType] ?? 10;
}
```

### 3. 📊 Создание API для получения настроек (`api/getAdSettings.php`)

**Функциональность:**
- Загрузка всех настроек рекламы из `coin_settings`
- Маппинг типов рекламы для совместимости
- Fallback значения при ошибках
- Дополнительные системные настройки

**Ответ API:**
```json
{
  "success": true,
  "ad_rewards": {
    "native_banner": 10,
    "rewarded_video": 1,
    "interstitial": 10
  },
  "ad_rewards_extended": {
    "native_banner": 10,
    "banner": 10,
    "link": 10,
    "rewarded_video": 1,
    "video": 1,
    "watch": 1,
    "interstitial": 10
  },
  "system_settings": {
    "daily_earn_limit": 200,
    "coin_rate_usd": 0.001,
    "min_withdrawal_coins": 1000
  }
}
```

### 4. 🎮 Обновление интеграционного модуля (`js/ad-reward-integration.js`)

**Новые возможности:**
- Автоматическая загрузка настроек при инициализации
- Обновление отображения наград в интерфейсе
- Маппинг типов рекламы для совместимости
- Методы для получения и обновления настроек

**Ключевые методы:**
```javascript
async loadAdSettings()              // Загрузка настроек из API
updateAdRewardsDisplay()            // Обновление кружочков в кнопках
getRewardForAdType(adType)          // Получение награды для типа
refreshAdSettings()                 // Перезагрузка настроек
```

### 5. 🧪 Демонстрационные страницы

**`demo_admin_rewards.html`:**
- Визуализация кнопок рекламы с кружочками
- Таблица настроек из базы данных
- Сравнение "до и после"
- Возможность обновления настроек

**`test_ad_reward_integration.html`:**
- Обновлен для использования актуальных наград
- Автоматическое обновление отображения
- Тестирование с реальными настройками

## 🔄 Схема работы

### Старая схема (захардкожено)
```
Кнопка рекламы → Фиксированная награда → API начисления
```

### Новая схема (из админки)
```
Админка → coin_settings → getAdSettings API → JavaScript → Обновление кружочков
                      ↓
                  recordAdView_sqlite API → Начисление по настройкам
```

## 📊 Маппинг типов рекламы

### Основные типы (в базе данных)
- `native_banner` → Нативный баннер (Open Link)
- `rewarded_video` → Видео с наградой (Watch Video)  
- `interstitial` → Полноэкранная реклама (Tap and Earn)

### Альтернативные названия (для совместимости)
```javascript
const typeMapping = {
  'native_banner': ['banner', 'link', 'native'],
  'rewarded_video': ['video', 'rewarded', 'watch'],
  'interstitial': ['interstitial', 'fullscreen', 'popup']
};
```

### Соответствие кнопкам в интерфейсе
- **Open Link** (🔗) → `native_banner` → 10 монет
- **Watch Video** (🎥) → `rewarded_video` → 1 монета
- **Tap and Earn** (📱) → `interstitial` → 10 монет

## 🛠️ Настройка в админке

### Путь к настройкам
1. Админка → Настройки
2. Раздел "Награды за типы рекламы"
3. Поля:
   - Баннер (монет) → `ad_reward_native_banner`
   - Видео (монет) → `ad_reward_rewarded_video`
   - Полноэкранная (монет) → `ad_reward_interstitial`

### Сохранение настроек
- Настройки сохраняются в таблицу `coin_settings`
- Автоматическое обновление в интерфейсе
- Fallback значения при ошибках

## 🔧 Техническая реализация

### База данных
```sql
-- Таблица настроек
CREATE TABLE coin_settings (
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT
);

-- Примеры записей
INSERT INTO coin_settings VALUES 
('ad_reward_native_banner', '10', 'Награда за просмотр нативного баннера'),
('ad_reward_rewarded_video', '1', 'Награда за просмотр видео'),
('ad_reward_interstitial', '10', 'Награда за просмотр интерстициала');
```

### CoinsManager метод
```php
public function getAdReward(string $adType): float {
    $settingKey = "ad_reward_{$adType}";
    $reward = $this->getSetting($settingKey, '0');
    return (float)$reward;
}
```

### JavaScript интеграция
```javascript
// Загрузка настроек
const settings = await fetch('/api/getAdSettings.php');
const data = await settings.json();

// Обновление кружочков
document.querySelectorAll('.reward-amount').forEach(el => {
    const adType = el.closest('[data-ad-type]').dataset.adType;
    const reward = data.ad_rewards[adType] || 10;
    el.textContent = reward;
});
```

## 🎯 Результат

### ✅ Что достигнуто:

1. **Динамические награды** - значения берутся из админки
2. **Автоматическое обновление** - кружочки обновляются при загрузке
3. **Fallback защита** - система работает даже при ошибках
4. **Совместимость** - поддержка всех существующих типов рекламы
5. **Централизованное управление** - все настройки в одном месте

### 📱 Пользователь видит:
```
🔗 Open Link        (10) ← Из админки
🎥 Watch Video      (1)  ← Из админки  
📱 Tap and Earn     (10) ← Из админки
```

### 👨‍💼 Администратор может:
- Изменить награды через админку
- Видеть изменения мгновенно в приложении
- Настраивать разные награды для разных типов рекламы
- Контролировать экономику приложения

## 🔄 Обратная совместимость

### Fallback механизм
1. **Первый приоритет:** Настройки из `coin_settings`
2. **Второй приоритет:** Константы из `config.php`
3. **Третий приоритет:** Захардкоженные значения в коде

### Поддержка старых типов
- Старые названия типов рекламы автоматически маппятся на новые
- Существующий код продолжает работать без изменений
- Постепенная миграция на новые типы

🚀 **Система полностью интегрирована с админкой и готова к использованию!**
