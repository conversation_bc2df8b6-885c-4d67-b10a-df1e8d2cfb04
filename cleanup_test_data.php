<?php
/**
 * cleanup_test_data.php
 * Очистка тестовых данных из базы данных
 */

declare(strict_types=1);

echo "🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ ИЗ БАЗЫ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    echo "1. 🔍 ПОИСК ТЕСТОВЫХ ДАННЫХ:\n";
    
    // Ищем тестовые выводы по характерным признакам
    $testWithdrawals = $db->query(
        "SELECT id, user_id, amount, wallet_address, status, requested_at 
         FROM user_withdrawals 
         WHERE wallet_address LIKE 'TTest%' 
            OR wallet_address LIKE '%test%' 
            OR wallet_address LIKE '%Test%'
            OR admin_notes LIKE '%test%'
            OR admin_notes LIKE '%Test%'"
    );
    
    echo "   📊 Найдено тестовых выводов: " . count($testWithdrawals) . "\n";
    
    if (!empty($testWithdrawals)) {
        echo "   📋 Список тестовых выводов:\n";
        foreach ($testWithdrawals as $withdrawal) {
            echo "     - ID: {$withdrawal['id']}, User: {$withdrawal['user_id']}, Amount: {$withdrawal['amount']}, Address: {$withdrawal['wallet_address']}\n";
        }
    }
    
    // Ищем тестовые транзакции
    $testTransactions = $db->query(
        "SELECT id, user_id, transaction_type, amount, description 
         FROM coin_transactions 
         WHERE description LIKE '%test%' 
            OR description LIKE '%Test%'
            OR description LIKE '%тест%'"
    );
    
    echo "\n   📊 Найдено тестовых транзакций: " . count($testTransactions) . "\n";
    
    if (!empty($testTransactions)) {
        echo "   📋 Типы тестовых транзакций:\n";
        $transactionTypes = [];
        foreach ($testTransactions as $transaction) {
            $type = $transaction['transaction_type'];
            if (!isset($transactionTypes[$type])) {
                $transactionTypes[$type] = 0;
            }
            $transactionTypes[$type]++;
        }
        
        foreach ($transactionTypes as $type => $count) {
            echo "     - {$type}: {$count} транзакций\n";
        }
    }
    
    // Ищем тестовые записи в audit_logs
    $testAuditLogs = $db->query(
        "SELECT COUNT(*) as count 
         FROM audit_logs 
         WHERE event_data LIKE '%test%' 
            OR event_data LIKE '%Test%'"
    );
    
    $auditCount = $testAuditLogs[0]['count'] ?? 0;
    echo "\n   📊 Найдено тестовых записей аудита: {$auditCount}\n";
    
    echo "\n2. ⚠️ ПОДТВЕРЖДЕНИЕ УДАЛЕНИЯ:\n";
    echo "   🚨 ВНИМАНИЕ: Будут удалены следующие данные:\n";
    echo "     - " . count($testWithdrawals) . " тестовых заявок на вывод\n";
    echo "     - " . count($testTransactions) . " тестовых транзакций\n";
    echo "     - {$auditCount} тестовых записей аудита\n";
    
    // Проверяем, есть ли что удалять
    $totalTestData = count($testWithdrawals) + count($testTransactions) + $auditCount;
    
    if ($totalTestData === 0) {
        echo "\n   ✅ Тестовых данных не найдено. База чистая!\n";
        return;
    }
    
    echo "\n3. 🗑️ УДАЛЕНИЕ ТЕСТОВЫХ ДАННЫХ:\n";
    
    $db->beginTransaction();
    
    $deletedWithdrawals = 0;
    $deletedTransactions = 0;
    $deletedAuditLogs = 0;
    
    try {
        // Удаляем тестовые выводы
        if (!empty($testWithdrawals)) {
            $withdrawalIds = array_column($testWithdrawals, 'id');
            $placeholders = str_repeat('?,', count($withdrawalIds) - 1) . '?';
            
            $result = $db->query(
                "DELETE FROM user_withdrawals WHERE id IN ({$placeholders})",
                $withdrawalIds
            );
            
            $deletedWithdrawals = count($testWithdrawals);
            echo "   ✅ Удалено тестовых выводов: {$deletedWithdrawals}\n";
        }
        
        // Удаляем тестовые транзакции
        if (!empty($testTransactions)) {
            $result = $db->query(
                "DELETE FROM coin_transactions 
                 WHERE description LIKE '%test%' 
                    OR description LIKE '%Test%'
                    OR description LIKE '%тест%'"
            );
            
            $deletedTransactions = count($testTransactions);
            echo "   ✅ Удалено тестовых транзакций: {$deletedTransactions}\n";
        }
        
        // Удаляем тестовые записи аудита
        if ($auditCount > 0) {
            $result = $db->query(
                "DELETE FROM audit_logs 
                 WHERE event_data LIKE '%test%' 
                    OR event_data LIKE '%Test%'"
            );
            
            $deletedAuditLogs = $auditCount;
            echo "   ✅ Удалено тестовых записей аудита: {$deletedAuditLogs}\n";
        }
        
        $db->commit();
        
        echo "\n4. 🔄 ВОССТАНОВЛЕНИЕ БАЛАНСОВ:\n";
        
        // Проверяем и исправляем балансы пользователей после удаления тестовых данных
        require_once 'api/coins_manager.php';
        $coinsManager = new CoinsManager();
        
        // Получаем пользователей, которые могли быть затронуты
        $affectedUsers = [];
        foreach ($testWithdrawals as $withdrawal) {
            $affectedUsers[$withdrawal['user_id']] = true;
        }
        foreach ($testTransactions as $transaction) {
            $affectedUsers[$transaction['user_id']] = true;
        }
        
        echo "   📊 Пользователей для проверки: " . count($affectedUsers) . "\n";
        
        $fixedBalances = 0;
        foreach (array_keys($affectedUsers) as $userId) {
            // Пересчитываем баланс на основе оставшихся транзакций
            $transactionSum = $db->query(
                "SELECT COALESCE(SUM(CASE WHEN operation = 'credit' THEN amount ELSE -amount END), 0) as sum
                 FROM coin_transactions 
                 WHERE user_id = ?",
                [$userId]
            )[0]['sum'];
            
            // Получаем текущий баланс
            $currentBalance = $db->query(
                "SELECT balance, reserved_balance FROM users WHERE telegram_id = ?",
                [$userId]
            );
            
            if (!empty($currentBalance)) {
                $current = $currentBalance[0];
                $expectedBalance = (float)$transactionSum;
                $currentBalanceValue = (float)$current['balance'];
                
                // Если балансы не сходятся, исправляем
                if (abs($expectedBalance - $currentBalanceValue) > 0.01) {
                    $db->query(
                        "UPDATE users SET balance = ? WHERE telegram_id = ?",
                        [$expectedBalance, $userId]
                    );
                    
                    $fixedBalances++;
                    echo "   🔧 Исправлен баланс пользователя {$userId}: {$currentBalanceValue} → {$expectedBalance}\n";
                }
            }
        }
        
        if ($fixedBalances === 0) {
            echo "   ✅ Все балансы корректны, исправлений не требуется\n";
        } else {
            echo "   ✅ Исправлено балансов: {$fixedBalances}\n";
        }
        
        echo "\n5. 📊 ИТОГОВАЯ СТАТИСТИКА:\n";
        
        $finalStats = [
            'Всего пользователей' => $db->query("SELECT COUNT(*) as count FROM users")[0]['count'],
            'Пользователей с балансом' => $db->query("SELECT COUNT(*) as count FROM users WHERE balance > 0")[0]['count'],
            'Всего заявок на вывод' => $db->query("SELECT COUNT(*) as count FROM user_withdrawals")[0]['count'],
            'Реальных выводов' => $db->query("SELECT COUNT(*) as count FROM user_withdrawals WHERE status != 'test'")[0]['count'],
            'Всего транзакций' => $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'],
            'Общий баланс системы' => $db->query("SELECT COALESCE(SUM(balance), 0) as total FROM users")[0]['total']
        ];
        
        foreach ($finalStats as $label => $value) {
            echo "   📊 {$label}: {$value}\n";
        }
        
        echo "\n✅ ОЧИСТКА ТЕСТОВЫХ ДАННЫХ ЗАВЕРШЕНА!\n";
        
        echo "\n🎯 РЕЗУЛЬТАТЫ ОЧИСТКИ:\n";
        echo "   🗑️ Удалено тестовых выводов: {$deletedWithdrawals}\n";
        echo "   🗑️ Удалено тестовых транзакций: {$deletedTransactions}\n";
        echo "   🗑️ Удалено записей аудита: {$deletedAuditLogs}\n";
        echo "   🔧 Исправлено балансов: {$fixedBalances}\n";
        echo "   ✅ База данных очищена от тестовых данных\n";
        echo "   🛡️ Реальные данные пользователей сохранены\n";
        
        echo "\n💡 РЕКОМЕНДАЦИИ:\n";
        echo "   📋 В будущем используйте отдельную тестовую базу\n";
        echo "   🏷️ Помечайте тестовые данные специальными префиксами\n";
        echo "   🔒 Не запускайте тесты на продакшн базе\n";
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА ОЧИСТКИ: " . $e->getMessage() . "\n";
    echo "📍 Файл: " . $e->getFile() . "\n";
    echo "📍 Строка: " . $e->getLine() . "\n";
}
?>
