<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо: История выплат с автообновлением</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-title {
            color: var(--cyber-accent-neon);
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .control-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .control-button.secondary {
            background: #2196f3;
        }
        .control-button.secondary:hover {
            background: #1976d2;
        }
        .control-button.danger {
            background: #dc3545;
        }
        .control-button.danger:hover {
            background: #c82333;
        }
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        .status-inactive {
            background: rgba(158, 158, 158, 0.2);
            color: #9e9e9e;
            border: 1px solid #9e9e9e;
        }
        .history-container {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid var(--cyber-border);
            border-radius: 10px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
        }
        .info-panel {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-title {
            color: #2196f3;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: var(--cyber-text-secondary);
        }
        .feature-list li:before {
            content: "✅ ";
            color: #4caf50;
            margin-right: 8px;
        }
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--cyber-border);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); margin-bottom: 30px;">
            📋 Демо: История выплат с автообновлением
        </h1>

        <!-- Статус системы -->
        <div class="demo-section">
            <div class="demo-title">
                📊 Статус системы автообновления
            </div>
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div id="auto-refresh-status" class="status-indicator status-inactive">
                    <span class="status-dot" style="width: 8px; height: 8px; border-radius: 50%; background: currentColor;"></span>
                    Автообновление: Выключено
                </div>
                <div id="last-update-status" class="status-indicator status-inactive">
                    <span>⏰</span>
                    Последнее обновление: Никогда
                </div>
                <div id="api-status" class="status-indicator status-inactive">
                    <span>📡</span>
                    API: Не проверен
                </div>
            </div>
        </div>

        <!-- Панель управления -->
        <div class="demo-section">
            <div class="demo-title">
                🎮 Панель управления
            </div>
            <div class="control-panel">
                <button class="control-button" onclick="startAutoRefresh()">
                    ▶️ Запустить автообновление
                </button>
                <button class="control-button danger" onclick="stopAutoRefresh()">
                    ⏹️ Остановить автообновление
                </button>
                <button class="control-button secondary" onclick="manualRefresh()">
                    🔄 Обновить сейчас
                </button>
                <button class="control-button secondary" onclick="testApiConnection()">
                    📡 Тест API
                </button>
                <button class="control-button secondary" onclick="clearHistory()">
                    🗑️ Очистить
                </button>
            </div>
        </div>

        <!-- Информация о функциях -->
        <div class="demo-section">
            <div class="demo-title">
                ℹ️ Возможности системы
            </div>
            <div class="info-panel">
                <div class="info-title">🚀 Новые функции автообновления:</div>
                <ul class="feature-list">
                    <li>Автоматическое обновление каждые 15 секунд</li>
                    <li>Актуальные статусы выплат из SQLite базы данных</li>
                    <li>Красивые карточки с детальной информацией</li>
                    <li>Статистика по статусам выплат</li>
                    <li>Индикатор времени последнего обновления</li>
                    <li>Fallback на старый API при ошибках</li>
                    <li>Возможность отмены выплат в статусе "pending"</li>
                </ul>
            </div>
        </div>

        <!-- История выплат -->
        <div class="demo-section">
            <div class="demo-title">
                📋 История выплат
            </div>
            <div id="withdrawal-history-container" class="history-container">
                <div style="text-align: center; color: var(--cyber-text-secondary); padding: 40px;">
                    Загрузка истории выплат...
                </div>
            </div>
        </div>

        <!-- Лог операций -->
        <div class="demo-section">
            <div class="demo-title">
                📝 Лог операций
            </div>
            <div id="operation-log" class="log-container">
                Ожидание операций...<br>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="control-button secondary" onclick="clearLog()" style="font-size: 12px; padding: 8px 16px;">
                    Очистить лог
                </button>
            </div>
        </div>
    </div>

    <!-- Подключение скриптов -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="js/withdrawal-manager.js"></script>

    <script>
        // Эмуляция Telegram WebApp
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22Demo%22%2C%22username%22%3A%22demouser%22%7D&auth_date=1640995200&hash=demo_hash',
                    initDataUnsafe: {
                        user: { id: 5880288830, first_name: 'Demo User', username: 'demouser' }
                    },
                    ready: () => log('Telegram WebApp готов (эмуляция)', 'success'),
                    expand: () => log('Telegram WebApp развернут (эмуляция)', 'info')
                }
            };
        }

        let withdrawalManager = null;

        function log(message, type = 'info') {
            const logEl = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#74c0fc',
                success: '#51cf66',
                error: '#ff6b6b',
                warning: '#ffd43b'
            };
            const color = colors[type] || colors.info;
            logEl.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('operation-log').innerHTML = 'Лог очищен...<br>';
        }

        function updateStatus(elementId, text, isActive = false) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                element.className = `status-indicator ${isActive ? 'status-active' : 'status-inactive'}`;
            }
        }

        function startAutoRefresh() {
            if (withdrawalManager) {
                withdrawalManager.startAutoRefresh();
                updateStatus('auto-refresh-status', '⚡ Автообновление: Активно', true);
                log('Автообновление запущено (каждые 15 секунд)', 'success');
            } else {
                log('WithdrawalManager не инициализирован', 'error');
            }
        }

        function stopAutoRefresh() {
            if (withdrawalManager) {
                withdrawalManager.stopAutoRefresh();
                updateStatus('auto-refresh-status', '⏹️ Автообновление: Остановлено', false);
                log('Автообновление остановлено', 'warning');
            }
        }

        async function manualRefresh() {
            if (withdrawalManager) {
                log('Ручное обновление истории...', 'info');
                await withdrawalManager.forceLoadHistory();
                updateStatus('last-update-status', `⏰ Обновлено: ${new Date().toLocaleTimeString()}`, true);
                log('Ручное обновление завершено', 'success');
            }
        }

        async function testApiConnection() {
            log('Тестирование подключения к API...', 'info');
            
            try {
                const response = await fetch('/api/getWithdrawalHistory_sqlite.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_id: '5880288830' })
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('api-status', '📡 API: Подключен', true);
                    log(`API тест успешен: ${data.success ? 'Данные получены' : 'Ошибка данных'}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('api-status', '📡 API: Ошибка', false);
                log(`API тест неудачен: ${error.message}`, 'error');
            }
        }

        function clearHistory() {
            const container = document.getElementById('withdrawal-history-container');
            if (container) {
                container.innerHTML = '<div style="text-align: center; color: var(--cyber-text-secondary); padding: 40px;">История очищена</div>';
                log('История выплат очищена', 'info');
            }
        }

        // Инициализация при загрузке
        window.addEventListener('load', () => {
            log('Демо страница истории выплат загружена', 'info');
            
            // Создаем кастомный контейнер для WithdrawalManager
            const container = document.getElementById('withdrawal-history-container');
            
            // Инициализируем WithdrawalManager
            withdrawalManager = new WithdrawalManager();
            withdrawalManager.elements.historyContainer = container;
            
            setTimeout(() => {
                withdrawalManager.init();
                log('WithdrawalManager инициализирован', 'success');
                
                // Автоматически запускаем автообновление
                setTimeout(() => {
                    startAutoRefresh();
                }, 1000);
                
            }, 500);
        });

        // Глобальные функции для совместимости
        window.withdrawalManager = null;
        window.appUtils = {
            showStatus: (message, type) => log(`Status: ${message}`, type)
        };
    </script>
</body>
</html>
