<?php
require_once 'database/real_sqlite_manager.php';
$sqlite = new RealSQLiteManager();

echo "БЫСТРАЯ ПРОВЕРКА ДАННЫХ:\n";
echo "Views: " . $sqlite->query("SELECT COUNT(*) as c FROM ad_views")[0]['c'] . "\n";
echo "Clicks: " . $sqlite->query("SELECT COUNT(*) as c FROM ad_clicks")[0]['c'] . "\n";

echo "\nПо типам:\n";
$types = $sqlite->query("SELECT ad_type, COUNT(*) as c FROM ad_views GROUP BY ad_type");
foreach($types as $t) echo "Views {$t['ad_type']}: {$t['c']}\n";

$types = $sqlite->query("SELECT ad_type, COUNT(*) as c FROM ad_clicks GROUP BY ad_type");
foreach($types as $t) echo "Clicks {$t['ad_type']}: {$t['c']}\n";
?>
