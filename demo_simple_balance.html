<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо: Упрощенный баланс без резервирования</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        .demo-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-title {
            color: var(--cyber-accent-neon);
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .balance-showcase {
            text-align: center;
            padding: 30px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 15px;
            margin: 20px 0;
        }
        .balance-amount {
            font-size: 36px;
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 15px var(--cyber-glow);
            margin-bottom: 10px;
        }
        .balance-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .balance-detail-item {
            background: rgba(255, 107, 53, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .detail-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--cyber-accent-neon);
            margin-bottom: 5px;
        }
        .detail-label {
            font-size: 12px;
            color: var(--cyber-text-secondary);
        }
        .demo-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .status-message {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--cyber-border);
            border-radius: 10px;
            padding: 15px;
        }
        .comparison-card.active {
            border-color: var(--cyber-accent-neon);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }
        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--cyber-text-primary);
        }
        .comparison-features {
            font-size: 12px;
            color: var(--cyber-text-secondary);
        }
        .feature-removed {
            text-decoration: line-through;
            opacity: 0.5;
        }
        .feature-simplified {
            color: var(--cyber-accent-neon);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); margin-bottom: 30px;">
            💰 Демо: Упрощенный баланс
        </h1>

        <!-- Сравнение схем -->
        <div class="demo-section">
            <div class="demo-title">
                🔄 Сравнение схем баланса
            </div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <div class="comparison-title">❌ Старая схема (с резервированием)</div>
                    <div class="comparison-features">
                        <div class="feature-removed">🔒 Резервирование средств</div>
                        <div class="feature-removed">📊 Доступный баланс</div>
                        <div class="feature-removed">🔐 Зарезервированный баланс</div>
                        <div class="feature-removed">⚡ Сложная логика</div>
                        <div class="feature-removed">🧮 Дополнительные расчеты</div>
                    </div>
                </div>
                <div class="comparison-card active">
                    <div class="comparison-title">✅ Новая схема (упрощенная)</div>
                    <div class="comparison-features">
                        <div class="feature-simplified">💰 Весь баланс доступен</div>
                        <div class="feature-simplified">🚀 Простая логика</div>
                        <div class="feature-simplified">⚡ Быстрые операции</div>
                        <div class="feature-simplified">🎯 Понятный интерфейс</div>
                        <div class="feature-simplified">🌐 Локализация</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Отображение баланса -->
        <div class="demo-section">
            <div class="demo-title">
                💰 Упрощенное отображение баланса
            </div>
            <div class="balance-showcase">
                <div id="main-balance" class="balance-amount">0 монет</div>
                <div id="balance-usd" style="color: var(--cyber-text-secondary);">≈ $0.00</div>
                <div style="margin-top: 15px; font-size: 14px; color: var(--cyber-text-primary);">
                    <span data-translate="balance.available">Доступно для вывода</span>: 
                    <span id="available-display" style="color: var(--cyber-accent-neon); font-weight: bold;">0</span>
                </div>
            </div>
            
            <div class="balance-details">
                <div class="balance-detail-item">
                    <div id="total-earned" class="detail-value">0</div>
                    <div class="detail-label" data-translate="balance.total_earned">Всего заработано</div>
                </div>
                <div class="balance-detail-item">
                    <div id="daily-earned" class="detail-value">0/200</div>
                    <div class="detail-label" data-translate="balance.earned_today">Заработано сегодня</div>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="demo-button" onclick="loadSimpleBalance()">
                    Загрузить баланс
                </button>
                <button class="demo-button" onclick="simulateEarning()">
                    Симуляция заработка
                </button>
            </div>
        </div>

        <!-- Статус системы -->
        <div class="demo-section">
            <div class="demo-title">
                📊 Статус упрощенной системы
            </div>
            <div id="system-status" class="status-message status-info">
                Загрузка статуса системы...
            </div>
        </div>

        <!-- Преимущества -->
        <div class="demo-section">
            <div class="demo-title">
                ✅ Преимущества упрощенной схемы
            </div>
            <div style="font-size: 14px; line-height: 1.6;">
                <div style="margin: 10px 0;">
                    <strong style="color: var(--cyber-accent-neon);">🚀 Простота:</strong>
                    Весь баланс сразу доступен для вывода
                </div>
                <div style="margin: 10px 0;">
                    <strong style="color: var(--cyber-accent-neon);">⚡ Скорость:</strong>
                    Нет сложных расчетов резервирования
                </div>
                <div style="margin: 10px 0;">
                    <strong style="color: var(--cyber-accent-neon);">🎯 Понятность:</strong>
                    Пользователь видит реальный доступный баланс
                </div>
                <div style="margin: 10px 0;">
                    <strong style="color: var(--cyber-accent-neon);">🌐 Локализация:</strong>
                    Все тексты переведены и локализованы
                </div>
                <div style="margin: 10px 0;">
                    <strong style="color: var(--cyber-accent-neon);">🔧 Совместимость:</strong>
                    Работает как с новым, так и со старым API
                </div>
            </div>
        </div>

        <!-- Лог операций -->
        <div class="demo-section">
            <div class="demo-title">
                📝 Лог операций
            </div>
            <div id="operation-log" style="background: rgba(0, 0, 0, 0.5); padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; border: 1px solid var(--cyber-border);">
                Ожидание операций...<br>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="demo-button" onclick="clearLog()" style="font-size: 12px; padding: 8px 16px;">Очистить лог</button>
            </div>
        </div>
    </div>

    <!-- Подключение скриптов -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="js/balance-api-client.js"></script>
    <script src="js/localization.js"></script>

    <script>
        // Эмуляция Telegram WebApp
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22Demo%22%2C%22username%22%3A%22demouser%22%7D&auth_date=1640995200&hash=demo_hash',
                    initDataUnsafe: {
                        user: { id: 5880288830, first_name: 'Demo User', username: 'demouser' }
                    },
                    ready: () => log('Telegram WebApp готов (эмуляция)', 'success'),
                    expand: () => log('Telegram WebApp развернут (эмуляция)', 'info')
                }
            };
        }

        let currentBalanceData = null;

        function log(message, type = 'info') {
            const logEl = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#74c0fc',
                success: '#51cf66',
                error: '#ff6b6b',
                warning: '#ffd43b'
            };
            const color = colors[type] || colors.info;
            logEl.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('operation-log').innerHTML = 'Лог очищен...<br>';
        }

        async function loadSimpleBalance() {
            try {
                log('Загрузка упрощенного баланса...', 'info');

                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const data = await window.balanceApiClient.getUserBalance();
                
                if (data.success) {
                    currentBalanceData = data;
                    updateBalanceDisplay(data);
                    updateSystemStatus(data);
                    log('Упрощенный баланс загружен успешно', 'success');
                } else {
                    throw new Error('Сервер вернул ошибку');
                }

            } catch (error) {
                log(`Ошибка загрузки баланса: ${error.message}`, 'error');
                document.getElementById('main-balance').textContent = `Ошибка: ${error.message}`;
            }
        }

        function updateBalanceDisplay(data) {
            // Основной баланс
            document.getElementById('main-balance').textContent = `${data.balance.toLocaleString()} монет`;
            document.getElementById('balance-usd').textContent = `≈ $${data.balance_usd.toFixed(4)}`;
            
            // Доступный баланс (весь баланс доступен)
            document.getElementById('available-display').textContent = data.balance.toLocaleString();
            
            // Статистика
            document.getElementById('total-earned').textContent = data.total_earned.toLocaleString();
            
            // Дневная статистика
            if (data.daily_stats) {
                const dailyText = `${data.daily_stats.earned_today}/${data.daily_stats.limit}`;
                document.getElementById('daily-earned').textContent = dailyText;
            }

            log(`Баланс обновлен: ${data.balance} монет (весь баланс доступен)`, 'success');
        }

        function updateSystemStatus(data) {
            const statusEl = document.getElementById('system-status');
            
            if (data.system_info) {
                const info = data.system_info;
                statusEl.innerHTML = `
                    <strong>API версия:</strong> ${info.api_version}<br>
                    <strong>Резервирование:</strong> ${info.reservation_enabled ? 'Включено' : 'Отключено'}<br>
                    <strong>SQLite backend:</strong> ${info.sqlite_backend ? 'Да' : 'Нет'}<br>
                    <strong>Последнее обновление:</strong> ${info.last_updated}
                `;
                statusEl.className = 'status-message status-success';
            } else {
                statusEl.textContent = 'Информация о системе недоступна';
                statusEl.className = 'status-message status-info';
            }
        }

        function simulateEarning() {
            if (currentBalanceData) {
                const reward = 10;
                currentBalanceData.balance += reward;
                currentBalanceData.total_earned += reward;
                
                if (currentBalanceData.daily_stats) {
                    currentBalanceData.daily_stats.earned_today += reward;
                }
                
                // Пересчитываем USD
                currentBalanceData.balance_usd = currentBalanceData.balance * 0.001;
                
                updateBalanceDisplay(currentBalanceData);
                log(`Симуляция заработка: +${reward} монет (весь баланс доступен для вывода)`, 'success');
            } else {
                log('Сначала загрузите баланс', 'warning');
            }
        }

        // Инициализация локализации
        async function initLocalization() {
            try {
                if (window.appLocalization) {
                    await window.appLocalization.init();
                    log('Локализация инициализирована', 'success');
                }
            } catch (error) {
                log(`Ошибка локализации: ${error.message}`, 'warning');
            }
        }

        // Автоматическая инициализация
        window.addEventListener('load', () => {
            log('Демо упрощенного баланса загружено', 'info');
            
            // Инициализируем локализацию
            setTimeout(() => {
                initLocalization();
            }, 500);
            
            // Загружаем баланс
            setTimeout(() => {
                loadSimpleBalance();
            }, 1500);
        });
    </script>
</body>
</html>
