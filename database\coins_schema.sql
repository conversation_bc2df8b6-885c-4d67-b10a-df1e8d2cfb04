-- coins_schema.sql
-- Схема базы данных для системы монет

-- Таблица балансов пользователей
CREATE TABLE IF NOT EXISTS user_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL UNIQUE,
    balance INTEGER NOT NULL DEFAULT 0,
    reserved_balance INTEGER NOT NULL DEFAULT 0, -- Зарезервированные средства для вывода
    total_earned INTEGER NOT NULL DEFAULT 0,     -- Всего заработано за все время
    total_withdrawn INTEGER NOT NULL DEFAULT 0,  -- Всего выведено за все время
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT check_balance_positive CHECK (balance >= 0),
    CONSTRAINT check_reserved_positive CHECK (reserved_balance >= 0),
    CONSTRAINT check_total_earned_positive CHECK (total_earned >= 0),
    CONSTRAINT check_total_withdrawn_positive CHECK (total_withdrawn >= 0)
);

-- Индекс для быстрого поиска по user_id
CREATE INDEX IF NOT EXISTS idx_user_balances_user_id ON user_balances(user_id);

-- Таблица транзакций (все операции с монетами)
CREATE TABLE IF NOT EXISTS coin_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    transaction_type TEXT NOT NULL, -- 'earn', 'withdraw', 'reserve', 'unreserve', 'bonus', 'penalty'
    amount INTEGER NOT NULL,        -- Сумма операции (всегда положительная)
    operation TEXT NOT NULL,       -- 'credit' (начисление) или 'debit' (списание)
    balance_before INTEGER NOT NULL, -- Баланс до операции
    balance_after INTEGER NOT NULL,  -- Баланс после операции
    source_type TEXT,               -- Источник: 'ad_view', 'referral', 'withdrawal', 'admin'
    source_id TEXT,                 -- ID источника (например, ID просмотра рекламы)
    description TEXT,               -- Описание операции
    metadata TEXT,                  -- JSON с дополнительными данными
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT check_amount_positive CHECK (amount > 0),
    CONSTRAINT check_operation_valid CHECK (operation IN ('credit', 'debit')),
    CONSTRAINT check_transaction_type_valid CHECK (
        transaction_type IN ('earn', 'withdraw', 'reserve', 'unreserve', 'bonus', 'penalty', 'refund')
    ),
    
    FOREIGN KEY (user_id) REFERENCES user_balances(user_id)
);

-- Индексы для таблицы транзакций
CREATE INDEX IF NOT EXISTS idx_coin_transactions_user_id ON coin_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_type ON coin_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_created_at ON coin_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_source ON coin_transactions(source_type, source_id);

-- Таблица заявок на вывод средств
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    amount INTEGER NOT NULL,           -- Сумма к выводу в монетах
    currency TEXT NOT NULL,           -- Валюта вывода: 'ton', 'eth', 'btc', 'usdttrc20'
    wallet_address TEXT NOT NULL,     -- Адрес кошелька
    crypto_amount TEXT,               -- Сумма в криптовалюте (строка для точности)
    network_fee TEXT,                 -- Сетевая комиссия
    final_amount TEXT,                -- Итоговая сумма к получению
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed', 'cancelled'
    transaction_hash TEXT,            -- Хэш транзакции в блокчейне
    error_message TEXT,               -- Сообщение об ошибке
    admin_notes TEXT,                 -- Заметки администратора
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,            -- Время обработки
    completed_at DATETIME,            -- Время завершения
    
    CONSTRAINT check_amount_positive CHECK (amount > 0),
    CONSTRAINT check_currency_valid CHECK (currency IN ('ton', 'eth', 'btc', 'usdttrc20')),
    CONSTRAINT check_status_valid CHECK (
        status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')
    ),
    
    FOREIGN KEY (user_id) REFERENCES user_balances(user_id)
);

-- Индексы для таблицы заявок на вывод
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_user_id ON withdrawal_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_created_at ON withdrawal_requests(created_at);

-- Таблица настроек системы монет
CREATE TABLE IF NOT EXISTS coin_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Вставляем базовые настройки
INSERT OR IGNORE INTO coin_settings (setting_key, setting_value, description) VALUES
('coin_rate_usd', '0.001', 'Курс монеты к доллару США'),
('min_withdrawal_coins', '1000', 'Минимальная сумма для вывода в монетах'),
('ad_reward_native_banner', '10', 'Награда за просмотр нативного баннера'),
('ad_reward_rewarded_video', '1', 'Награда за просмотр видео'),
('ad_reward_interstitial', '10', 'Награда за просмотр интерстициала'),
('referral_bonus_percent', '10', 'Процент бонуса с рефералов'),
('withdrawal_fee_percent', '0', 'Процент комиссии за вывод'),
('daily_earn_limit', '200', 'Дневной лимит заработка монет');

-- Индекс для настроек
CREATE INDEX IF NOT EXISTS idx_coin_settings_key ON coin_settings(setting_key);

-- Таблица статистики пользователей (денормализованная для быстрого доступа)
CREATE TABLE IF NOT EXISTS user_coin_stats (
    user_id TEXT PRIMARY KEY,
    total_earned_today INTEGER NOT NULL DEFAULT 0,
    total_earned_week INTEGER NOT NULL DEFAULT 0,
    total_earned_month INTEGER NOT NULL DEFAULT 0,
    total_earned_all_time INTEGER NOT NULL DEFAULT 0,
    total_withdrawn_all_time INTEGER NOT NULL DEFAULT 0,
    last_earn_date DATE,
    last_withdrawal_date DATE,
    referral_earnings INTEGER NOT NULL DEFAULT 0,
    ad_earnings INTEGER NOT NULL DEFAULT 0,
    bonus_earnings INTEGER NOT NULL DEFAULT 0,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_balances(user_id)
);

-- Индексы для статистики
CREATE INDEX IF NOT EXISTS idx_user_coin_stats_last_earn ON user_coin_stats(last_earn_date);
CREATE INDEX IF NOT EXISTS idx_user_coin_stats_updated ON user_coin_stats(updated_at);

-- Триггер для автоматического обновления updated_at в user_balances
CREATE TRIGGER IF NOT EXISTS update_user_balances_timestamp 
    AFTER UPDATE ON user_balances
    FOR EACH ROW
BEGIN
    UPDATE user_balances SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Триггер для автоматического обновления статистики при транзакциях
CREATE TRIGGER IF NOT EXISTS update_user_stats_on_transaction
    AFTER INSERT ON coin_transactions
    FOR EACH ROW
    WHEN NEW.operation = 'credit' AND NEW.transaction_type = 'earn'
BEGIN
    INSERT OR REPLACE INTO user_coin_stats (
        user_id, 
        total_earned_today,
        total_earned_week,
        total_earned_month,
        total_earned_all_time,
        total_withdrawn_all_time,
        last_earn_date,
        last_withdrawal_date,
        referral_earnings,
        ad_earnings,
        bonus_earnings,
        updated_at
    ) VALUES (
        NEW.user_id,
        COALESCE((SELECT total_earned_today FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + 
            CASE WHEN date('now') = date(NEW.created_at) THEN NEW.amount ELSE 0 END,
        COALESCE((SELECT total_earned_week FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + NEW.amount,
        COALESCE((SELECT total_earned_month FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + NEW.amount,
        COALESCE((SELECT total_earned_all_time FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + NEW.amount,
        COALESCE((SELECT total_withdrawn_all_time FROM user_coin_stats WHERE user_id = NEW.user_id), 0),
        date(NEW.created_at),
        COALESCE((SELECT last_withdrawal_date FROM user_coin_stats WHERE user_id = NEW.user_id), NULL),
        COALESCE((SELECT referral_earnings FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + 
            CASE WHEN NEW.source_type = 'referral' THEN NEW.amount ELSE 0 END,
        COALESCE((SELECT ad_earnings FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + 
            CASE WHEN NEW.source_type = 'ad_view' THEN NEW.amount ELSE 0 END,
        COALESCE((SELECT bonus_earnings FROM user_coin_stats WHERE user_id = NEW.user_id), 0) + 
            CASE WHEN NEW.source_type = 'bonus' THEN NEW.amount ELSE 0 END,
        CURRENT_TIMESTAMP
    );
END;
