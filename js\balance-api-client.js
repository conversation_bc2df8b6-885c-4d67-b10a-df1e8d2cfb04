/**
 * js/balance-api-client.js
 * Клиент для работы с новым SQLite API баланса
 */

class BalanceApiClient {
  constructor() {
    this.baseUrl = window.location.origin;
    this.endpoints = {
      balance: '/api/get_user_balance.php',
      balanceSqlite: '/api/getUserBalance_sqlite.php',
      transactions: '/api/get_transactions.php',
      stats: '/api/get_transaction_stats.php'
    };
  }

  /**
   * Получить данные пользователя для API запросов
   */
  getUserDataForAPI() {
    const tg = window.Telegram?.WebApp;
    if (!tg || !tg.initData) {
      console.warn('[BalanceApiClient] Telegram WebApp данные недоступны');
      return null;
    }

    return {
      initData: tg.initData
    };
  }

  /**
   * Выполнить API запрос
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const defaultOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      ...options
    };

    if (defaultOptions.method === 'POST' && options.body) {
      defaultOptions.body = JSON.stringify(options.body);
    }

    try {
      console.log(`[BalanceApiClient] 📡 ${defaultOptions.method} ${endpoint}`);
      
      const response = await fetch(url, defaultOptions);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'API returned success: false');
      }

      console.log(`[BalanceApiClient] ✅ ${endpoint} успешно`);
      return data;

    } catch (error) {
      console.error(`[BalanceApiClient] ❌ Ошибка ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Получить баланс пользователя (новый SQLite API)
   */
  async getUserBalance() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя Telegram');
    }

    try {
      // Сначала пробуем новый SQLite API
      const data = await this.request(this.endpoints.balanceSqlite, {
        body: userData
      });

      return {
        success: true,
        balance: data.balance,
        available_balance: data.available_balance,
        reserved_balance: data.reserved_balance,
        total_earned: data.total_earned,
        total_withdrawn: data.total_withdrawn,
        balance_usd: data.balance_usd,
        daily_stats: data.daily_stats,
        withdrawal_settings: data.withdrawal_settings,
        recent_transactions: data.recent_transactions,
        user_info: data.user_info
      };

    } catch (error) {
      console.warn('[BalanceApiClient] SQLite API недоступен, пробуем старый API:', error);
      
      // Fallback на старый API
      try {
        const fallbackData = await this.request(this.endpoints.balance, {
          body: userData
        });

        return {
          success: true,
          balance: fallbackData.balance || 0,
          available_balance: fallbackData.available_balance || fallbackData.balance || 0,
          reserved_balance: 0,
          total_earned: fallbackData.total_earned || 0,
          total_withdrawn: 0,
          balance_usd: 0,
          daily_stats: fallbackData.daily_stats || {},
          withdrawal_settings: {},
          recent_transactions: [],
          user_info: null
        };

      } catch (fallbackError) {
        console.error('[BalanceApiClient] Оба API недоступны:', fallbackError);
        throw new Error('Не удалось загрузить баланс');
      }
    }
  }

  /**
   * Получить историю транзакций
   */
  async getTransactions(limit = 50, offset = 0, transactionType = null) {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя Telegram');
    }

    return this.request(this.endpoints.transactions, {
      body: {
        ...userData,
        limit,
        offset,
        transaction_type: transactionType
      }
    });
  }

  /**
   * Получить статистику транзакций
   */
  async getTransactionStats(period = 'month') {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя Telegram');
    }

    return this.request(this.endpoints.stats, {
      body: {
        ...userData,
        period
      }
    });
  }

  /**
   * Проверить доступность нового SQLite API
   */
  async checkSqliteApiAvailability() {
    try {
      const response = await fetch(`${this.baseUrl}${this.endpoints.balanceSqlite}`, {
        method: 'GET'
      });
      return response.status !== 404;
    } catch (error) {
      return false;
    }
  }

  /**
   * Получить информацию о системе монет
   */
  async getSystemInfo() {
    try {
      const userData = this.getUserDataForAPI();
      if (!userData) {
        return null;
      }

      const data = await this.getUserBalance();
      return {
        sqlite_available: true,
        coin_rate_usd: data.withdrawal_settings?.coin_rate_usd || 0.001,
        min_withdrawal: data.withdrawal_settings?.min_withdrawal || 1000,
        daily_limit: data.daily_stats?.limit || 200,
        can_withdraw: data.withdrawal_settings?.can_withdraw || false
      };

    } catch (error) {
      console.warn('[BalanceApiClient] Не удалось получить информацию о системе:', error);
      return {
        sqlite_available: false,
        coin_rate_usd: 0.001,
        min_withdrawal: 1000,
        daily_limit: 200,
        can_withdraw: false
      };
    }
  }

  /**
   * Форматировать баланс для отображения
   */
  formatBalance(amount) {
    if (typeof amount !== 'number') {
      amount = parseFloat(amount) || 0;
    }
    return amount.toLocaleString();
  }

  /**
   * Конвертировать монеты в USD
   */
  convertToUSD(coins, rate = 0.001) {
    return (coins * rate).toFixed(4);
  }

  /**
   * Проверить, достаточно ли баланса для вывода
   */
  canWithdraw(amount, availableBalance, minWithdrawal = 1000) {
    return availableBalance >= amount && amount >= minWithdrawal;
  }
}

// Создаем глобальный экземпляр
window.balanceApiClient = new BalanceApiClient();

// Экспортируем для модульных систем
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BalanceApiClient;
}

console.log('📡 [BalanceApiClient] Клиент для SQLite API баланса загружен');
