<?php
/**
 * migrate_withdrawals_to_sqlite.php
 * Миграция данных выводов из JSON в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔄 МИГРАЦИЯ ДАННЫХ ВЫВОДОВ В SQLITE\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Загружаем данные пользователей из JSON
    echo "📂 Загружаем данные пользователей из JSON...\n";
    $userDataFile = 'api/user_data.json';
    
    if (!file_exists($userDataFile)) {
        throw new Exception("Файл {$userDataFile} не найден!");
    }
    
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (!$userData) {
        throw new Exception("Не удалось декодировать JSON из {$userDataFile}");
    }
    
    echo "✅ Загружено " . count($userData) . " пользователей\n\n";
    
    // Подключаемся к SQLite
    echo "🔗 Подключаемся к SQLite...\n";
    $sqlite = new RealSQLiteManager();
    echo "✅ Подключение установлено\n\n";
    
    // Ищем пользователей с выводами
    echo "🔍 Ищем пользователей с выводами...\n";
    $usersWithWithdrawals = 0;
    $totalWithdrawals = 0;
    $migratedWithdrawals = 0;
    $errors = 0;
    
    foreach ($userData as $userId => $user) {
        if (isset($user['withdrawals']) && is_array($user['withdrawals']) && count($user['withdrawals']) > 0) {
            $usersWithWithdrawals++;
            $userWithdrawals = count($user['withdrawals']);
            $totalWithdrawals += $userWithdrawals;
            
            echo "👤 Пользователь {$userId}: {$userWithdrawals} выводов\n";
            
            // Мигрируем каждый вывод
            foreach ($user['withdrawals'] as $withdrawal) {
                try {
                    // Подготавливаем данные для SQLite
                    $withdrawalData = [
                        'user_id' => (int)$userId,
                        'amount' => (float)($withdrawal['coins_amount'] ?? 0),
                        'currency' => $withdrawal['currency'] ?? 'TON',
                        'wallet_address' => $withdrawal['crypto_address'] ?? '',
                        'status' => $withdrawal['status'] ?? 'pending',
                        'transaction_hash' => $withdrawal['payout_id'] ?? null,
                        'requested_at' => isset($withdrawal['timestamp']) ? 
                            date('Y-m-d H:i:s', $withdrawal['timestamp']) : 
                            date('Y-m-d H:i:s'),
                        'processed_at' => ($withdrawal['status'] === 'completed' && isset($withdrawal['timestamp'])) ?
                            date('Y-m-d H:i:s', $withdrawal['timestamp']) : null,
                        'error_message' => $withdrawal['error_message'] ?? null
                    ];
                    
                    // Вставляем в SQLite
                    $sql = "INSERT INTO user_withdrawals (user_id, amount, currency, wallet_address, status, transaction_hash, requested_at, processed_at, error_message)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    $sqlite->query($sql, [
                        $withdrawalData['user_id'],
                        $withdrawalData['amount'],
                        $withdrawalData['currency'],
                        $withdrawalData['wallet_address'],
                        $withdrawalData['status'],
                        $withdrawalData['transaction_hash'],
                        $withdrawalData['requested_at'],
                        $withdrawalData['processed_at'],
                        $withdrawalData['error_message']
                    ]);
                    
                    $migratedWithdrawals++;
                    echo "  ✅ Мигрирован вывод: {$withdrawalData['amount']} {$withdrawalData['currency']} ({$withdrawalData['status']})\n";
                    
                } catch (Exception $e) {
                    $errors++;
                    echo "  ❌ Ошибка миграции вывода: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "\n📊 РЕЗУЛЬТАТЫ МИГРАЦИИ:\n";
    echo "  👥 Пользователей с выводами: {$usersWithWithdrawals}\n";
    echo "  💰 Всего выводов найдено: {$totalWithdrawals}\n";
    echo "  ✅ Успешно мигрировано: {$migratedWithdrawals}\n";
    echo "  ❌ Ошибок: {$errors}\n\n";
    
    // Проверяем результат
    echo "🔍 Проверяем результат в SQLite...\n";
    $result = $sqlite->query('SELECT COUNT(*) as count FROM user_withdrawals');
    echo "✅ В таблице user_withdrawals теперь: " . $result[0]['count'] . " записей\n";
    
    if ($result[0]['count'] > 0) {
        $sample = $sqlite->query('SELECT * FROM user_withdrawals ORDER BY requested_at DESC LIMIT 3');
        echo "\n📋 Примеры мигрированных выводов:\n";
        foreach ($sample as $w) {
            echo "  - ID: {$w['id']}, User: {$w['user_id']}, Amount: {$w['amount']} {$w['currency']}, Status: {$w['status']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 МИГРАЦИЯ ЗАВЕРШЕНА!\n";
?>
