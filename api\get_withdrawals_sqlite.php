<?php
/**
 * api/get_withdrawals_sqlite.php
 * API для получения истории выводов пользователя из SQLite
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $userId = null;
    $limit = 50;
    $offset = 0;
    $status = null;
    
    // Поддерживаем как GET, так и POST запросы
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Если есть initData, парсим его
        if (isset($input['initData'])) {
            require_once __DIR__ . '/validate_initdata.php';
            $userData = validateInitData($input['initData']);
            
            if (!$userData || !isset($userData['user']['id'])) {
                throw new Exception('Invalid Telegram data');
            }
            
            $userId = (string)$userData['user']['id'];
        } elseif (isset($input['user_id'])) {
            $userId = (string)$input['user_id'];
        }
        
        $limit = $input['limit'] ?? 50;
        $offset = $input['offset'] ?? 0;
        $status = $input['status'] ?? null;
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = (int)($_GET['offset'] ?? 0);
        $status = $_GET['status'] ?? null;
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Валидация параметров
    $limit = max(1, min(100, $limit));
    $offset = max(0, $offset);
    
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    // Строим SQL запрос с фильтрами
    $whereConditions = ['user_id = ?'];
    $params = [$userId];
    
    if ($status) {
        $whereConditions[] = 'status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Получаем общее количество выводов
    $totalQuery = "SELECT COUNT(*) as total FROM user_withdrawals WHERE {$whereClause}";
    $totalResult = $db->query($totalQuery, $params);
    $total = $totalResult[0]['total'];
    
    // Получаем выводы с пагинацией
    $withdrawalsQuery = "
        SELECT * FROM user_withdrawals 
        WHERE {$whereClause}
        ORDER BY requested_at DESC 
        LIMIT ? OFFSET ?
    ";
    $params[] = $limit;
    $params[] = $offset;
    
    $withdrawals = $db->query($withdrawalsQuery, $params);
    
    // Форматируем выводы для фронтенда
    $formattedWithdrawals = [];
    foreach ($withdrawals as $withdrawal) {
        $formattedWithdrawals[] = [
            'id' => (int)$withdrawal['id'],
            'amount' => (float)$withdrawal['amount'],
            'currency' => $withdrawal['currency'],
            'wallet_address' => $withdrawal['wallet_address'],
            'status' => $withdrawal['status'],
            'crypto_amount' => $withdrawal['crypto_amount'],
            'network_fee' => $withdrawal['network_fee'],
            'final_amount' => $withdrawal['final_amount'],
            'transaction_hash' => $withdrawal['transaction_hash'],
            'requested_at' => $withdrawal['requested_at'],
            'processed_at' => $withdrawal['processed_at'],
            'completed_at' => $withdrawal['completed_at'],
            'error_message' => $withdrawal['error_message'],
            'admin_notes' => $withdrawal['admin_notes'],
            'formatted_date' => date('d.m.Y H:i', strtotime($withdrawal['requested_at'])),
            'status_text' => getStatusText($withdrawal['status']),
            'status_color' => getStatusColor($withdrawal['status'])
        ];
    }
    
    // Получаем статистику выводов пользователя
    $statsQuery = "
        SELECT 
            COUNT(*) as total_withdrawals,
            SUM(amount) as total_amount,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count
        FROM user_withdrawals 
        WHERE user_id = ?
    ";
    $statsResult = $db->query($statsQuery, [$userId]);
    $stats = $statsResult[0];
    
    // Получаем статистику по валютам
    $currencyStatsQuery = "
        SELECT 
            currency,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        FROM user_withdrawals 
        WHERE user_id = ?
        GROUP BY currency
        ORDER BY total_amount DESC
    ";
    $currencyStats = $db->query($currencyStatsQuery, [$userId]);
    
    $formattedCurrencyStats = [];
    foreach ($currencyStats as $stat) {
        $formattedCurrencyStats[] = [
            'currency' => $stat['currency'],
            'count' => (int)$stat['count'],
            'total_amount' => (float)$stat['total_amount'],
            'avg_amount' => round((float)$stat['avg_amount'], 2)
        ];
    }
    
    // Получаем последние статусы для мониторинга
    $recentStatusQuery = "
        SELECT status, COUNT(*) as count
        FROM user_withdrawals 
        WHERE user_id = ? AND requested_at >= datetime('now', '-30 days')
        GROUP BY status
    ";
    $recentStatusStats = $db->query($recentStatusQuery, [$userId]);
    
    $statusDistribution = [];
    foreach ($recentStatusStats as $statusStat) {
        $statusDistribution[$statusStat['status']] = (int)$statusStat['count'];
    }
    
    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'pagination' => [
            'total' => (int)$total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total,
            'pages' => ceil($total / $limit),
            'current_page' => floor($offset / $limit) + 1
        ],
        'filters' => [
            'status' => $status
        ],
        'withdrawals' => $formattedWithdrawals,
        'statistics' => [
            'total_withdrawals' => (int)$stats['total_withdrawals'],
            'total_amount' => (float)$stats['total_amount'],
            'completed_amount' => (float)$stats['completed_amount'],
            'pending_count' => (int)$stats['pending_count'],
            'completed_count' => (int)$stats['completed_count'],
            'failed_count' => (int)$stats['failed_count'],
            'cancelled_count' => (int)$stats['cancelled_count'],
            'success_rate' => $stats['total_withdrawals'] > 0 ? 
                round(($stats['completed_count'] / $stats['total_withdrawals']) * 100, 1) : 0
        ],
        'currency_stats' => $formattedCurrencyStats,
        'status_distribution' => $statusDistribution
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'GET_WITHDRAWALS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Получить текст статуса на русском языке
 */
function getStatusText(string $status): string {
    $statusTexts = [
        'pending' => 'В обработке',
        'processing' => 'Обрабатывается',
        'completed' => 'Завершен',
        'failed' => 'Ошибка',
        'cancelled' => 'Отменен',
        'expired' => 'Истек',
        'waiting' => 'Ожидание',
        'confirming' => 'Подтверждение',
        'confirmed' => 'Подтвержден',
        'sending' => 'Отправка',
        'partially_paid' => 'Частично оплачен',
        'finished' => 'Завершен'
    ];
    
    return $statusTexts[$status] ?? ucfirst($status);
}

/**
 * Получить цвет статуса для UI
 */
function getStatusColor(string $status): string {
    $statusColors = [
        'pending' => 'orange',
        'processing' => 'blue',
        'completed' => 'green',
        'failed' => 'red',
        'cancelled' => 'gray',
        'expired' => 'red',
        'waiting' => 'orange',
        'confirming' => 'blue',
        'confirmed' => 'green',
        'sending' => 'blue',
        'partially_paid' => 'orange',
        'finished' => 'green'
    ];
    
    return $statusColors[$status] ?? 'gray';
}
?>
