<?php
/**
 * test_sqlite_connection.php
 * Простой тест подключения к SQLite
 */

declare(strict_types=1);

echo "🔍 ТЕСТ ПОДКЛЮЧЕНИЯ К SQLITE\n";
echo "============================\n\n";

try {
    // Проверяем наличие файлов
    $dbPath = __DIR__ . '/database/app.sqlite';
    $schemaPath = __DIR__ . '/database/schema.sql';
    
    echo "📁 Проверка файлов:\n";
    echo "   - База данных: " . ($dbPath) . "\n";
    echo "   - Существует: " . (file_exists($dbPath) ? "✅ ДА" : "❌ НЕТ") . "\n";
    echo "   - Размер: " . (file_exists($dbPath) ? filesize($dbPath) . " байт" : "0 байт") . "\n";
    echo "   - Схема: " . ($schemaPath) . "\n";
    echo "   - Существует: " . (file_exists($schemaPath) ? "✅ ДА" : "❌ НЕТ") . "\n\n";
    
    // Тест 1: Прямое подключение через PDO
    echo "🔌 ТЕСТ 1: Подключение через PDO\n";
    try {
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "   ✅ PDO подключение успешно\n";
        
        // Проверяем таблицы
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
        echo "   📋 Найдено таблиц: " . count($tables) . "\n";
        foreach ($tables as $table) {
            echo "      - {$table}\n";
        }
        
        // Проверяем пользователей
        if (in_array('users', $tables)) {
            $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            echo "   👥 Пользователей в базе: {$userCount}\n";
        }
        
        $pdo = null;
        echo "   ✅ PDO соединение закрыто\n\n";
        
    } catch (Exception $e) {
        echo "   ❌ Ошибка PDO: " . $e->getMessage() . "\n\n";
    }
    
    // Тест 2: Подключение через SQLite3
    echo "🔌 ТЕСТ 2: Подключение через SQLite3\n";
    try {
        $sqlite = new SQLite3($dbPath);
        $sqlite->enableExceptions(true);
        echo "   ✅ SQLite3 подключение успешно\n";
        
        // Проверяем таблицы
        $result = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $tables[] = $row['name'];
        }
        echo "   📋 Найдено таблиц: " . count($tables) . "\n";
        
        // Проверяем пользователей
        if (in_array('users', $tables)) {
            $userCount = $sqlite->querySingle("SELECT COUNT(*) FROM users");
            echo "   👥 Пользователей в базе: {$userCount}\n";
        }
        
        $sqlite->close();
        echo "   ✅ SQLite3 соединение закрыто\n\n";
        
    } catch (Exception $e) {
        echo "   ❌ Ошибка SQLite3: " . $e->getMessage() . "\n\n";
    }
    
    // Тест 3: Наш чистый менеджер
    echo "🔌 ТЕСТ 3: Наш SQLiteManagerClean\n";
    try {
        require_once 'database/sqlite_manager_clean.php';
        $manager = new SQLiteManagerClean($dbPath);
        
        echo "   ✅ SQLiteManagerClean создан\n";
        
        // Получаем информацию о базе
        $info = $manager->getDatabaseInfo();
        echo "   📊 Информация о базе:\n";
        echo "      - Тип: {$info['database_type']}\n";
        echo "      - Подключение: {$info['connection_type']}\n";
        echo "      - Размер файла: {$info['file_size']} байт\n";
        
        if (isset($info['tables'])) {
            echo "   📋 Статистика таблиц:\n";
            foreach ($info['tables'] as $table => $count) {
                echo "      - {$table}: {$count} записей\n";
            }
        }
        
        $manager->close();
        echo "   ✅ SQLiteManagerClean закрыт\n\n";
        
    } catch (Exception $e) {
        echo "   ❌ Ошибка SQLiteManagerClean: " . $e->getMessage() . "\n\n";
    }
    
    // Тест 4: Проверка JSON файлов
    echo "📁 ТЕСТ 4: Проверка JSON файлов\n";
    $jsonFiles = [
        'user_data.json' => 'database/user_data.json',
        'ad_views.json' => 'database/ad_views.json', 
        'ad_clicks.json' => 'database/ad_clicks.json'
    ];
    
    foreach ($jsonFiles as $name => $path) {
        if (file_exists($path)) {
            $data = json_decode(file_get_contents($path), true);
            $count = is_array($data) ? count($data) : 0;
            echo "   ✅ {$name}: {$count} записей\n";
        } else {
            echo "   ❌ {$name}: файл не найден\n";
        }
    }
    
    echo "\n🎉 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
    echo "========================\n";
    echo "Если все тесты прошли успешно, можно запускать миграцию.\n";
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}
?>
