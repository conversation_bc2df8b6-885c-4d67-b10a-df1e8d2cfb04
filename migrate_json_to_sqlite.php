<?php
/**
 * migrate_json_to_sqlite.php
 * Полная миграция актуальных JSON данных в SQLite
 */

declare(strict_types=1);

echo "🔄 МИГРАЦИЯ АКТУАЛЬНЫХ JSON ДАННЫХ В SQLITE\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    // 1. ОЧИСТКА СТАРЫХ ДАННЫХ
    echo "1. 🗑️ ОЧИСТКА СТАРЫХ ДАННЫХ:\n";
    
    $tables = ['ad_clicks', 'ad_views', 'users'];
    foreach ($tables as $table) {
        $count = $sqlite->query("SELECT COUNT(*) as count FROM {$table}")[0]['count'];
        echo "   - {$table}: {$count} записей (будут удалены)\n";
        $sqlite->query("DELETE FROM {$table}");
    }
    
    echo "   ✅ Старые данные очищены\n\n";
    
    // 2. МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ
    echo "2. 👥 МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ:\n";
    
    $userDataFile = 'database/user_data.json';
    if (!file_exists($userDataFile)) {
        throw new Exception("Файл {$userDataFile} не найден!");
    }
    
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (!$userData) {
        throw new Exception("Ошибка декодирования {$userDataFile}!");
    }
    
    echo "   📊 Найдено пользователей: " . count($userData) . "\n";
    
    $userCount = 0;
    foreach ($userData as $telegramId => $user) {
        $sql = "INSERT INTO users (
            telegram_id, balance, total_earned, referrer_id, referral_earnings,
            first_name, last_name, username, language, registered_at, last_activity,
            suspicious_activity_count, withdrawals_count, blocked, blocked_at,
            referrals_count, joined, suspicious_activity, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";
        
        $params = [
            (int)$telegramId,
            (float)($user['balance'] ?? 0),
            (float)($user['total_earned'] ?? 0),
            isset($user['referrer_id']) ? (int)$user['referrer_id'] : null,
            (float)($user['referral_earnings'] ?? 0),
            $user['first_name'] ?? '',
            $user['last_name'] ?? '',
            $user['username'] ?? '',
            $user['language'] ?? 'ru',
            (int)($user['registered_at'] ?? time()),
            (int)($user['last_activity'] ?? time()),
            (int)($user['suspicious_activity_count'] ?? 0),
            count($user['withdrawals'] ?? []),
            (bool)($user['blocked'] ?? false),
            isset($user['blocked_at']) ? (int)$user['blocked_at'] : null,
            count($user['referrals'] ?? []),
            (int)($user['joined'] ?? $user['registered_at'] ?? time()),
            (int)($user['suspicious_activity'] ?? 0)
        ];
        
        $sqlite->query($sql, $params);
        $userCount++;
        
        if ($userCount % 1000 == 0) {
            echo "   📊 Мигрировано пользователей: {$userCount}\n";
        }
    }
    
    echo "   ✅ Мигрировано пользователей: {$userCount}\n\n";
    
    // 3. МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ
    echo "3. 👁️ МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ:\n";
    
    $adViewsFile = 'database/ad_views.json';
    if (!file_exists($adViewsFile)) {
        throw new Exception("Файл {$adViewsFile} не найден!");
    }
    
    $adViews = json_decode(file_get_contents($adViewsFile), true);
    if (!$adViews) {
        throw new Exception("Ошибка декодирования {$adViewsFile}!");
    }
    
    echo "   📊 Найдено просмотров: " . count($adViews) . "\n";
    
    $viewCount = 0;
    $skippedViews = 0;
    
    foreach ($adViews as $view) {
        // Пропускаем test_banner
        if (($view['ad_type'] ?? '') === 'test_banner') {
            $skippedViews++;
            continue;
        }
        
        $sql = "INSERT INTO ad_views (
            user_id, ad_type, reward, timestamp, ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)";

        // Используем реальную дату из JSON или текущую дату
        $timestamp = $view['timestamp'] ?? date('Y-m-d H:i:s');

        $params = [
            (int)($view['user_id'] ?? 0),
            $view['ad_type'] ?? 'unknown',
            (float)($view['reward'] ?? 0),
            $timestamp,
            $view['ip'] ?? null,
            $view['user_agent'] ?? null,
            $timestamp // created_at тоже используем реальную дату
        ];
        
        $sqlite->query($sql, $params);
        $viewCount++;
        
        if ($viewCount % 500 == 0) {
            echo "   📊 Мигрировано просмотров: {$viewCount}\n";
        }
    }
    
    echo "   ✅ Мигрировано просмотров: {$viewCount}\n";
    echo "   ⚠️ Пропущено test_banner: {$skippedViews}\n\n";
    
    // 4. МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ
    echo "4. 🖱️ МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ:\n";
    
    $adClicksFile = 'database/ad_clicks.json';
    if (!file_exists($adClicksFile)) {
        throw new Exception("Файл {$adClicksFile} не найден!");
    }
    
    $adClicks = json_decode(file_get_contents($adClicksFile), true);
    if (!$adClicks) {
        throw new Exception("Ошибка декодирования {$adClicksFile}!");
    }
    
    echo "   📊 Найдено кликов: " . count($adClicks) . "\n";
    
    $clickCount = 0;
    $skippedClicks = 0;
    $clicksWithIP = 0;
    
    foreach ($adClicks as $click) {
        // Пропускаем test_banner
        if (($click['ad_type'] ?? '') === 'test_banner') {
            $skippedClicks++;
            continue;
        }
        
        $sql = "INSERT INTO ad_clicks (
            user_id, ad_type, click_type, reason, timestamp, ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $ipAddress = $click['ip'] ?? null;
        if ($ipAddress && $ipAddress !== 'unknown' && $ipAddress !== '') {
            $clicksWithIP++;
        }

        // Используем реальную дату из JSON или текущую дату
        $timestamp = $click['timestamp'] ?? date('Y-m-d H:i:s');

        $params = [
            (int)($click['user_id'] ?? 0),
            $click['ad_type'] ?? 'unknown',
            $click['click_type'] ?? 'button_click',
            $click['reason'] ?? '',
            $timestamp,
            $ipAddress,
            $click['user_agent'] ?? null,
            $timestamp // created_at тоже используем реальную дату
        ];
        
        $sqlite->query($sql, $params);
        $clickCount++;
        
        if ($clickCount % 1000 == 0) {
            echo "   📊 Мигрировано кликов: {$clickCount}\n";
        }
    }
    
    echo "   ✅ Мигрировано кликов: {$clickCount}\n";
    echo "   🌐 Кликов с IP адресами: {$clicksWithIP}\n";
    echo "   ⚠️ Пропущено test_banner: {$skippedClicks}\n\n";
    
    // 5. ПРОВЕРКА РЕЗУЛЬТАТОВ
    echo "5. 📊 ПРОВЕРКА РЕЗУЛЬТАТОВ МИГРАЦИИ:\n";
    
    $finalUsers = $sqlite->query("SELECT COUNT(*) as count FROM users")[0]['count'];
    $finalViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views")[0]['count'];
    $finalClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks")[0]['count'];
    
    echo "   👥 Пользователей в SQLite: {$finalUsers}\n";
    echo "   👁️ Просмотров в SQLite: {$finalViews}\n";
    echo "   🖱️ Кликов в SQLite: {$finalClicks}\n";
    
    // Проверка IP адресов
    $viewsWithIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ip_address IS NOT NULL AND ip_address != ''")[0]['count'];
    $clicksWithIPFinal = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ip_address IS NOT NULL AND ip_address != ''")[0]['count'];
    
    echo "   🌐 Просмотров с IP: {$viewsWithIP}\n";
    echo "   🌐 Кликов с IP: {$clicksWithIPFinal}\n";
    
    // Проверка CTR
    echo "\n6. 📈 ПРОВЕРКА CTR:\n";
    
    $ctrQuery = "
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COALESCE(c.clicks, 0) as clicks,
            CASE 
                WHEN COUNT(v.id) > 0 THEN ROUND((COALESCE(c.clicks, 0) * 100.0 / COUNT(v.id)), 2)
                ELSE 0 
            END as ctr
        FROM ad_views v
        LEFT JOIN (
            SELECT ad_type, COUNT(*) as clicks 
            FROM ad_clicks 
            GROUP BY ad_type
        ) c ON v.ad_type = c.ad_type
        GROUP BY v.ad_type
        ORDER BY views DESC
    ";
    
    $ctrResults = $sqlite->query($ctrQuery);
    
    foreach ($ctrResults as $row) {
        echo "   - {$row['ad_type']}: {$row['views']} просмотров, {$row['clicks']} кликов, CTR: {$row['ctr']}%\n";
    }
    
    // 7. МИГРАЦИЯ ЛИМИТОВ РЕКЛАМЫ
    echo "7. 🎯 МИГРАЦИЯ ЛИМИТОВ РЕКЛАМЫ:\n";

    $adLimitsFile = 'database/ad_limits.json';
    if (!file_exists($adLimitsFile)) {
        echo "   ⚠️ Файл {$adLimitsFile} не найден, пропускаем\n";
    } else {
        $adLimitsData = json_decode(file_get_contents($adLimitsFile), true);
        if (!$adLimitsData) {
            echo "   ❌ Ошибка декодирования {$adLimitsFile}!\n";
        } else {
            echo "   📊 Найдены лимиты в JSON\n";

            $limitsCount = 0;
            $today = date('Y-m-d');

            // Мигрируем счетчики пользователей
            if (isset($adLimitsData['user_counts'])) {
                foreach ($adLimitsData['user_counts'] as $userIdStr => $counts) {
                    // Извлекаем ID пользователя из строки "user_7176766994"
                    $userId = (int)str_replace('user_', '', $userIdStr);

                    foreach ($counts as $adType => $count) {
                        // Пропускаем test_banner
                        if ($adType === 'test_banner') {
                            continue;
                        }

                        // Проверяем, есть ли уже запись
                        $existing = $sqlite->query("
                            SELECT id FROM ad_limits
                            WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                        ", [$userId, $adType, $today]);

                        if (empty($existing)) {
                            // Добавляем новую запись
                            $sqlite->query("
                                INSERT INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                                VALUES (?, ?, ?, ?)
                            ", [$userId, $adType, $count, $today]);

                            $limitsCount++;
                        } else {
                            // Обновляем существующую запись
                            $sqlite->query("
                                UPDATE ad_limits
                                SET daily_count = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                            ", [$count, $userId, $adType, $today]);
                        }
                    }
                }
            }

            echo "   ✅ Мигрировано лимитов: {$limitsCount}\n";
        }
    }

    echo "\n✅ МИГРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!\n";
    echo "🎯 Теперь в SQLite актуальные данные с правильными IP адресами и лимитами\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА МИГРАЦИИ: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎉 ГОТОВО!\n";
?>
