<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
/**
 * Полная диагностика системы поддержки
 */

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Диагностика системы поддержки</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-ok { color: green; }
        .status-error { color: red; }
        .status-warning { color: orange; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Диагностика системы поддержки</h1>
        
        <!-- Проверка конфигурации -->
        <div class="card mb-3">
            <div class="card-header"><h5>📋 Конфигурация</h5></div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr><td>Токен бота:</td><td><?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo substr(SUPPORT_BOT_TOKEN, 0, 10) . '...'; ?></td></tr>
                    <tr><td>Username:</td><td>@<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo SUPPORT_BOT_USERNAME; ?></td></tr>
                    <tr><td>Webhook URL:</td><td><?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo SUPPORT_WEBHOOK_URL; ?></td></tr>
                </table>
            </div>
        </div>

        <!-- Проверка файлов -->
        <div class="card mb-3">
            <div class="card-header"><h5>📁 Файлы системы</h5></div>
            <div class="card-body">
                <table class="table table-sm">
                    <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
                    $files = [
                        'support_config.php' => __DIR__ . '/support_config.php',
                        'support_data.php' => __DIR__ . '/support_data.php',
                        'support_webhook.php' => __DIR__ . '/support_webhook.php',
                        'support_chats.json' => SUPPORT_CHATS_FILE,
                        'support_messages.json' => SUPPORT_MESSAGES_FILE,
                        'support_bot.log' => SUPPORT_LOG_FILE
                    ];
                    
                    foreach ($files as $name => $path) {
                        $exists = file_exists($path);
                        $writable = $exists ? is_writable($path) : false;
                        $size = $exists ? filesize($path) : 0;
                        
                        $status = $exists ? 
                            ($writable ? '<span class="status-ok">✅ OK</span>' : '<span class="status-warning">⚠️ Не записывается</span>') : 
                            '<span class="status-error">❌ Отсутствует</span>';
                        
                        echo "<tr><td>{$name}</td><td>{$status}</td><td>{$size} байт</td></tr>";
                    }
                    ?>
                </table>
            </div>
        </div>

        <!-- Проверка Telegram API -->
        <div class="card mb-3">
            <div class="card-header"><h5>🤖 Telegram API</h5></div>
            <div class="card-body">
                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
                $botInfo = supportTelegramRequest('getMe', []);
                if ($botInfo && isset($botInfo['result'])) {
                    $bot = $botInfo['result'];
                    echo '<div class="alert alert-success">✅ Бот работает: ' . $bot['first_name'] . ' (@' . $bot['username'] . ')</div>';
                } else {
                    echo '<div class="alert alert-danger">❌ Бот не отвечает</div>';
                }

                $webhookInfo = supportTelegramRequest('getWebhookInfo', []);
                if ($webhookInfo && isset($webhookInfo['result'])) {
                    $webhook = $webhookInfo['result'];
                    echo '<table class="table table-sm">';
                    echo '<tr><td>Webhook URL:</td><td>' . ($webhook['url'] ?: '<span class="status-error">НЕ УСТАНОВЛЕН</span>') . '</td></tr>';
                    echo '<tr><td>Ожидающих обновлений:</td><td>' . $webhook['pending_update_count'] . '</td></tr>';
                    
                    if (isset($webhook['last_error_date'])) {
                        echo '<tr><td>Последняя ошибка:</td><td><span class="status-error">' . date('d.m.Y H:i:s', $webhook['last_error_date']) . '</span></td></tr>';
                        echo '<tr><td>Сообщение ошибки:</td><td><span class="status-error">' . $webhook['last_error_message'] . '</span></td></tr>';
                    } else {
                        echo '<tr><td>Ошибки:</td><td><span class="status-ok">Нет</span></td></tr>';
                    }
                    echo '</table>';
                }
                ?>
            </div>
        </div>

        <!-- Проверка данных -->
        <div class="card mb-3">
            <div class="card-header"><h5>💾 Данные</h5></div>
            <div class="card-body">
                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
                $chats = getSupportChats();
                $messages = getSupportMessages();
                
                echo '<table class="table table-sm">';
                echo '<tr><td>Чатов поддержки:</td><td>' . count($chats) . '</td></tr>';
                echo '<tr><td>Сообщений:</td><td>' . count($messages) . '</td></tr>';
                echo '</table>';
                
                if (!empty($chats)) {
                    echo '<h6>Последние чаты:</h6>';
                    echo '<ul>';
                    foreach (array_slice($chats, -3, 3, true) as $chatId => $chat) {
                        $name = $chat['first_name'] . ($chat['last_name'] ? ' ' . $chat['last_name'] : '');
                        echo '<li>' . $name . ' (ID: ' . $chat['user_id'] . ', Язык: ' . ($chat['language'] ?? 'неизвестен') . ')</li>';
                    }
                    echo '</ul>';
                }
                ?>
            </div>
        </div>

        <!-- Логи -->
        <div class="card mb-3">
            <div class="card-header"><h5>📝 Последние записи в логе</h5></div>
            <div class="card-body">
                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
                if (file_exists(SUPPORT_LOG_FILE)) {
                    $logContent = file_get_contents(SUPPORT_LOG_FILE);
                    if (!empty($logContent)) {
                        $lines = explode("\n", trim($logContent));
                        $lastLines = array_slice($lines, -10);
                        echo '<pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">';
                        echo htmlspecialchars(implode("\n", $lastLines));
                        echo '</pre>';
                    } else {
                        echo '<div class="alert alert-warning">Лог пуст</div>';
                    }
                } else {
                    echo '<div class="alert alert-danger">Лог файл не существует</div>';
                }
                ?>
            </div>
        </div>

        <!-- Действия -->
        <div class="card mb-3">
            <div class="card-header"><h5>🔧 Действия</h5></div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="fix_webhook.php" class="btn btn-primary btn-sm mb-2">🔧 Исправить webhook</a><br>
                        <a href="test_webhook_simple.php" class="btn btn-info btn-sm mb-2">🧪 Простой тест</a><br>
                        <a href="support.php" class="btn btn-success btn-sm mb-2">📞 Открыть поддержку</a>
                    </div>
                    <div class="col-md-6">
                        <a href="support_bot.log" target="_blank" class="btn btn-secondary btn-sm mb-2">📝 Открыть лог</a><br>
                        <a href="https://t.me/<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo SUPPORT_BOT_USERNAME; ?>" target="_blank" class="btn btn-primary btn-sm mb-2">💬 Открыть бота</a><br>
                        <button onclick="location.reload()" class="btn btn-outline-secondary btn-sm mb-2">🔄 Обновить</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Инструкции -->
        <div class="card mb-3">
            <div class="card-header"><h5>📖 Инструкции по тестированию</h5></div>
            <div class="card-body">
                <ol>
                    <li>Нажмите <strong>"Исправить webhook"</strong> выше</li>
                    <li>Откройте бота: <a href="https://t.me/<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo SUPPORT_BOT_USERNAME; ?>" target="_blank">@<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo SUPPORT_BOT_USERNAME; ?></a></li>
                    <li>Отправьте команду <code>/start</code></li>
                    <li>Отправьте любое сообщение</li>
                    <li>Обновите эту страницу и проверьте логи</li>
                    <li>Проверьте админку поддержки</li>
                </ol>
            </div>
        </div>

        <div class="text-muted text-center">
            <small>Время проверки: <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ echo date('Y-m-d H:i:s'); ?></small>
        </div>
    </div>
</body>
</html>
