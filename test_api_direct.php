<?php
/**
 * test_api_direct.php
 * Прямой тест API без авторизации
 */

declare(strict_types=1);

echo "🔧 ПРЯМОЙ ТЕСТ API\n";
echo "=" . str_repeat("=", 30) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ТЕСТ ПРОСТОГО ЗАПРОСА:\n";
    
    // Простой запрос статистики
    $simpleQuery = "
        SELECT 
            ad_type,
            COUNT(*) as views
        FROM ad_views 
        WHERE ad_type != 'test_banner'
        GROUP BY ad_type
        ORDER BY views DESC
    ";
    
    $simpleResults = $sqlite->query($simpleQuery);
    
    echo "   Просмотры по типам:\n";
    foreach ($simpleResults as $row) {
        echo "   - {$row['ad_type']}: {$row['views']} просмотров\n";
    }
    
    echo "\n2. 📊 ТЕСТ ОБЪЕДИНЕННОГО ЗАПРОСА:\n";
    
    // Тестируем новый запрос из API
    $unionQuery = "
        SELECT 
            ad_type,
            SUM(views) as views,
            SUM(clicks) as clicks,
            SUM(rewards) as rewards,
            CASE 
                WHEN SUM(views) > 0 THEN ROUND((SUM(clicks) * 1.0 / SUM(views)), 4)
                ELSE 0 
            END as ctr
        FROM (
            SELECT 
                ad_type,
                COUNT(*) as views,
                0 as clicks,
                SUM(reward) as rewards
            FROM ad_views 
            WHERE ad_type != 'test_banner'
            GROUP BY ad_type
            
            UNION ALL
            
            SELECT 
                ad_type,
                0 as views,
                COUNT(*) as clicks,
                0 as rewards
            FROM ad_clicks 
            WHERE ad_type != 'test_banner'
            GROUP BY ad_type
        ) combined
        GROUP BY ad_type
        ORDER BY clicks DESC
    ";
    
    $unionResults = $sqlite->query($unionQuery);
    
    echo "   Объединенная статистика:\n";
    foreach ($unionResults as $row) {
        $ctrPercent = round((float)$row['ctr'] * 100, 2);
        echo "   - {$row['ad_type']}:\n";
        echo "     👁️ Просмотры: {$row['views']}\n";
        echo "     🖱️ Клики: {$row['clicks']}\n";
        echo "     💰 Награды: {$row['rewards']}\n";
        echo "     📈 CTR: {$ctrPercent}%\n";
    }
    
    echo "\n3. 🌍 ТЕСТ СТАТИСТИКИ ПО СТРАНАМ:\n";
    
    $countryQuery = "
        SELECT
            ip_address,
            COUNT(*) as clicks
        FROM ad_clicks
        WHERE ad_type != 'test_banner' AND ip_address IS NOT NULL AND ip_address != ''
        GROUP BY ip_address
        ORDER BY clicks DESC
        LIMIT 10
    ";
    
    $countryResults = $sqlite->query($countryQuery);
    
    echo "   ТОП-10 IP адресов:\n";
    foreach ($countryResults as $row) {
        echo "   - {$row['ip_address']}: {$row['clicks']} кликов\n";
    }
    
    echo "\n4. 🔧 ТЕСТ API ФУНКЦИИ:\n";
    
    // Симулируем вызов API
    $_GET = []; // Без фильтров
    
    // Устанавливаем переменные как в API
    $date_from = null;
    $date_to = null;
    $ad_type_filter = 'all';
    
    // Условия для фильтрации просмотров
    $viewWhereConditions = ["ad_type != 'test_banner'"];
    $viewParams = [];
    
    // Условия для фильтрации кликов
    $clickWhereConditions = ["ad_type != 'test_banner'"];
    $clickParams = [];
    
    $viewWhereClause = 'WHERE ' . implode(' AND ', $viewWhereConditions);
    $clickWhereClause = 'WHERE ' . implode(' AND ', $clickWhereConditions);
    
    // Тестируем запрос как в API
    $apiQuery = "
        SELECT 
            ad_type,
            SUM(views) as views,
            SUM(clicks) as clicks,
            SUM(rewards) as rewards,
            CASE 
                WHEN SUM(views) > 0 THEN ROUND((SUM(clicks) * 1.0 / SUM(views)), 4)
                ELSE 0 
            END as ctr
        FROM (
            SELECT 
                ad_type,
                COUNT(*) as views,
                0 as clicks,
                SUM(reward) as rewards
            FROM ad_views 
            {$viewWhereClause}
            GROUP BY ad_type
            
            UNION ALL
            
            SELECT 
                ad_type,
                0 as views,
                COUNT(*) as clicks,
                0 as rewards
            FROM ad_clicks 
            {$clickWhereClause}
            GROUP BY ad_type
        ) combined
        GROUP BY ad_type
        ORDER BY clicks DESC
    ";
    
    $allParams = array_merge($viewParams, $clickParams);
    $apiResults = $sqlite->query($apiQuery, $allParams);
    
    echo "   API запрос результат:\n";
    $stats_by_type = [];
    
    foreach ($apiResults as $row) {
        $stats_by_type[$row['ad_type']] = [
            'views' => (int)$row['views'],
            'clicks' => (int)$row['clicks'],
            'rewards' => (float)$row['rewards'],
            'ctr' => round((float)$row['ctr'] * 100, 2) // Умножаем на 100 для процентов
        ];
        
        echo "   - {$row['ad_type']}: {$stats_by_type[$row['ad_type']]['views']} просмотров, {$stats_by_type[$row['ad_type']]['clicks']} кликов, CTR: {$stats_by_type[$row['ad_type']]['ctr']}%\n";
    }
    
    echo "\n5. 📋 ФОРМАТ JSON:\n";
    
    $response = [
        'success' => true,
        'stats_by_type' => $stats_by_type,
        'stats_by_country' => ['RU' => 100, 'US' => 50], // Пример
        'hourly_stats' => array_fill(0, 24, ['clicks' => 0, 'views' => 0])
    ];
    
    echo "   JSON ответ:\n";
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n\n🎯 ТЕСТ ЗАВЕРШЕН!\n";
?>
