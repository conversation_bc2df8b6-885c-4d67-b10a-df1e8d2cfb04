<?php
/**
 * api/requestWithdrawal_sqlite.php
 * API эндпоинт для запроса вывода средств через NOWPayments с использованием SQLite
 */

declare(strict_types=1);

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Подключаем зависимости
    require_once __DIR__ . '/config.php';
    require_once __DIR__ . '/validate_initdata.php';
    require_once __DIR__ . '/security.php';
    require_once __DIR__ . '/NOWPaymentsAPI.php';
    require_once __DIR__ . '/FeeCalculator.php';
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    $requiredFields = ['initData', 'amount', 'address', 'currency'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $initData = $input['initData'];
    $amount = (float)$input['amount'];
    $cryptoAddress = trim($input['address']);
    $cryptoCurrency = strtoupper(trim($input['currency']));
    $cryptoAmount = $input['crypto_amount'] ?? null;
    
    // Валидируем сумму
    if ($amount <= 0) {
        throw new Exception('Amount must be positive');
    }
    
    // Валидируем валюту
    $supportedCurrencies = ['USDT', 'BTC', 'ETH', 'TRX', 'TON'];
    if (!in_array($cryptoCurrency, $supportedCurrencies)) {
        throw new Exception('Unsupported currency');
    }
    
    // Парсим initData для получения user_id
    $userData = validateInitData($initData);
    if (!$userData || !isset($userData['user']['id'])) {
        throw new Exception('Invalid Telegram data');
    }
    
    $userId = (string)$userData['user']['id'];
    
    // Инициализируем менеджеры
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Проверяем существование пользователя в SQLite
    $userExists = $db->query("SELECT telegram_id FROM users WHERE telegram_id = ?", [$userId]);
    if (empty($userExists)) {
        throw new Exception('User not found in database');
    }
    
    // Получаем баланс пользователя из SQLite
    $balance = $coinsManager->getUserBalance($userId);
    $userBalance = $balance['available_balance'];
    
    error_log("requestWithdrawal_sqlite INFO: User {$userId} balance: {$userBalance}, requested: {$amount}");
    
    // Проверяем минимальный баланс для доступа к выводу
    $minBalanceForWithdrawal = (float)$coinsManager->getSetting('min_balance_for_withdrawal', '100');
    if ($balance['balance'] < $minBalanceForWithdrawal) {
        throw new Exception("Minimum balance required: {$minBalanceForWithdrawal} coins");
    }
    
    // Проверяем минимальную сумму вывода
    $minWithdrawal = (float)$coinsManager->getSetting('min_withdrawal_coins', '1000');
    if ($amount < $minWithdrawal) {
        throw new Exception("Minimum withdrawal amount: {$minWithdrawal} coins");
    }
    
    // Проверяем достаточность баланса
    if ($userBalance < $amount) {
        throw new Exception("Insufficient balance. Available: {$userBalance} coins");
    }
    
    // Проверяем лимиты на вывод
    $withdrawalLimits = checkWithdrawalLimitsSQLite($userId, $amount, $cryptoCurrency, $db);
    if (!$withdrawalLimits['allowed']) {
        throw new Exception($withdrawalLimits['error']);
    }
    
    // Проверяем безопасность (подозрительная активность)
    $securityCheck = checkUserSecuritySQLite($userId, $db);
    if (!$securityCheck['allowed']) {
        throw new Exception($securityCheck['error']);
    }
    
    // Валидируем адрес кошелька
    $walletValidation = validateWalletAddressSQLite($cryptoAddress, $cryptoCurrency);
    if (!$walletValidation['valid']) {
        throw new Exception('Invalid wallet address: ' . $walletValidation['error']);
    }
    
    // Логируем событие запроса на вывод
    logAuditEventSQLite('withdrawal_request', $userId, [
        'amount' => $amount,
        'currency' => $cryptoCurrency,
        'address' => $cryptoAddress
    ], $db);
    
    // Расчет суммы с комиссиями
    try {
        $calculator = FeeCalculator::getInstance();
        $calculationResult = $calculator->calculateWithdrawalAmount($amount, $cryptoCurrency);
        
        if (!$calculationResult['success']) {
            throw new Exception('Fee calculation failed: ' . $calculationResult['error']);
        }
        
        $finalCryptoAmount = $calculationResult['crypto_amount_gross'];
        $networkFee = $calculationResult['network_fee_crypto'];
        $userReceives = $calculationResult['crypto_amount_net'];
        
        error_log("requestWithdrawal_sqlite INFO: Calculated amounts - Gross: {$finalCryptoAmount}, Net: {$userReceives}, Fee: {$networkFee}");
        
    } catch (Exception $e) {
        error_log("requestWithdrawal_sqlite ERROR: Fee calculation error: " . $e->getMessage());
        throw new Exception('Error calculating fees');
    }
    
    try {
        $db->beginTransaction();
        
        // Резервируем средства (списываем с доступного баланса)
        $reserveSuccess = $coinsManager->reserveCoins($userId, $amount, 0); // Временный ID
        
        if (!$reserveSuccess) {
            throw new Exception('Failed to reserve coins');
        }
        
        // Создаем заявку на вывод в SQLite
        $db->query(
            "INSERT INTO user_withdrawals 
             (user_id, amount, currency, wallet_address, status, crypto_amount, network_fee, final_amount, requested_at) 
             VALUES (?, ?, ?, ?, 'pending', ?, ?, ?, CURRENT_TIMESTAMP)",
            [
                $userId,
                $amount,
                $cryptoCurrency,
                $cryptoAddress,
                number_format($finalCryptoAmount, 8),
                number_format($networkFee, 8),
                number_format($userReceives, 8)
            ]
        );
        
        // Получаем ID созданной заявки
        $withdrawalId = $db->query("SELECT last_insert_rowid() as id")[0]['id'];
        
        // Обновляем резервирование с правильным ID
        $db->query(
            "UPDATE coin_transactions SET source_id = ?, description = ? 
             WHERE user_id = ? AND transaction_type = 'reserve' AND source_id = 0 
             ORDER BY created_at DESC LIMIT 1",
            [$withdrawalId, "Reserved for withdrawal #{$withdrawalId}", $userId]
        );
        
        // Создаем запрос на вывод через NOWPayments API
        $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
        
        $result = $api->createSinglePayout(
            $cryptoAddress,
            $cryptoCurrency,
            $finalCryptoAmount,
            $userId // extra_id для связывания
        );
        
        if (isset($result['error'])) {
            throw new Exception('NOWPayments error: ' . ($result['message'] ?? 'Unknown error'));
        }
        
        $nowpaymentsId = $result['id'] ?? null;
        $initialStatus = $result['status'] ?? 'pending';
        
        // Обновляем заявку с данными от NOWPayments
        $db->query(
            "UPDATE user_withdrawals SET transaction_hash = ?, status = ? WHERE id = ?",
            [$nowpaymentsId, $initialStatus, $withdrawalId]
        );
        
        // Записываем транзакцию создания заявки
        $transactionSuccess = $coinsManager->recordTransaction(
            $userId,
            'withdraw_request',
            $amount,
            'debit',
            $balance['balance'],
            $balance['balance'], // Баланс не меняется, только резерв
            'withdrawal',
            $withdrawalId,
            "Withdrawal request #{$withdrawalId} for {$userReceives} {$cryptoCurrency}",
            [
                'withdrawal_id' => $withdrawalId,
                'nowpayments_id' => $nowpaymentsId,
                'currency' => $cryptoCurrency,
                'wallet_address' => $cryptoAddress,
                'crypto_amount_gross' => $finalCryptoAmount,
                'crypto_amount_net' => $userReceives,
                'network_fee' => $networkFee
            ]
        );
        
        // Обновляем счетчик выводов пользователя
        $db->query(
            "UPDATE users SET withdrawals_count = withdrawals_count + 1 WHERE telegram_id = ?",
            [$userId]
        );
        
        $db->commit();
        
        // Получаем обновленный баланс
        $newBalance = $coinsManager->getUserBalance($userId);
        
        // Логируем успешное создание заявки
        logAuditEventSQLite('withdrawal_created', $userId, [
            'withdrawal_id' => $withdrawalId,
            'nowpayments_id' => $nowpaymentsId,
            'amount' => $amount,
            'currency' => $cryptoCurrency,
            'status' => $initialStatus
        ], $db);
        
        error_log("requestWithdrawal_sqlite INFO: Successfully created withdrawal #{$withdrawalId} for user {$userId}");
        
        // Возвращаем успешный ответ
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request created successfully',
            'withdrawal_id' => (int)$withdrawalId,
            'nowpayments_id' => $nowpaymentsId,
            'new_balance' => $newBalance['available_balance'],
            'withdrawal_data' => [
                'id' => $withdrawalId,
                'amount' => $amount,
                'currency' => $cryptoCurrency,
                'wallet_address' => $cryptoAddress,
                'crypto_amount_gross' => $finalCryptoAmount,
                'crypto_amount_net' => $userReceives,
                'network_fee' => $networkFee,
                'status' => $initialStatus,
                'requested_at' => date('Y-m-d H:i:s')
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("requestWithdrawal_sqlite ERROR: Transaction failed: " . $e->getMessage());
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("requestWithdrawal_sqlite ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'WITHDRAWAL_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Проверка лимитов на вывод в SQLite
 */
function checkWithdrawalLimitsSQLite(string $userId, float $amount, string $currency, RealSQLiteManager $db): array {
    // Проверяем дневной лимит
    $dailyWithdrawals = $db->query(
        "SELECT COALESCE(SUM(amount), 0) as total 
         FROM user_withdrawals 
         WHERE user_id = ? AND date(requested_at) = date('now') AND status != 'cancelled'",
        [$userId]
    )[0]['total'];
    
    $dailyLimit = 10000; // 10000 монет в день
    if (($dailyWithdrawals + $amount) > $dailyLimit) {
        return [
            'allowed' => false,
            'error' => "Daily withdrawal limit exceeded. Used: {$dailyWithdrawals}/{$dailyLimit} coins"
        ];
    }
    
    // Проверяем количество заявок в день
    $dailyRequestsCount = $db->query(
        "SELECT COUNT(*) as count 
         FROM user_withdrawals 
         WHERE user_id = ? AND date(requested_at) = date('now')",
        [$userId]
    )[0]['count'];
    
    $maxDailyRequests = 3;
    if ($dailyRequestsCount >= $maxDailyRequests) {
        return [
            'allowed' => false,
            'error' => "Maximum {$maxDailyRequests} withdrawal requests per day"
        ];
    }
    
    // Проверяем есть ли незавершенные заявки
    $pendingWithdrawals = $db->query(
        "SELECT COUNT(*) as count 
         FROM user_withdrawals 
         WHERE user_id = ? AND status = 'pending'",
        [$userId]
    )[0]['count'];
    
    $maxPendingRequests = 2;
    if ($pendingWithdrawals >= $maxPendingRequests) {
        return [
            'allowed' => false,
            'error' => "Maximum {$maxPendingRequests} pending withdrawal requests allowed"
        ];
    }
    
    return ['allowed' => true];
}

/**
 * Проверка безопасности пользователя в SQLite
 */
function checkUserSecuritySQLite(string $userId, RealSQLiteManager $db): array {
    $user = $db->query("SELECT blocked, suspicious_activity_count FROM users WHERE telegram_id = ?", [$userId]);
    
    if (empty($user)) {
        return ['allowed' => false, 'error' => 'User not found'];
    }
    
    $userData = $user[0];
    
    if ($userData['blocked']) {
        return ['allowed' => false, 'error' => 'Account is blocked'];
    }
    
    if ($userData['suspicious_activity_count'] > 5) {
        return ['allowed' => false, 'error' => 'Account has suspicious activity'];
    }
    
    return ['allowed' => true];
}

/**
 * Валидация адреса кошелька
 */
function validateWalletAddressSQLite(string $address, string $currency): array {
    $address = trim($address);
    
    switch ($currency) {
        case 'USDT':
        case 'TRX':
            if (preg_match('/^T[A-Za-z0-9]{33}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid TRON address format'];
            
        case 'BTC':
            if (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) || 
                preg_match('/^3[a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||   
                preg_match('/^bc1[a-z0-9]{39,59}$/', $address)) {             
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid Bitcoin address format'];
            
        case 'ETH':
            if (preg_match('/^0x[a-fA-F0-9]{40}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid Ethereum address format'];
            
        case 'TON':
            if (preg_match('/^[A-Za-z0-9_-]{48}$/', $address)) {
                return ['valid' => true];
            }
            return ['valid' => false, 'error' => 'Invalid TON address format'];
            
        default:
            return ['valid' => false, 'error' => 'Unsupported currency'];
    }
}

/**
 * Логирование событий аудита в SQLite
 */
function logAuditEventSQLite(string $event, string $userId, array $data, RealSQLiteManager $db): void {
    try {
        $db->query(
            "INSERT INTO audit_logs (event_type, user_id, event_data, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
            [$event, $userId, json_encode($data)]
        );
    } catch (Exception $e) {
        error_log("logAuditEventSQLite ERROR: " . $e->getMessage());
    }
}
?>
