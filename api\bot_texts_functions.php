<?php
/**
 * api/bot_texts_functions.php
 * Функции для работы с текстами бота (без веб-интерфейса)
 */

declare(strict_types=1);

/**
 * Загрузка текстов бота из SQLite
 */
function loadBotTextsFromSQLite() {
    try {
        require_once __DIR__ . '/../database/real_sqlite_manager.php';
        $sqlite = new RealSQLiteManager();
        
        $texts = [];
        $result = $sqlite->query("SELECT language_code, text_key, text_value, category FROM bot_texts ORDER BY language_code, category, text_key");
        
        foreach ($result as $row) {
            $language = $row['language_code'];
            $category = $row['category'];
            $key = str_replace($category . '.', '', $row['text_key']); // Убираем префикс категории
            
            if (!isset($texts[$language])) {
                $texts[$language] = [];
            }
            if (!isset($texts[$language][$category])) {
                $texts[$language][$category] = [];
            }
            
            $texts[$language][$category][$key] = $row['text_value'];
        }
        
        return $texts;
        
    } catch (Exception $e) {
        error_log("Failed to load bot texts from SQLite: " . $e->getMessage());
        return false;
    }
}

/**
 * Сохранение текстов бота в SQLite
 */
function saveBotTextsToSQLite($texts) {
    try {
        require_once __DIR__ . '/../database/real_sqlite_manager.php';
        $sqlite = new RealSQLiteManager();
        
        // Очищаем старые тексты
        $sqlite->query("DELETE FROM bot_texts");
        
        $savedCount = 0;
        
        // Сохраняем новые тексты
        foreach ($texts as $language => $sections) {
            if (is_array($sections)) {
                foreach ($sections as $section => $sectionTexts) {
                    if (is_array($sectionTexts)) {
                        foreach ($sectionTexts as $key => $value) {
                            if (is_string($value)) {
                                $fullKey = $section . '.' . $key;
                                $sql = "INSERT INTO bot_texts (language_code, text_key, text_value, category) VALUES (?, ?, ?, ?)";
                                $sqlite->query($sql, [$language, $fullKey, $value, $section]);
                                $savedCount++;
                            }
                        }
                    }
                }
            }
        }
        
        return $savedCount > 0;
        
    } catch (Exception $e) {
        error_log("Failed to save bot texts to SQLite: " . $e->getMessage());
        return false;
    }
}
?>
