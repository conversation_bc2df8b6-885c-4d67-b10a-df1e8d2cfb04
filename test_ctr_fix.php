<?php
require_once 'database/real_sqlite_manager.php';
$sqlite = new RealSQLiteManager();

echo "🔧 ТЕСТ CTR ИСПРАВЛЕНИЯ\n";
echo "=" . str_repeat("=", 30) . "\n\n";

// Получаем данные
$data = $sqlite->query("
    SELECT 
        ad_type,
        SUM(views) as views,
        SUM(clicks) as clicks,
        CASE 
            WHEN SUM(clicks) > 0 THEN ROUND((SUM(views) * 1.0 / SUM(clicks)), 4)
            ELSE 0 
        END as ctr_new,
        CASE 
            WHEN SUM(views) > 0 THEN ROUND((SUM(clicks) * 1.0 / SUM(views)), 4)
            ELSE 0 
        END as ctr_old
    FROM (
        SELECT 
            ad_type,
            COUNT(*) as views,
            0 as clicks
        FROM ad_views 
        WHERE ad_type != 'test_banner'
        GROUP BY ad_type
        
        UNION ALL
        
        SELECT 
            ad_type,
            0 as views,
            COUNT(*) as clicks
        FROM ad_clicks 
        WHERE ad_type != 'test_banner'
        GROUP BY ad_type
    ) combined
    GROUP BY ad_type
");

foreach ($data as $row) {
    $ctr_new_percent = round((float)$row['ctr_new'] * 100, 2);
    $ctr_old_percent = round((float)$row['ctr_old'] * 100, 2);
    
    echo "{$row['ad_type']}:\n";
    echo "  Просмотры: {$row['views']}\n";
    echo "  Клики: {$row['clicks']}\n";
    echo "  CTR СТАРЫЙ (клики/просмотры): {$ctr_old_percent}%\n";
    echo "  CTR НОВЫЙ (просмотры/клики): {$ctr_new_percent}%\n";
    echo "\n";
}

// Тест стран
echo "🌍 ТЕСТ СТРАН:\n";
$countries = $sqlite->query("
    SELECT ip_address, COUNT(*) as clicks 
    FROM ad_clicks 
    WHERE ad_type != 'test_banner' AND ip_address IS NOT NULL 
    GROUP BY ip_address 
    ORDER BY clicks DESC 
    LIMIT 10
");

require_once 'api/admin/ad_stats_api.php';

foreach ($countries as $row) {
    $code = getCountryCode($row['ip_address']);
    $name = getCountryName($code);
    echo "  {$row['ip_address']} ({$code}) - {$name}: {$row['clicks']} кликов\n";
}
?>
