<?php
/**
 * Прямой тест webhook без аутентификации для отладки
 */

require_once __DIR__ . '/../../includes/bot_config_loader.php';
defineBotConstants();

echo "<h1>🔧 Прямой тест webhook</h1>";

// Функция для тестирования webhook
function testWebhookDirect($botToken, $webhookUrl, $botName) {
    echo "<h2>Тестируем {$botName}</h2>";
    echo "<p><strong>Токен:</strong> " . substr($botToken, 0, 10) . "...</p>";
    echo "<p><strong>Webhook URL:</strong> {$webhookUrl}</p>";
    
    // 1. Проверяем токен бота
    echo "<h3>1. Проверка токена</h3>";
    $getMeUrl = "https://api.telegram.org/bot{$botToken}/getMe";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $getMeUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>❌ CURL ошибка: {$error}</p>";
        return false;
    }
    
    if ($httpCode !== 200) {
        echo "<p style='color: red;'>❌ HTTP ошибка: {$httpCode}</p>";
        return false;
    }
    
    $response = json_decode($result, true);
    if (!$response || !$response['ok']) {
        echo "<p style='color: red;'>❌ Неверный токен</p>";
        echo "<pre>" . htmlspecialchars($result) . "</pre>";
        return false;
    }
    
    $bot = $response['result'];
    echo "<p style='color: green;'>✅ Токен валиден: @{$bot['username']} ({$bot['first_name']})</p>";
    
    // 2. Проверяем доступность webhook URL
    echo "<h3>2. Проверка доступности URL</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Для локального тестирования
    curl_setopt($ch, CURLOPT_USERAGENT, 'TelegramBot/1.0');
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>❌ URL недоступен (CURL ошибка): {$error}</p>";
    } elseif ($httpCode >= 200 && $httpCode < 400) {
        echo "<p style='color: green;'>✅ URL доступен (HTTP {$httpCode})</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ URL возвращает HTTP {$httpCode}</p>";
    }
    
    // 3. Получаем текущую информацию о webhook
    echo "<h3>3. Текущий webhook</h3>";
    $webhookInfoUrl = "https://api.telegram.org/bot{$botToken}/getWebhookInfo";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookInfoUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($result && $httpCode === 200) {
        $webhookInfo = json_decode($result, true);
        if ($webhookInfo && $webhookInfo['ok']) {
            $info = $webhookInfo['result'];
            echo "<p><strong>Установленный URL:</strong> " . ($info['url'] ?: 'Не установлен') . "</p>";
            echo "<p><strong>Ожидающих обновлений:</strong> " . ($info['pending_update_count'] ?? 0) . "</p>";
            if (!empty($info['last_error_message'])) {
                echo "<p style='color: red;'><strong>Последняя ошибка:</strong> {$info['last_error_message']}</p>";
                echo "<p><strong>Дата ошибки:</strong> " . date('Y-m-d H:i:s', $info['last_error_date']) . "</p>";
            }
        }
    }
    
    // 4. Пытаемся установить webhook
    echo "<h3>4. Установка webhook</h3>";
    $setWebhookUrl = "https://api.telegram.org/bot{$botToken}/setWebhook";
    $data = [
        'url' => $webhookUrl,
        'allowed_updates' => ['message', 'callback_query'],
        'drop_pending_updates' => true
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $setWebhookUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>❌ CURL ошибка при установке: {$error}</p>";
        return false;
    }
    
    if ($httpCode !== 200) {
        echo "<p style='color: red;'>❌ HTTP ошибка при установке: {$httpCode}</p>";
        return false;
    }
    
    $response = json_decode($result, true);
    if (!$response) {
        echo "<p style='color: red;'>❌ Не удалось декодировать ответ</p>";
        echo "<pre>" . htmlspecialchars($result) . "</pre>";
        return false;
    }
    
    if ($response['ok']) {
        echo "<p style='color: green;'>✅ Webhook успешно установлен!</p>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ Ошибка установки webhook: {$response['description']}</p>";
        return false;
    }
}

// Тестируем основной бот
echo "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>";
$mainResult = testWebhookDirect(TELEGRAM_BOT_TOKEN, WEBHOOK_URL, "Основной бот");
echo "</div>";

// Тестируем бот поддержки
echo "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>";
$supportResult = testWebhookDirect(SUPPORT_BOT_TOKEN, SUPPORT_WEBHOOK_URL, "Бот поддержки");
echo "</div>";

// Итоги
echo "<h2>📊 Итоги</h2>";
echo "<p>Основной бот: " . ($mainResult ? "✅ OK" : "❌ Проблемы") . "</p>";
echo "<p>Бот поддержки: " . ($supportResult ? "✅ OK" : "❌ Проблемы") . "</p>";

if (!$mainResult || !$supportResult) {
    echo "<h3>🔧 Возможные решения:</h3>";
    echo "<ul>";
    echo "<li>Проверьте правильность токенов ботов</li>";
    echo "<li>Убедитесь, что URL доступны из интернета</li>";
    echo "<li>Проверьте SSL сертификат</li>";
    echo "<li>Убедитесь, что файлы webhook существуют и работают</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='bot_settings.php'>← Вернуться к настройкам бота</a></p>";
echo "<p><a href='webhook_diagnostics.php'>🔧 Полная диагностика</a></p>";
?>
