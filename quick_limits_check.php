<?php
require_once 'database/real_sqlite_manager.php';
$sqlite = new RealSQLiteManager();

echo "ПРОВЕРКА ЛИМИТОВ:\n";

// Проверяем таблицы
$tables = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table'");
echo "Таблицы в базе:\n";
foreach($tables as $table) {
    echo "- " . $table['name'] . "\n";
}

// Проверяем ad_limits
try {
    $limits = $sqlite->query("SELECT COUNT(*) as count FROM ad_limits");
    echo "\nЗаписей в ad_limits: " . $limits[0]['count'] . "\n";
    
    if ($limits[0]['count'] > 0) {
        $sample = $sqlite->query("SELECT * FROM ad_limits LIMIT 3");
        echo "Примеры:\n";
        foreach($sample as $s) {
            echo "- User {$s['user_id']}: {$s['ad_type']} = {$s['daily_count']}\n";
        }
    }
} catch (Exception $e) {
    echo "Ошибка с ad_limits: " . $e->getMessage() . "\n";
}

// Проверяем JSON
if (file_exists('database/ad_limits.json')) {
    $json = json_decode(file_get_contents('database/ad_limits.json'), true);
    echo "\nJSON лимиты:\n";
    if (isset($json['user_counts'])) {
        foreach($json['user_counts'] as $user => $counts) {
            echo "- {$user}: " . json_encode($counts) . "\n";
        }
    }
}
?>
