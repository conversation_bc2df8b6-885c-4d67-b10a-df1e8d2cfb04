<?php
/**
 * api/get_user_limits.php
 * API для получения лимитов пользователя из SQLite
 */

declare(strict_types=1);

header('Content-Type: application/json');

// Подключение зависимостей
if (!(@require_once __DIR__ . '/config.php')) { 
    http_response_code(500); 
    echo json_encode(['error' => 'Ошибка сервера: CFG']); 
    exit; 
}

if (!(@require_once __DIR__ . '/validate_initdata.php')) { 
    http_response_code(500); 
    echo json_encode(['error' => 'Ошибка сервера: VID']); 
    exit; 
}

if (!(@require_once __DIR__ . '/ad_limits_sqlite.php')) { 
    http_response_code(500); 
    echo json_encode(['error' => 'Ошибка сервера: ALS']); 
    exit; 
}

// Получение и валидация входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// Проверяем режим тестирования
$isTestMode = isset($input['test_mode']) && $input['test_mode'] === true;

if ($isTestMode && isset($input['user_id'])) {
    // Режим тестирования - используем переданный user_id
    $userId = intval($input['user_id']);
    error_log("get_user_limits: Режим тестирования для пользователя {$userId}");
} else {
    // Обычный режим - валидация через Telegram
    if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Нет данных авторизации']);
        exit;
    }

    // Валидация initData
    $validatedData = validateTelegramInitData($input['initData']);
    if ($validatedData === false) {
        http_response_code(403);
        echo json_encode(['error' => 'Неверные данные авторизации']);
        exit;
    }

    $userId = intval($validatedData['user']['id']);
}

try {
    $limitsManager = new AdLimitsSQLite();
    
    // Получаем лимиты пользователя
    $userLimits = $limitsManager->getUserLimits($userId);
    
    // Добавляем информацию о типах рекламы
    $adTypes = ['native_banner', 'rewarded_video', 'interstitial'];
    $limitsWithButtons = [];
    
    foreach ($adTypes as $adType) {
        $current = $limitsManager->getCurrentCount($userId, $adType);
        $remaining = $limitsManager->getRemainingCount($userId, $adType);
        $canShow = $limitsManager->canShowAd($userId, $adType);
        
        $limitsWithButtons[$adType] = [
            'current' => $current,
            'limit' => 20,
            'remaining' => $remaining,
            'can_show' => $canShow,
            'button_text' => $limitsManager->getButtonText($userId, $adType, 'Открыть ссылку'),
            'percentage' => $current > 0 ? round(($current / 20) * 100, 1) : 0
        ];
    }
    
    // Общая статистика
    $totalCurrent = array_sum(array_column($limitsWithButtons, 'current'));
    $totalLimit = count($adTypes) * 20;
    $totalRemaining = $totalLimit - $totalCurrent;
    
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'limits' => $limitsWithButtons,
        'summary' => [
            'total_current' => $totalCurrent,
            'total_limit' => $totalLimit,
            'total_remaining' => $totalRemaining,
            'total_percentage' => $totalLimit > 0 ? round(($totalCurrent / $totalLimit) * 100, 1) : 0
        ],
        'timestamp' => time(),
        'date' => date('Y-m-d')
    ]);

} catch (Exception $e) {
    error_log("get_user_limits ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка получения лимитов']);
}
?>
