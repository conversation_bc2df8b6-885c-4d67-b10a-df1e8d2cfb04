<?php
/**
 * api/get_localization.php
 * API endpoint для получения переводов из SQLite
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $language = $_GET['lang'] ?? 'ru';
    
    // Проверяем поддерживаемые языки
    if (!in_array($language, ['ru', 'en'])) {
        $language = 'ru';
    }
    
    $sqlite = new RealSQLiteManager();
    
    // Получаем тексты из SQLite
    $result = $sqlite->query("SELECT text_key, text_value, category FROM bot_texts WHERE language_code = ? ORDER BY category, text_key", [$language]);
    
    $translations = [];
    
    foreach ($result as $row) {
        $category = $row['category'];
        $key = $row['text_key'];
        $value = $row['text_value'];
        
        // Убираем префикс категории из ключа если есть
        if (strpos($key, $category . '.') === 0) {
            $key = substr($key, strlen($category) + 1);
        }
        
        if (!isset($translations[$category])) {
            $translations[$category] = [];
        }
        
        $translations[$category][$key] = $value;
    }
    
    // Если нет переводов в SQLite, загружаем из файла как fallback
    if (empty($translations)) {
        $localeFile = __DIR__ . '/../locales/' . $language . '.json';
        if (file_exists($localeFile)) {
            $fileData = json_decode(file_get_contents($localeFile), true);
            if ($fileData && isset($fileData['app'])) {
                $translations = $fileData['app'];
            }
        }
    }
    
    // Возвращаем в том же формате, что ожидает приложение
    echo json_encode([
        'app' => $translations
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // В случае ошибки возвращаем пустой объект
    echo json_encode(['app' => []], JSON_UNESCAPED_UNICODE);
}
?>
