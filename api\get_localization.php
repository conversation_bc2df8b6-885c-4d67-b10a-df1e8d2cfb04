<?php
/**
 * api/get_localization.php
 * API endpoint для получения переводов из SQLite
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $language = $_GET['lang'] ?? 'ru';

    // Проверяем поддерживаемые языки
    if (!in_array($language, ['ru', 'en'])) {
        $language = 'ru';
    }

    // Загружаем переводы из JSON файла
    $localeFile = __DIR__ . '/../locales/' . $language . '.json';
    $translations = [];

    if (file_exists($localeFile)) {
        $fileData = json_decode(file_get_contents($localeFile), true);
        if ($fileData && isset($fileData['app'])) {
            $translations = $fileData['app'];
        }
    }
    
    // Возвращаем в том же формате, что ожидает приложение
    echo json_encode([
        'app' => $translations
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // В случае ошибки возвращаем пустой объект
    echo json_encode(['app' => []], JSON_UNESCAPED_UNICODE);
}
?>
