<?php
/**
 * database/file_based_sqlite_emulator.php
 * Заглушка для совместимости (больше не используется, так как работает настоящая SQLite)
 */

declare(strict_types=1);

class FileBasedSQLiteEmulator
{
    private string $filePath;
    
    public function __construct(string $filePath)
    {
        $this->filePath = $filePath;
    }
    
    public function close(): void
    {
        // Заглушка - ничего не делаем
    }
    
    // Остальные методы - заглушки для совместимости
    public function __call($name, $arguments)
    {
        throw new Exception("FileBasedSQLiteEmulator больше не используется. Используется настоящая SQLite.");
    }
}
?>
