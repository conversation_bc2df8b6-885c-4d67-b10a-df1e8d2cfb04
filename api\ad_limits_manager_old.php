<?php

require_once __DIR__ . '/ads-config.php';
/**
 * api/ad_limits_manager.php
 * Менеджер лимитов показов рекламы с записью в JSON файл
 */

class AdLimitsManager {
    private $limitsFile;
    private $data;
    
    public function __construct() {
        $this->limitsFile = __DIR__ . '/../database/ad_limits.json';
        $this->loadData();
        $this->checkDailyReset();
    }
    
    /**
     * Загружает данные из JSON файла
     */
    private function loadData() {
        if (!file_exists($this->limitsFile)) {
            $this->createDefaultFile();
        }
        
        $jsonData = file_get_contents($this->limitsFile);
        $this->data = json_decode($jsonData, true);
        
        if (!$this->data) {
            $this->createDefaultFile();
            $jsonData = file_get_contents($this->limitsFile);
            $this->data = json_decode($jsonData, true);
        }
    }
    
    /**
     * Создает файл с данными по умолчанию
     */
    private function createDefaultFile() {
        // Получаем лимиты из config.php
        $defaultData = [
            'daily_limits' => $this->getConfigLimits(),
            'user_counts' => [],
            'last_reset_date' => gmdate('Y-m-d'), // Используем UTC время
            'version' => '1.1'
        ];

        $this->saveData($defaultData);
    }

    /**
     * Получает пользовательские лимиты из config.php
     */
    private function getConfigLimits() {
        // Подключаем config.php если еще не подключен
        if (!defined('USER_AD_LIMIT_NATIVE_BANNER')) {
            require_once __DIR__ . '/config.php';
        }

        return [
            'native_banner' => defined('USER_AD_LIMIT_NATIVE_BANNER') ? USER_AD_LIMIT_NATIVE_BANNER : 20,
            'rewarded_video' => defined('USER_AD_LIMIT_REWARDED_VIDEO') ? USER_AD_LIMIT_REWARDED_VIDEO : 20,
            'interstitial' => defined('USER_AD_LIMIT_INTERSTITIAL') ? USER_AD_LIMIT_INTERSTITIAL : 20
        ];
    }
    
    /**
     * Сохраняет данные в JSON файл
     */
    private function saveData($data = null) {
        if ($data === null) {
            $data = $this->data;
        }
        
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        // Создаем директорию если не существует
        $dir = dirname($this->limitsFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($this->limitsFile, $jsonData, LOCK_EX);
        $this->data = $data;
    }
    
    /**
     * Проверяет и выполняет ежедневный сброс лимитов (по UTC времени)
     */
    private function checkDailyReset() {
        // ИСПРАВЛЕНИЕ: Используем UTC время для корректного сброса в полночь UTC
        $todayUTC = gmdate('Y-m-d');
        $lastResetDate = $this->data['last_reset_date'] ?? '2024-01-01';

        if ($todayUTC !== $lastResetDate) {
            // Сбрасываем все счетчики пользователей
            $this->data['user_counts'] = [];
            $this->data['last_reset_date'] = $todayUTC;
            $this->saveData();

            error_log("[AdLimits] Ежедневный сброс лимитов выполнен по UTC: $todayUTC (предыдущий: $lastResetDate)");
        }
    }
    
    /**
     * Получает текущий счетчик пользователя для типа рекламы
     */
    public function getUserAdCount($userId, $adType) {
        $userKey = "user_$userId";
        return $this->data['user_counts'][$userKey][$adType] ?? 0;
    }
    
    /**
     * Увеличивает счетчик пользователя для типа рекламы
     */
    public function incrementUserAdCount($userId, $adType) {
        $userKey = "user_$userId";
        
        if (!isset($this->data['user_counts'][$userKey])) {
            $this->data['user_counts'][$userKey] = [];
        }
        
        $currentCount = $this->data['user_counts'][$userKey][$adType] ?? 0;
        $newCount = $currentCount + 1;
        
        $this->data['user_counts'][$userKey][$adType] = $newCount;
        $this->saveData();
        
        return $newCount;
    }
    
    /**
     * Получает оставшееся количество показов для пользователя
     */
    public function getRemainingCount($userId, $adType) {
        $currentCount = $this->getUserAdCount($userId, $adType);

        // Получаем актуальный лимит из config.php
        $configLimits = $this->getConfigLimits();
        $limit = $configLimits[$adType] ?? $this->data['daily_limits'][$adType] ?? 20;

        return max(0, $limit - $currentCount);
    }

    /**
     * Получает общее количество просмотров пользователя за день (все типы)
     */
    public function getTotalUserAdCount($userId) {
        $total = 0;
        $configLimits = $this->getConfigLimits();

        foreach ($configLimits as $adType => $limit) {
            $total += $this->getUserAdCount($userId, $adType);
        }

        return $total;
    }

    /**
     * Получает общий лимит для пользователя в день (сумма всех типов)
     */
    public function getTotalUserDailyLimit() {
        $configLimits = $this->getConfigLimits();
        return array_sum($configLimits);
    }

    /**
     * Получает оставшееся общее количество показов для пользователя
     */
    public function getTotalRemainingCount($userId) {
        $totalCurrent = $this->getTotalUserAdCount($userId);
        $totalLimit = $this->getTotalUserDailyLimit();

        return max(0, $totalLimit - $totalCurrent);
    }
    
    /**
     * Проверяет, достигнут ли лимит для пользователя
     */
    public function isLimitReached($userId, $adType) {
        return $this->getRemainingCount($userId, $adType) === 0;
    }
    
    /**
     * Получает полную информацию о лимитах пользователя
     */
    public function getUserLimitsInfo($userId) {
        $info = [];
        $configLimits = $this->getConfigLimits();

        // Информация по каждому типу рекламы
        foreach ($configLimits as $adType => $limit) {
            $current = $this->getUserAdCount($userId, $adType);
            $remaining = $this->getRemainingCount($userId, $adType);

            $info[$adType] = [
                'current' => $current,
                'limit' => $limit,
                'remaining' => $remaining,
                'isLimitReached' => $remaining === 0,
                'percentage' => $limit > 0 ? round(($current / $limit) * 100, 1) : 0
            ];
        }

        // Общая информация по всем типам для пользователя
        $totalCurrent = $this->getTotalUserAdCount($userId);
        $totalLimit = $this->getTotalUserDailyLimit();
        $totalRemaining = $this->getTotalRemainingCount($userId);

        $info['total'] = [
            'current' => $totalCurrent,
            'limit' => $totalLimit,
            'remaining' => $totalRemaining,
            'isLimitReached' => $totalRemaining === 0,
            'percentage' => $totalLimit > 0 ? round(($totalCurrent / $totalLimit) * 100, 1) : 0
        ];

        return $info;
    }
    
    /**
     * Сбрасывает счетчики пользователя (для тестирования)
     */
    public function resetUserCounters($userId) {
        $userKey = "user_$userId";
        unset($this->data['user_counts'][$userKey]);
        $this->saveData();
    }
    
    /**
     * Получает статистику по всем пользователям
     */
    public function getGlobalStats() {
        $stats = [
            'total_users' => count($this->data['user_counts']),
            'last_reset_date' => $this->data['last_reset_date'],
            'ad_types' => []
        ];
        
        foreach ($this->data['daily_limits'] as $adType => $limit) {
            $totalViews = 0;
            $usersWithViews = 0;
            
            foreach ($this->data['user_counts'] as $userCounts) {
                if (isset($userCounts[$adType]) && $userCounts[$adType] > 0) {
                    $totalViews += $userCounts[$adType];
                    $usersWithViews++;
                }
            }
            
            $stats['ad_types'][$adType] = [
                'total_views' => $totalViews,
                'users_with_views' => $usersWithViews,
                'daily_limit' => $limit
            ];
        }
        
        return $stats;
    }
}

// Обработка AJAX запросов (только если файл вызван напрямую)
if ((basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) &&
    ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET')) {
    header('Content-Type: application/json; charset=utf-8');

    try {
        $manager = new AdLimitsManager();
        $action = $_REQUEST['action'] ?? '';
        $userId = $_REQUEST['user_id'] ?? '';
        $adType = $_REQUEST['ad_type'] ?? '';
        
        switch ($action) {
            case 'get_user_limits':
                if (empty($userId)) {
                    throw new Exception('User ID is required');
                }
                
                $info = $manager->getUserLimitsInfo($userId);
                echo json_encode([
                    'success' => true,
                    'data' => $info
                ]);
                break;
                
            case 'increment_counter':
                if (empty($userId) || empty($adType)) {
                    throw new Exception('User ID and ad type are required');
                }
                
                $newCount = $manager->incrementUserAdCount($userId, $adType);
                $remaining = $manager->getRemainingCount($userId, $adType);
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'new_count' => $newCount,
                        'remaining' => $remaining,
                        'is_limit_reached' => $remaining === 0
                    ]
                ]);
                break;
                
            case 'check_limit':
                if (empty($userId) || empty($adType)) {
                    throw new Exception('User ID and ad type are required');
                }
                
                $isLimitReached = $manager->isLimitReached($userId, $adType);
                $remaining = $manager->getRemainingCount($userId, $adType);
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'is_limit_reached' => $isLimitReached,
                        'remaining' => $remaining
                    ]
                ]);
                break;
                
            case 'reset_user':
                if (empty($userId)) {
                    throw new Exception('User ID is required');
                }
                
                $manager->resetUserCounters($userId);
                echo json_encode([
                    'success' => true,
                    'message' => 'User counters reset successfully'
                ]);
                break;
                
            case 'get_stats':
                $stats = $manager->getGlobalStats();
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ]);
                break;
                
            default:
                throw new Exception('Unknown action');
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
