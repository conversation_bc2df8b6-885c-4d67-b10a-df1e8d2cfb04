// === withdrawal-manager.js ===
// Файл: js/withdrawal-manager.js
// Описание: Управляет историей выплат.

class WithdrawalManager {
  constructor() {
    this.isInitialized = false;
    this.withdrawals = [];
    this.statusMonitorInterval = null;
    this.forceUpdateInterval = null;
    this.autoRefreshInterval = null;
    this.lastUpdateTime = null;
    this.updateFrequency = 15000; // 15 секунд

    // Элементы интерфейса (из оригинала)
    this.elements = {
      historyContainer: document.querySelector('.placeholder-list') || document.querySelector('#withdrawal-history-list'),
      refreshHistoryButton: document.querySelector('.refresh-history-btn') || document.getElementById('refresh-history-button'),
      lastUpdateIndicator: document.querySelector('#last-update-time')
    };

    // API endpoints
    this.endpoints = {
      history: '/api/getWithdrawalHistory_sqlite.php',
      historyFallback: '/api/getWithdrawalHistory.php'
    };
  }

  init() {
    if (this.isInitialized) {
      console.log('[WithdrawalManager] Уже инициализирован.');
      return;
    }
    console.log('[WithdrawalManager] 🚀 Инициализация истории выплат с автообновлением...');
    this.setupEventListeners();
    this.loadAndDisplayHistory();
    this.startAutoRefresh();
    this.isInitialized = true;
  }

  /**
   * Остановка всех интервалов при уничтожении
   */
  destroy() {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
      this.autoRefreshInterval = null;
    }
    if (this.statusMonitorInterval) {
      clearInterval(this.statusMonitorInterval);
      this.statusMonitorInterval = null;
    }
    this.isInitialized = false;
    console.log('[WithdrawalManager] 🛑 Менеджер остановлен');
  }

  setupEventListeners() {
    if (this.elements.refreshHistoryButton) {
      this.elements.refreshHistoryButton.addEventListener('click', () => this.forceLoadHistory());
    }
  }

  /**
   * Запуск автоматического обновления каждые 15 секунд
   */
  startAutoRefresh() {
    console.log('[WithdrawalManager] ⏰ Запуск автообновления каждые', this.updateFrequency / 1000, 'секунд');

    // Очищаем предыдущий интервал если есть
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
    }

    // Запускаем новый интервал
    this.autoRefreshInterval = setInterval(() => {
      this.loadAndDisplayHistory(true); // true = silent update
    }, this.updateFrequency);
  }

  /**
   * Остановка автоматического обновления
   */
  stopAutoRefresh() {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
      this.autoRefreshInterval = null;
      console.log('[WithdrawalManager] ⏹️ Автообновление остановлено');
    }
  }

  /**
   * Обновление индикатора времени последнего обновления
   */
  updateLastUpdateIndicator() {
    this.lastUpdateTime = new Date();

    if (this.elements.lastUpdateIndicator) {
      const timeString = this.lastUpdateTime.toLocaleTimeString('ru-RU', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      this.elements.lastUpdateIndicator.textContent = `Обновлено: ${timeString}`;
    }
  }

  /**
   * Загружает и отображает историю выплат (обновленная SQLite версия)
   */
  async loadAndDisplayHistory(silent = false) {
    if (!silent) {
      console.log('[WithdrawalManager] 📡 Загружаем историю выплат из SQLite...');
    }

    let userData = this.getUserDataForAPI();

    if (!userData) {
      console.log('[WithdrawalManager] Нет данных пользователя - используем тестовые данные');
      userData = { user_id: '5880288830' }; // Тестовый ID
    }

    // Показываем индикатор загрузки только при не-silent обновлении
    if (!silent) {
      this.showLoadingState();
    }

    try {
      // Сначала пробуем новый SQLite API
      const response = await fetch(this.endpoints.history, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData)
      });

      if (response.ok) {
        const data = await response.json();

        if (!silent) {
          console.log('[WithdrawalManager] ✅ Ответ SQLite API:', data);
        }

        if (data.success && data.withdrawals && data.withdrawals.length > 0) {
          if (!silent) {
            console.log('[WithdrawalManager] 📋 Отображаем', data.withdrawals.length, 'выплат');
          }

          this.withdrawals = data.withdrawals;
          this.displayHistory(data.withdrawals, data.stats);
          this.updateLastUpdateIndicator();

        } else {
          if (!silent) {
            console.log('[WithdrawalManager] 📭 Нет выплат для отображения');
          }
          this.showEmptyHistory();
        }
      } else {
        throw new Error(`SQLite API HTTP ${response.status}`);
      }

    } catch (error) {
      console.warn('[WithdrawalManager] ⚠️ SQLite API недоступен, пробуем fallback:', error);

      // Fallback на старый API
      try {
        const fallbackResponse = await fetch(this.endpoints.historyFallback, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(userData)
        });

        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();

          if (fallbackData.withdrawals && fallbackData.withdrawals.length > 0) {
            console.log('[WithdrawalManager] ✅ Fallback API вернул', fallbackData.withdrawals.length, 'выплат');
            this.withdrawals = fallbackData.withdrawals;
            this.displayHistory(fallbackData.withdrawals);
            this.updateLastUpdateIndicator();
          } else {
            this.showEmptyHistory();
          }
        } else {
          throw new Error(`Fallback API HTTP ${fallbackResponse.status}`);
        }

      } catch (fallbackError) {
        console.error('[WithdrawalManager] ❌ Оба API недоступны:', fallbackError);

        if (!silent && window.appUtils) {
          window.appUtils.showStatus('Ошибка загрузки истории выплат', 'error');
        }
        this.showEmptyHistory();
      }
    }
  }

  /**
   * Отображает историю выплат (обновленная версия)
   */
  displayHistory(withdrawals, stats = null) {
    if (!this.elements.historyContainer) {
      console.warn("Не найден контейнер для истории выплат");
      return;
    }

    // Очищаем контейнер
    this.elements.historyContainer.innerHTML = "";

    // Добавляем индикатор последнего обновления
    this.addUpdateIndicator();

    // Добавляем статистику если есть
    if (stats) {
      this.addStatsDisplay(stats);
    }

    // Создаем карточки выплат
    withdrawals.forEach(withdrawal => {
      const withdrawalCard = this.createWithdrawalCard(withdrawal);
      this.elements.historyContainer.appendChild(withdrawalCard);
    });
  }

  /**
   * Добавляет индикатор времени последнего обновления
   */
  addUpdateIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'update-indicator';
    indicator.id = 'last-update-time';
    indicator.style.cssText = `
      text-align: center;
      font-size: 12px;
      color: var(--cyber-text-secondary, #888);
      margin-bottom: 15px;
      padding: 8px;
      background: rgba(0, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(0, 255, 255, 0.1);
    `;

    if (this.lastUpdateTime) {
      const timeString = this.lastUpdateTime.toLocaleTimeString('ru-RU', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      indicator.textContent = `Обновлено: ${timeString}`;
    } else {
      indicator.textContent = 'Загрузка...';
    }

    this.elements.historyContainer.appendChild(indicator);
    this.elements.lastUpdateIndicator = indicator;
  }

  /**
   * Добавляет отображение статистики
   */
  addStatsDisplay(stats) {
    if (!stats || Object.keys(stats).length === 0) return;

    const statsContainer = document.createElement('div');
    statsContainer.className = 'withdrawal-stats';
    statsContainer.style.cssText = `
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
      padding: 15px;
      background: rgba(0, 255, 255, 0.05);
      border-radius: 10px;
      border: 1px solid rgba(0, 255, 255, 0.1);
    `;

    for (const [status, data] of Object.entries(stats)) {
      const statItem = document.createElement('div');
      statItem.className = 'stat-item';
      statItem.style.cssText = `
        text-align: center;
        padding: 10px;
        background: rgba(255, 107, 53, 0.1);
        border-radius: 8px;
      `;

      statItem.innerHTML = `
        <div style="font-size: 16px; font-weight: bold; color: var(--cyber-accent-neon, #00ffff);">
          ${data.count}
        </div>
        <div style="font-size: 12px; color: var(--cyber-text-secondary, #888); margin-top: 2px;">
          ${this.getStatusText(status)}
        </div>
        <div style="font-size: 11px; color: var(--cyber-text-secondary, #888);">
          ${data.total_amount.toFixed(2)} TON
        </div>
      `;

      statsContainer.appendChild(statItem);
    }

    this.elements.historyContainer.appendChild(statsContainer);
  }

  /**
   * Создает карточку выплаты с актуальными данными
   */
  createWithdrawalCard(withdrawal) {
    const card = document.createElement('div');
    card.className = 'withdrawal-item history-item';
    card.setAttribute('data-withdrawal-id', withdrawal.id);
    card.setAttribute('data-status', withdrawal.status);

    // Определяем цвет статуса
    const statusColors = {
      'pending': '#ffc107',
      'processing': '#17a2b8',
      'completed': '#28a745',
      'failed': '#dc3545',
      'cancelled': '#6c757d',
      'confirming': '#007bff'
    };

    const statusColor = statusColors[withdrawal.status] || '#6c757d';

    card.style.cssText = `
      background: rgba(42, 42, 42, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-left: 4px solid ${statusColor};
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      transition: all 0.3s ease;
      position: relative;
    `;

    // Создаем содержимое карточки
    card.innerHTML = `
      <div class="withdrawal-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
        <div class="withdrawal-amount" style="font-size: 18px; font-weight: bold; color: var(--cyber-accent-neon, #00ffff);">
          ${withdrawal.amount} ${withdrawal.currency || 'TON'}
        </div>
        <div class="withdrawal-status ${withdrawal.status_class || ''}" style="
          background: ${statusColor}20;
          color: ${statusColor};
          padding: 4px 8px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: bold;
          border: 1px solid ${statusColor}40;
        ">
          ${withdrawal.status_text || withdrawal.status}
        </div>
      </div>

      <div class="withdrawal-details" style="font-size: 14px; color: var(--cyber-text-secondary, #888); line-height: 1.4;">
        <div style="margin-bottom: 6px;">
          <span style="color: var(--cyber-text-primary, #fff);">Кошелек:</span>
          <span style="font-family: monospace; word-break: break-all;">
            ${this.formatWalletAddress(withdrawal.wallet_address)}
          </span>
        </div>

        <div style="margin-bottom: 6px;">
          <span style="color: var(--cyber-text-primary, #fff);">Дата:</span>
          ${withdrawal.formatted_date || new Date(withdrawal.created_at).toLocaleDateString('ru-RU')}
          в ${withdrawal.formatted_time || new Date(withdrawal.created_at).toLocaleTimeString('ru-RU', {hour: '2-digit', minute: '2-digit'})}
        </div>

        ${withdrawal.transaction_hash ? `
          <div style="margin-bottom: 6px;">
            <span style="color: var(--cyber-text-primary, #fff);">Транзакция:</span>
            <span style="font-family: monospace; word-break: break-all; color: ${statusColor};">
              ${this.formatTransactionHash(withdrawal.transaction_hash)}
            </span>
          </div>
        ` : ''}

        ${withdrawal.network_fee ? `
          <div style="margin-bottom: 6px;">
            <span style="color: var(--cyber-text-primary, #fff);">Комиссия сети:</span>
            ${withdrawal.network_fee} ${withdrawal.currency || 'TON'}
          </div>
        ` : ''}

        ${withdrawal.final_amount && withdrawal.final_amount !== withdrawal.amount ? `
          <div style="margin-bottom: 6px;">
            <span style="color: var(--cyber-text-primary, #fff);">К получению:</span>
            <span style="color: var(--cyber-accent-neon, #00ffff); font-weight: bold;">
              ${withdrawal.final_amount} ${withdrawal.currency || 'TON'}
            </span>
          </div>
        ` : ''}

        ${withdrawal.admin_notes ? `
          <div style="margin-top: 8px; padding: 8px; background: rgba(255, 193, 7, 0.1); border-radius: 6px; border-left: 3px solid #ffc107;">
            <span style="color: #ffc107; font-weight: bold;">Примечание:</span>
            <div style="margin-top: 4px;">${withdrawal.admin_notes}</div>
          </div>
        ` : ''}
      </div>

      ${withdrawal.can_cancel ? `
        <div class="withdrawal-actions" style="margin-top: 12px; text-align: right;">
          <button onclick="window.withdrawalManager.cancelWithdrawal('${withdrawal.id}')"
                  style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 6px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: background 0.3s ease;
                  "
                  onmouseover="this.style.background='#c82333'"
                  onmouseout="this.style.background='#dc3545'">
            Отменить
          </button>
        </div>
      ` : ''}

      <div class="withdrawal-id" style="
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 10px;
        color: var(--cyber-text-secondary, #888);
        opacity: 0.7;
      ">
        #${withdrawal.id}
      </div>
    `;

    // Добавляем hover эффект
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-2px)';
      card.style.boxShadow = `0 4px 12px ${statusColor}30`;
    });

    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
      card.style.boxShadow = 'none';
    });

    return card;
  }

  /**
   * Форматирует адрес кошелька для отображения
   */
  formatWalletAddress(address) {
    if (!address) return 'Не указан';
    if (address.length <= 20) return address;
    return `${address.substring(0, 8)}...${address.substring(address.length - 8)}`;
  }

  /**
   * Форматирует хеш транзакции для отображения
   */
  formatTransactionHash(hash) {
    if (!hash) return 'Не указан';
    if (hash.length <= 20) return hash;
    return `${hash.substring(0, 10)}...${hash.substring(hash.length - 10)}`;
  }

  /**
   * Получает текст статуса на русском языке
   */
  getStatusText(status) {
    const statusTexts = {
      'pending': 'Ожидает',
      'processing': 'В обработке',
      'completed': 'Завершена',
      'failed': 'Ошибка',
      'cancelled': 'Отменена',
      'expired': 'Истекла',
      'confirming': 'Подтверждение',
      'confirmed': 'Подтверждена',
      'sending': 'Отправка'
    };

    return statusTexts[status] || status;
  }

  /**
   * Принудительная загрузка истории (для кнопки обновления)
   */
  async forceLoadHistory() {
    console.log('[WithdrawalManager] 🔄 Принудительное обновление истории...');
    await this.loadAndDisplayHistory(false);
  }

  /**
   * Отмена выплаты
   */
  async cancelWithdrawal(withdrawalId) {
    try {
      console.log('[WithdrawalManager] 🚫 Отмена выплаты:', withdrawalId);

      // Здесь должен быть API вызов для отмены выплаты
      // const response = await fetch('/api/cancelWithdrawal.php', { ... });

      // Пока что просто обновляем историю
      await this.loadAndDisplayHistory(false);

      if (window.appUtils) {
        window.appUtils.showStatus('Выплата отменена', 'success');
      }

    } catch (error) {
      console.error('[WithdrawalManager] Ошибка отмены выплаты:', error);

      if (window.appUtils) {
        window.appUtils.showStatus('Ошибка отмены выплаты', 'error');
      }
    }
  }

    if (withdrawals && withdrawals.length > 0) {
      // Создаем список выплат
      withdrawals.forEach(withdrawal => {
        const withdrawalItem = document.createElement("div");
        withdrawalItem.className = "withdrawal-item";

        // Определяем статус и его стиль
        const statusText = this.getStatusText(withdrawal.status);
        const statusClass = this.getStatusClass(withdrawal.status);

        // ИСПРАВЛЕНИЕ: Обрабатываем дату точно как в оригинале
        let date;
        if (withdrawal.timestamp) {
          date = new Date(withdrawal.timestamp * 1000);
        } else if (withdrawal.created_at) {
          date = new Date(withdrawal.created_at);
        } else {
          date = new Date();
        }
        const dateStr = date.toLocaleDateString('ru-RU');
        const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

        // ИСПРАВЛЕНИЕ: Используем правильные поля точно как в оригинале
        const coinsAmount = withdrawal.coins_amount || withdrawal.amount || 0;
        const currency = withdrawal.currency || 'unknown';
        const address = withdrawal.wallet_address || withdrawal.address || 'Адрес не указан';

        // ИСПРАВЛЕНИЕ: Создаем HTML точно как в оригинале
        withdrawalItem.innerHTML = `
          <div class="withdrawal-header">
            <span class="withdrawal-amount">${coinsAmount} ${window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет'}</span>
            <span class="withdrawal-status ${statusClass}">${statusText}</span>
          </div>
          <div class="withdrawal-details">
            <div class="withdrawal-currency">${currency.toUpperCase()}</div>
            <div class="withdrawal-date">${dateStr} ${timeStr}</div>
            ${withdrawal.payout_id ? `<div class="withdrawal-payout-id">ID: ${withdrawal.payout_id}</div>` : ''}
          </div>
          <div class="withdrawal-address">${address}</div>
          <div class="admin-controls">
              <select class="status-select" data-withdrawal-id="${withdrawal.id}" data-user-id="${withdrawal.user_id}">
                  <option value="waiting" ${withdrawal.status === 'waiting' ? 'selected' : ''}>Ожидание</option>
                  <option value="processing" ${withdrawal.status === 'processing' ? 'selected' : ''}>В обработке</option>
                  <option value="finished" ${withdrawal.status === 'finished' ? 'selected' : ''}>Завершено</option>
                  <option value="failed" ${withdrawal.status === 'failed' ? 'selected' : ''}>Ошибка</option>
                  <option value="cancelled" ${withdrawal.status === 'cancelled' ? 'selected' : ''}>Отменено</option>
              </select>
              <button class="save-status-btn" data-withdrawal-id="${withdrawal.id}" data-user-id="${withdrawal.user_id}">Сохранить</button>
          </div>
        `;

        this.elements.historyContainer.appendChild(withdrawalItem);
      });

      // Добавляем обработчики событий для новых элементов
      this.elements.historyContainer.querySelectorAll('.save-status-btn').forEach(button => {
          button.addEventListener('click', (event) => {
              const withdrawalId = event.target.dataset.withdrawalId;
              const userId = event.target.dataset.userId;
              const select = this.elements.historyContainer.querySelector(`.status-select[data-withdrawal-id="${withdrawalId}"]`);
              const newStatus = select.value;
              this.updateWithdrawalStatus(userId, withdrawalId, newStatus);
          });
      });
    } else {
      this.showEmptyHistory();
    }
  }

  async updateWithdrawalStatus(userId, withdrawalId, newStatus) {
      console.log(`Updating status for withdrawal ${withdrawalId} of user ${userId} to ${newStatus}`);
      try {
          const response = await fetch(`${window.API_BASE_URL}/admin/update_withdrawal_status.php`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ 
                  user_id: userId,
                  withdrawal_id: withdrawalId,
                  new_status: newStatus
              })
          });

          const data = await response.json();

          if (data.success) {
              console.log("Status updated successfully");
              if (window.appUtils) {
                  window.appUtils.showStatus('Статус успешно обновлен', 'success');
              }
              this.loadAndDisplayHistory(); // Перезагружаем историю для отображения изменений
          } else {
              throw new Error(data.error || 'Unknown error');
          }
      } catch (error) {
          console.error('Failed to update withdrawal status:', error);
          if (window.appUtils) {
              window.appUtils.showStatus(`Ошибка обновления статуса: ${error.message}`, 'error');
          }
      }
  }

  /**
   * Показывает пустую историю с красивым дизайном
   */
  showEmptyHistory() {
    if (this.elements.historyContainer) {
      this.elements.historyContainer.innerHTML = `
        <div class="empty-history-state">
          <div class="empty-history-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M12 2v10"/>
              <path d="M8 8l4-4 4 4"/>
              <rect x="3" y="14" width="18" height="6" rx="2"/>
              <circle cx="7" cy="17" r="1"/>
              <circle cx="17" cy="17" r="1"/>
            </svg>
          </div>
          <div class="empty-history-content">
            <h4 class="empty-history-title">${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_title') : 'История выплат пуста'}</h4>
            <p class="empty-history-description">
              ${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_description') : 'Ваши запросы на вывод будут отображаться здесь'}
            </p>
            <div class="empty-history-hint">
              <span class="hint-icon">💡</span>
              <span>${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_hint') : 'Перейдите в калькулятор, чтобы рассчитать и запросить первый вывод'}</span>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * Получает текст статуса выплаты на русском языке (из оригинала)
   */
  getStatusText(status) {
    if (window.appLocalization) {
      const statusTranslations = {
        'waiting': 'withdrawal_status.waiting',
        'processing': 'withdrawal_status.processing',
        'sending': 'withdrawal_status.sending',
        'finished': 'withdrawal_status.finished',
        'completed': 'withdrawal_status.completed',
        'confirmed': 'withdrawal_status.confirmed',
        'failed': 'withdrawal_status.failed',
        'rejected': 'withdrawal_status.rejected',
        'pending': 'withdrawal_status.pending',
        'cancelled': 'withdrawal_status.cancelled',
        'expired': 'withdrawal_status.expired'
      };

      const translationKey = statusTranslations[status];
      if (translationKey) {
        return window.appLocalization.get(translationKey);
      }
    }

    // Fallback статусы
    const statusMap = {
      'waiting': 'Ожидание обработки',
      'processing': 'Обрабатывается',
      'sending': 'Отправляется на кошелек',
      'finished': 'Отправлено на кошелек',
      'completed': 'Отправлено на кошелек',
      'confirmed': 'Подтверждено в блокчейне',
      'failed': 'Ошибка выплаты',
      'rejected': 'Отклонено системой',
      'pending': 'В обработке',
      'cancelled': 'Отменено',
      'expired': 'Истекло'
    };
    return statusMap[status] || status || 'Неизвестно';
  }

  /**
   * Получает CSS класс для статуса (из оригинала)
   */
  getStatusClass(status) {
    const classMap = {
      // Определяем CSS класс на основе статуса (из оригинала)
      'waiting': 'status-pending',
      'pending': 'status-pending',
      'processing': 'status-processing',
      'sending': 'status-processing',
      'completed': 'status-completed',
      'finished': 'status-completed',
      'confirmed': 'status-completed',
      'failed': 'status-failed',
      'cancelled': 'status-failed',
      'rejected': 'status-failed',
      'expired': 'status-expired'
    };
    return classMap[status] || 'status-unknown';
  }
  /**
   * Принудительная загрузка истории (для кнопки в интерфейсе) (из оригинала)
   */
  forceLoadHistory() {
    console.log('[WithdrawalManager] Принудительная загрузка истории выплат');

    // Показываем индикатор загрузки
    if (this.elements.historyContainer) {
      this.elements.historyContainer.innerHTML = '<div class="history-item placeholder">🔄 Загрузка истории выплат...</div>';
    }

    // Принудительно загружаем историю
    this.loadAndDisplayHistory();

    // Дополнительно через 2 секунды
    setTimeout(() => {
      console.log('[WithdrawalManager] Повторная загрузка через 2 секунды');
      this.loadAndDisplayHistory();
    }, 2000);
  }

  /**
   * Автоматически проверяет статусы выплат каждые 30 секунд (из оригинала)
   */
  startStatusMonitoring() {
    console.log('[WithdrawalManager] Запуск мониторинга статусов выплат');

    // Проверяем сразу при запуске
    this.checkWithdrawalStatusUpdates();

    // Затем проверяем каждые 30 секунд
    this.statusMonitorInterval = setInterval(() => {
      this.checkWithdrawalStatusUpdates();
    }, 30000);

    // Дополнительно проверяем каждые 5 минут через принудительное обновление
    this.forceUpdateInterval = setInterval(() => {
      console.log('[WithdrawalManager] Принудительное обновление статусов через API');
      this.forceUpdateWithdrawalStatuses();
    }, 300000); // 5 минут
  }

  /**
   * Проверяет обновления статусов выплат (из оригинала)
   */
  async checkWithdrawalStatusUpdates() {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const response = await fetch(`${window.API_BASE_URL}/checkUserWithdrawals.php`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": window.API_KEY || "default_key"
          },
          signal: controller.signal,
          body: JSON.stringify({
            initData: window.Telegram?.WebApp?.initData,
            timestamp: Date.now()
          })
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('[WithdrawalManager] Ошибка API:', {
            status: response.status,
            url: response.url,
            error: errorText
          });

          if (response.status === 500) {
            if (window.appUtils) {
              window.appUtils.showStatus('Временные проблемы с сервером', 'warning');
            }
          }
          return 0;
        }

        const data = await response.json();
        if (!data || typeof data !== 'object') {
          throw new Error('Некорректный формат ответа сервера');
        }

        if (data.success) {
          console.log(`[WithdrawalManager] Проверено выплат: ${data.checked}, обновлено: ${data.updated}`);

          if (data.updated > 0) {
            setTimeout(() => this.loadAndDisplayHistory(), 1000);
            if (window.appUtils) {
              window.appUtils.showStatus(`Обновлено статусов: ${data.updated}`, 'success');
            }
          }
          return data.updated;
        } else {
          console.warn('[WithdrawalManager] Ошибка API:', data.error);
          return 0;
        }
      } catch (error) {
        clearTimeout(timeoutId);
        console.warn('[WithdrawalManager] Ошибка сети:', error.message);
        if (window.appUtils) {
          window.appUtils.showStatus('Оффлайн режим: данные могут быть неактуальны', 'warning');
        }
        return 0;
      }
    } catch (error) {
      console.warn('[WithdrawalManager] Внешняя ошибка:', error.message);
      return 0;
    }
  }

  /**
   * Принудительно обновляет статусы выплат (из оригинала)
   */
  async forceUpdateWithdrawalStatuses() {
    try {
      const response = await fetch(`${window.API_BASE_URL}/forceUpdateWithdrawalStatuses.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ initData: window.Telegram?.WebApp?.initData })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[WithdrawalManager] Принудительное обновление статусов:', data);

        // Если есть обновления, перезагружаем историю
        if (data.updated && data.updated > 0) {
          console.log(`[WithdrawalManager] Принудительное обновление: найдено ${data.updated} изменений статусов`);
          setTimeout(() => this.loadAndDisplayHistory(), 1000);

          if (window.appUtils) {
            window.appUtils.showStatus(`Принудительно обновлено статусов: ${data.updated}`, 'success');
          }
        }

        return data;
      } else {
        console.warn('[WithdrawalManager] API принудительного обновления недоступен (HTTP:', response.status, ')');
      }
    } catch (error) {
      console.warn('[WithdrawalManager] API принудительного обновления недоступен:', error.message);
    }
    return null;
  }

  /**
   * Получает данные пользователя для API (из оригинала)
   */
  getUserDataForAPI() {
    const initData = window.Telegram?.WebApp?.initData;
    if (initData) {
      return { initData };
    }
    return null;
  }

  /**
   * Останавливает мониторинг статусов
   */
  stopStatusMonitoring() {
    if (this.statusMonitorInterval) {
      clearInterval(this.statusMonitorInterval);
      this.statusMonitorInterval = null;
    }
    if (this.forceUpdateInterval) {
      clearInterval(this.forceUpdateInterval);
      this.forceUpdateInterval = null;
    }
  }

  // Методы для обратной совместимости
  renderHistory(withdrawals) {
    this.displayHistory(withdrawals);
  }

  getWithdrawalStatusText(status) {
    return this.getStatusText(status);
  }

  async checkAndUpdateStatuses(initData) {
    return this.checkWithdrawalStatusUpdates();
  }
}

window.withdrawalManager = new WithdrawalManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.loadAndDisplayWithdrawalHistory = () => window.withdrawalManager.loadAndDisplayHistory();
window.displayWithdrawalHistory = (withdrawals) => window.withdrawalManager.displayHistory(withdrawals);
window.startWithdrawalStatusMonitoring = () => window.withdrawalManager.startStatusMonitoring();
window.checkWithdrawalStatusUpdates = () => window.withdrawalManager.checkWithdrawalStatusUpdates();
window.forceLoadHistory = () => window.withdrawalManager.forceLoadHistory();
window.getWithdrawalStatusText = (status) => window.withdrawalManager.getStatusText(status);

console.log('📜 [WithdrawalManager] Менеджер истории выплат загружен с полной интеграцией.');