<?php
/**
 * api/transaction_manager.php
 * Расширенный менеджер транзакций с дополнительными функциями
 */

declare(strict_types=1);

class TransactionManager {
    private RealSQLiteManager $db;
    
    public function __construct() {
        require_once __DIR__ . '/../database/real_sqlite_manager.php';
        $this->db = new RealSQLiteManager();
    }
    
    /**
     * Получить детальную информацию о транзакции
     */
    public function getTransactionDetails(int $transactionId, string $userId): ?array {
        $result = $this->db->query(
            "SELECT * FROM coin_transactions WHERE id = ? AND user_id = ?",
            [$transactionId, $userId]
        );
        
        if (empty($result)) {
            return null;
        }
        
        $transaction = $result[0];
        
        return [
            'id' => (int)$transaction['id'],
            'user_id' => $transaction['user_id'],
            'type' => $transaction['transaction_type'],
            'amount' => (float)$transaction['amount'],
            'operation' => $transaction['operation'],
            'balance_before' => (float)$transaction['balance_before'],
            'balance_after' => (float)$transaction['balance_after'],
            'source_type' => $transaction['source_type'],
            'source_id' => $transaction['source_id'] ? (int)$transaction['source_id'] : null,
            'description' => $transaction['description'],
            'metadata' => $transaction['metadata'] ? json_decode($transaction['metadata'], true) : null,
            'created_at' => $transaction['created_at'],
            'formatted_date' => date('d.m.Y H:i:s', strtotime($transaction['created_at'])),
            'amount_display' => ($transaction['operation'] === 'credit' ? '+' : '-') . $transaction['amount']
        ];
    }
    
    /**
     * Получить транзакции по источнику
     */
    public function getTransactionsBySource(string $userId, string $sourceType, ?int $sourceId = null, int $limit = 50): array {
        $params = [$userId, $sourceType];
        $sourceCondition = '';
        
        if ($sourceId !== null) {
            $sourceCondition = ' AND source_id = ?';
            $params[] = $sourceId;
        }
        
        $result = $this->db->query(
            "SELECT * FROM coin_transactions 
             WHERE user_id = ? AND source_type = ? {$sourceCondition}
             ORDER BY created_at DESC 
             LIMIT ?",
            array_merge($params, [$limit])
        );
        
        return array_map([$this, 'formatTransaction'], $result);
    }
    
    /**
     * Получить сводку по дням
     */
    public function getDailySummary(string $userId, int $days = 30): array {
        $result = $this->db->query(
            "SELECT 
                date(created_at) as date,
                COUNT(*) as transactions_count,
                SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as earned,
                SUM(CASE WHEN operation = 'debit' THEN amount ELSE 0 END) as spent,
                COUNT(CASE WHEN transaction_type = 'earn' THEN 1 END) as earn_count,
                COUNT(CASE WHEN transaction_type = 'withdraw' THEN 1 END) as withdraw_count
             FROM coin_transactions 
             WHERE user_id = ? AND date(created_at) >= date('now', '-{$days} days')
             GROUP BY date(created_at)
             ORDER BY date(created_at) DESC",
            [$userId]
        );
        
        $summary = [];
        foreach ($result as $row) {
            $summary[] = [
                'date' => $row['date'],
                'formatted_date' => date('d.m.Y', strtotime($row['date'])),
                'day_of_week' => date('l', strtotime($row['date'])),
                'transactions_count' => (int)$row['transactions_count'],
                'earned' => (float)$row['earned'],
                'spent' => (float)$row['spent'],
                'net' => (float)$row['earned'] - (float)$row['spent'],
                'earn_count' => (int)$row['earn_count'],
                'withdraw_count' => (int)$row['withdraw_count']
            ];
        }
        
        return $summary;
    }
    
    /**
     * Получить статистику по типам рекламы
     */
    public function getAdTypeStats(string $userId, int $days = 30): array {
        // Получаем статистику из ad_views
        $adViewsStats = $this->db->query(
            "SELECT 
                ad_type,
                COUNT(*) as views_count,
                SUM(reward) as total_reward,
                AVG(reward) as avg_reward
             FROM ad_views 
             WHERE user_id = ? AND date(timestamp) >= date('now', '-{$days} days')
             GROUP BY ad_type
             ORDER BY total_reward DESC",
            [$userId]
        );
        
        // Получаем статистику из транзакций
        $transactionStats = $this->db->query(
            "SELECT 
                ct.metadata,
                COUNT(*) as transaction_count,
                SUM(ct.amount) as total_amount
             FROM coin_transactions ct
             WHERE ct.user_id = ? 
               AND ct.source_type = 'ad_view' 
               AND ct.operation = 'credit'
               AND date(ct.created_at) >= date('now', '-{$days} days')
             GROUP BY ct.metadata",
            [$userId]
        );
        
        $stats = [];
        
        // Обрабатываем статистику просмотров
        foreach ($adViewsStats as $stat) {
            $adType = $stat['ad_type'];
            $stats[$adType] = [
                'ad_type' => $adType,
                'views_count' => (int)$stat['views_count'],
                'total_reward' => (float)$stat['total_reward'],
                'avg_reward' => round((float)$stat['avg_reward'], 2),
                'transaction_count' => 0,
                'transaction_amount' => 0
            ];
        }
        
        // Добавляем статистику транзакций
        foreach ($transactionStats as $stat) {
            $metadata = $stat['metadata'] ? json_decode($stat['metadata'], true) : null;
            $adType = $metadata['ad_type'] ?? 'unknown';
            
            if (isset($stats[$adType])) {
                $stats[$adType]['transaction_count'] = (int)$stat['transaction_count'];
                $stats[$adType]['transaction_amount'] = (float)$stat['total_amount'];
            }
        }
        
        return array_values($stats);
    }
    
    /**
     * Проверить подозрительную активность
     */
    public function checkSuspiciousActivity(string $userId): array {
        $checks = [];
        
        // 1. Слишком много транзакций за короткое время
        $recentTransactions = $this->db->query(
            "SELECT COUNT(*) as count FROM coin_transactions 
             WHERE user_id = ? AND created_at >= datetime('now', '-1 hour')",
            [$userId]
        )[0]['count'];
        
        $checks['high_frequency'] = [
            'suspicious' => $recentTransactions > 50,
            'count' => (int)$recentTransactions,
            'threshold' => 50,
            'description' => 'Слишком много транзакций за час'
        ];
        
        // 2. Одинаковые суммы подряд
        $duplicateAmounts = $this->db->query(
            "SELECT amount, COUNT(*) as count 
             FROM coin_transactions 
             WHERE user_id = ? AND created_at >= datetime('now', '-24 hours')
             GROUP BY amount 
             HAVING COUNT(*) > 10
             ORDER BY count DESC
             LIMIT 1",
            [$userId]
        );
        
        $checks['duplicate_amounts'] = [
            'suspicious' => !empty($duplicateAmounts),
            'max_duplicates' => !empty($duplicateAmounts) ? (int)$duplicateAmounts[0]['count'] : 0,
            'threshold' => 10,
            'description' => 'Много транзакций с одинаковой суммой'
        ];
        
        // 3. Транзакции в необычное время
        $nightTransactions = $this->db->query(
            "SELECT COUNT(*) as count FROM coin_transactions 
             WHERE user_id = ? 
               AND strftime('%H', created_at) BETWEEN '02' AND '05'
               AND date(created_at) >= date('now', '-7 days')",
            [$userId]
        )[0]['count'];
        
        $checks['night_activity'] = [
            'suspicious' => $nightTransactions > 20,
            'count' => (int)$nightTransactions,
            'threshold' => 20,
            'description' => 'Много транзакций ночью (02:00-05:00)'
        ];
        
        // Общая оценка подозрительности
        $suspiciousCount = 0;
        foreach ($checks as $check) {
            if ($check['suspicious']) {
                $suspiciousCount++;
            }
        }
        
        return [
            'user_id' => $userId,
            'is_suspicious' => $suspiciousCount > 0,
            'suspicious_count' => $suspiciousCount,
            'risk_level' => $suspiciousCount >= 2 ? 'high' : ($suspiciousCount === 1 ? 'medium' : 'low'),
            'checks' => $checks,
            'checked_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Экспорт транзакций в CSV
     */
    public function exportTransactionsCSV(string $userId, ?string $dateFrom = null, ?string $dateTo = null): string {
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if ($dateFrom) {
            $whereConditions[] = 'date(created_at) >= ?';
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereConditions[] = 'date(created_at) <= ?';
            $params[] = $dateTo;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $transactions = $this->db->query(
            "SELECT * FROM coin_transactions WHERE {$whereClause} ORDER BY created_at DESC",
            $params
        );
        
        $csv = "ID,Дата,Тип,Операция,Сумма,Баланс до,Баланс после,Источник,Описание\n";
        
        foreach ($transactions as $transaction) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%.2f,%.2f,%.2f,%s,\"%s\"\n",
                $transaction['id'],
                $transaction['created_at'],
                $transaction['transaction_type'],
                $transaction['operation'],
                $transaction['amount'],
                $transaction['balance_before'],
                $transaction['balance_after'],
                $transaction['source_type'] ?? '',
                str_replace('"', '""', $transaction['description'])
            );
        }
        
        return $csv;
    }
    
    /**
     * Форматировать транзакцию для API
     */
    private function formatTransaction(array $transaction): array {
        return [
            'id' => (int)$transaction['id'],
            'type' => $transaction['transaction_type'],
            'amount' => (float)$transaction['amount'],
            'operation' => $transaction['operation'],
            'balance_before' => (float)$transaction['balance_before'],
            'balance_after' => (float)$transaction['balance_after'],
            'source_type' => $transaction['source_type'],
            'source_id' => $transaction['source_id'] ? (int)$transaction['source_id'] : null,
            'description' => $transaction['description'],
            'metadata' => $transaction['metadata'] ? json_decode($transaction['metadata'], true) : null,
            'created_at' => $transaction['created_at'],
            'formatted_date' => date('d.m.Y H:i', strtotime($transaction['created_at'])),
            'amount_display' => ($transaction['operation'] === 'credit' ? '+' : '-') . $transaction['amount']
        ];
    }
}
?>
