// === balance-manager.js ===
// Файл: js/balance-manager.js
// Описание: Управляет состоянием и отображением баланса пользователя.

class BalanceManager {
  constructor() {
    this._currentUserBalance = 0;
    this._availableBalance = 0;
    this._reservedBalance = 0;
    this._totalEarned = 0;
    this._totalWithdrawn = 0;
    this._balanceUSD = 0;
    this._dailyStats = {};
    this._withdrawalSettings = {};
    this._lastUpdateTime = 0;

    this.elements = {
      balanceAmount: document.getElementById("balance-amount"),
      earnBalanceAmount: document.getElementById("earn-balance-amount"),
      availableWithdrawal: document.getElementById("available-withdrawal"),
      calcBalance: document.getElementById("calc-balance"),
    };

    // Защищенная переменная баланса с валидацией (из оригинала)
    Object.defineProperty(window, 'currentUserBalance', {
      get: () => this._currentUserBalance,
      set: (value) => {
        // Предупреждение о попытке изменения баланса (из оригинала)
        if (typeof value === 'number' && value !== this._currentUserBalance) {
          console.warn('🚨 БЕЗОПАСНОСТЬ: Попытка изменения баланса через консоль заблокирована!');
          console.warn('💡 Баланс можно изменить только через серверные операции.');
          return;
        }
        this._currentUserBalance = value;
      },
      configurable: false,
      enumerable: true
    });
  }

  init(initialBalance = 0) {
    console.log('[BalanceManager] Инициализация.');
    this.updateBalance(initialBalance, 'init');
  }

  /**
   * Загрузить баланс с сервера (новый SQLite API)
   */
  async loadBalanceFromServer() {
    try {
      console.log('[BalanceManager] 📡 Загрузка баланса с сервера...');

      if (!window.balanceApiClient) {
        throw new Error('BalanceApiClient не загружен');
      }

      const data = await window.balanceApiClient.getUserBalance();

      if (data.success) {
        // Обновляем все данные о балансе
        this._currentUserBalance = data.balance || 0;
        this._availableBalance = data.available_balance || 0;
        this._reservedBalance = data.reserved_balance || 0;
        this._totalEarned = data.total_earned || 0;
        this._totalWithdrawn = data.total_withdrawn || 0;
        this._balanceUSD = data.balance_usd || 0;
        this._dailyStats = data.daily_stats || {};
        this._withdrawalSettings = data.withdrawal_settings || {};
        this._lastUpdateTime = Date.now();

        // Обновляем отображение
        this.updateBalance(this._currentUserBalance, 'server_load');

        // Обновляем дополнительную информацию
        this.updateAdditionalInfo(data);

        console.log('[BalanceManager] ✅ Баланс загружен с сервера:', {
          balance: this._currentUserBalance,
          available: this._availableBalance,
          reserved: this._reservedBalance,
          earned_today: this._dailyStats.earned_today
        });

        return data;
      } else {
        throw new Error('Сервер вернул ошибку');
      }

    } catch (error) {
      console.error('[BalanceManager] ❌ Ошибка загрузки баланса:', error);

      // Показываем уведомление пользователю
      if (window.showStatus) {
        window.showStatus('Ошибка загрузки баланса', 'error');
      }

      throw error;
    }
  }

  /**
   * Обновить дополнительную информацию в интерфейсе
   */
  updateAdditionalInfo(data) {
    try {
      // Обновляем доступный баланс для вывода (весь баланс доступен)
      if (this.elements.availableWithdrawal) {
        this.elements.availableWithdrawal.textContent = this.formatBalance(this._currentUserBalance);
      }

      // Обновляем баланс в калькуляторе (весь баланс доступен)
      if (this.elements.calcBalance) {
        this.elements.calcBalance.textContent = this.formatBalance(this._currentUserBalance);
      }

      // Обновляем общую сумму заработанных монет
      const totalEarnedEl = document.getElementById('total-earned');
      if (totalEarnedEl) {
        totalEarnedEl.textContent = this.formatBalance(this._totalEarned);
      }

      // Обновляем информацию о дневном лимите
      const dailyProgressEl = document.getElementById('daily-progress');
      if (dailyProgressEl && this._dailyStats.limit) {
        const progressPercent = Math.round((this._dailyStats.earned_today / this._dailyStats.limit) * 100);
        dailyProgressEl.textContent = `${this._dailyStats.earned_today}/${this._dailyStats.limit} (${progressPercent}%)`;
      }

      // Обновляем USD эквивалент
      const balanceUsdEl = document.getElementById('balance-usd');
      if (balanceUsdEl) {
        balanceUsdEl.textContent = `$${this._balanceUSD}`;
      }

      // Обновляем информацию о возможности вывода
      const withdrawalStatusEl = document.getElementById('withdrawal-status');
      if (withdrawalStatusEl) {
        const canWithdraw = this._withdrawalSettings.can_withdraw || false;
        withdrawalStatusEl.textContent = canWithdraw ? 'Доступен' : 'Недоступен';
        withdrawalStatusEl.className = canWithdraw ? 'status-available' : 'status-unavailable';
      }

    } catch (error) {
      console.warn('[BalanceManager] Ошибка обновления дополнительной информации:', error);
    }
  }

  updateBalance(newBalance, source = 'unknown') {
    // Безопасное обновление баланса (только для серверных операций)
    const balance = parseInt(newBalance, 10) || 0;
    if (this._currentUserBalance === balance) return;

    console.log(`[BalanceManager] Обновление баланса из "${source}": ${this._currentUserBalance} -> ${balance}`);
    this._currentUserBalance = balance;

    // Обновляем все элементы баланса
    const formattedBalance = balance.toLocaleString();

    if (this.elements.balanceAmount) {
      this.elements.balanceAmount.textContent = formattedBalance;
    }

    if (this.elements.earnBalanceAmount) {
      this.elements.earnBalanceAmount.textContent = formattedBalance;
    }

    if (this.elements.availableWithdrawal) {
      this.elements.availableWithdrawal.textContent = formattedBalance;
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      console.log(`💰 Элемент "Доступно для вывода" обновлен: ${formattedBalance} ${coinsText}`);
    } else {
      console.warn('⚠️ Элемент "available-withdrawal" не найден!');
    }

    // Обновляем баланс в калькуляторе
    if (this.elements.calcBalance) {
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      this.elements.calcBalance.innerHTML = `${formattedBalance} <span data-translate="currency.coins">${coinsText}</span>`;
    }

    // Обновляем расчеты в калькуляторе, если есть введенная сумма
    const calcAmountInput = document.getElementById('calc-amount');
    if (calcAmountInput && calcAmountInput.value) {
      const amount = parseInt(calcAmountInput.value) || 0;
      if (window.calculatorManager && window.calculatorManager.updateDisplay) {
        window.calculatorManager.updateDisplay(amount);
      }
      if (window.calculatorManager && window.calculatorManager.updateBalanceCheck) {
        window.calculatorManager.updateBalanceCheck(amount);
      }
    }

    // Повторно валидируем основную форму вывода, так как изменение баланса может повлиять на ее валидность
    if (window.withdrawalFormManager && window.withdrawalFormManager.validateForm) {
      window.withdrawalFormManager.validateForm();
    }

    // Отправляем событие для других модулей
    window.dispatchEvent(new CustomEvent('balanceChanged', {
      detail: { newBalance: balance, source: source }
    }));

    console.log(`🎯 Баланс обновлен: ${formattedBalance}`);
  }

  getCurrentBalance() {
    return this._currentUserBalance;
  }

  getBalance() {
    return this._currentUserBalance;
  }

  addCoins(amount, source = 'unknown') {
    const newBalance = this._currentUserBalance + amount;
    this.updateBalance(newBalance, source);
  }

  // Проверка достаточности баланса для вывода (упрощенная версия)
  canWithdraw(amount) {
    const minWithdrawal = this._withdrawalSettings.min_withdrawal || 1000;
    // Весь баланс доступен для вывода
    return this._currentUserBalance >= amount && amount >= minWithdrawal;
  }

  // Проверка минимального баланса для доступа к выводу
  canAccessWithdrawal() {
    const minBalance = this._withdrawalSettings.min_balance_for_access ||
                      (window.appSettings ? window.appSettings.get('min_balance_for_withdrawal') : 100);
    return this._currentUserBalance >= minBalance;
  }

  /**
   * Получить доступный баланс (без зарезервированных средств)
   */
  getAvailableBalance() {
    return this._availableBalance;
  }

  /**
   * Получить зарезервированный баланс
   */
  getReservedBalance() {
    return this._reservedBalance;
  }

  /**
   * Получить общую сумму заработанных монет
   */
  getTotalEarned() {
    return this._totalEarned;
  }

  /**
   * Получить общую сумму выведенных монет
   */
  getTotalWithdrawn() {
    return this._totalWithdrawn;
  }

  /**
   * Получить баланс в USD
   */
  getBalanceUSD() {
    return this._balanceUSD;
  }

  /**
   * Получить дневную статистику
   */
  getDailyStats() {
    return this._dailyStats;
  }

  /**
   * Получить настройки вывода
   */
  getWithdrawalSettings() {
    return this._withdrawalSettings;
  }

  /**
   * Получить время последнего обновления
   */
  getLastUpdateTime() {
    return this._lastUpdateTime;
  }

  /**
   * Проверить, нужно ли обновить баланс (если прошло больше 5 минут)
   */
  needsUpdate() {
    const fiveMinutes = 5 * 60 * 1000;
    return (Date.now() - this._lastUpdateTime) > fiveMinutes;
  }

  // Форматирование баланса для отображения
  formatBalance(balance = null) {
    const amount = balance !== null ? balance : this._currentUserBalance;
    return amount.toLocaleString();
  }

  // Получение баланса в долларах
  getBalanceInUSD() {
    const coinValue = window.appSettings ? window.appSettings.getCoinValue() : 0.001;
    return this._currentUserBalance * coinValue;
  }

  // Обновление всех элементов баланса без изменения значения
  refreshDisplay() {
    this.updateBalance(this._currentUserBalance, 'refresh');
  }
}

window.balanceManager = new BalanceManager();
console.log('💰 [BalanceManager] Менеджер баланса загружен.');