<?php
/**
 * api/db_mock_sqlite.php
 * Новая версия эмуляции базы данных с использованием SQLite вместо JSON файлов
 */

declare(strict_types=1);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database_manager.php';

/**
 * ЗАГРУЗКА ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Теперь использует SQLite вместо JSON файлов
 */
function loadUserData(): array 
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->loadUserData();
    } catch (Exception $e) {
        error_log("loadUserData ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * СОХРАНЕНИЕ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 * Теперь использует SQLite вместо JSON файлов
 */
function saveUserData(array $userData): bool
{
    try {
        // ВАЖНО: Автоматически исправляем реферальные связи перед сохранением
        $fixed = fixReferralLinks($userData);
        if ($fixed) {
            error_log("saveUserData INFO: Реферальные связи автоматически исправлены перед сохранением.");
        }

        $db = DatabaseManager::getInstance();
        return $db->saveUserData($userData);
    } catch (Exception $e) {
        error_log("saveUserData ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ДАННЫХ КОНКРЕТНОГО ПОЛЬЗОВАТЕЛЯ
 */
function getUserDetails(int $telegramId): ?array
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->getUserDetails($telegramId);
    } catch (Exception $e) {
        error_log("getUserDetails ERROR: " . $e->getMessage());
        return null;
    }
}

/**
 * ОБНОВЛЕНИЕ БАЛАНСА ПОЛЬЗОВАТЕЛЯ
 */
function updateUserBalance(int $telegramId, float $newBalance): bool
{
    try {
        $userData = loadUserData();
        
        if (!isset($userData[$telegramId])) {
            error_log("updateUserBalance ERROR: Пользователь {$telegramId} не найден");
            return false;
        }
        
        $userData[$telegramId]['balance'] = $newBalance;
        $userData[$telegramId]['last_activity'] = time();
        
        return saveUserData($userData);
    } catch (Exception $e) {
        error_log("updateUserBalance ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ДОБАВЛЕНИЕ ЗАРАБОТКА ПОЛЬЗОВАТЕЛЮ
 */
function addUserEarnings(int $telegramId, float $amount): bool
{
    try {
        $userData = loadUserData();
        
        if (!isset($userData[$telegramId])) {
            error_log("addUserEarnings ERROR: Пользователь {$telegramId} не найден");
            return false;
        }
        
        $userData[$telegramId]['balance'] += $amount;
        $userData[$telegramId]['total_earned'] += $amount;
        $userData[$telegramId]['last_activity'] = time();
        
        return saveUserData($userData);
    } catch (Exception $e) {
        error_log("addUserEarnings ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * СОЗДАНИЕ НОВОГО ПОЛЬЗОВАТЕЛЯ
 */
function createUser(int $telegramId, array $userInfo = []): bool
{
    try {
        $userData = loadUserData();
        
        if (isset($userData[$telegramId])) {
            error_log("createUser INFO: Пользователь {$telegramId} уже существует");
            return true; // Пользователь уже существует
        }
        
        $userData[$telegramId] = [
            'id' => $telegramId,
            'balance' => 0,
            'total_earned' => 0,
            'referrer_id' => $userInfo['referrer_id'] ?? null,
            'referral_earnings' => 0,
            'first_name' => $userInfo['first_name'] ?? '',
            'last_name' => $userInfo['last_name'] ?? '',
            'username' => $userInfo['username'] ?? '',
            'language' => $userInfo['language'] ?? 'ru',
            'registered_at' => time(),
            'last_activity' => time(),
            'suspicious_activity_count' => 0,
            'withdrawals_count' => 0,
            'blocked' => false,
            'blocked_at' => null,
            'referrals_count' => 0,
            'joined' => time(),
            'suspicious_activity' => 0,
            'withdrawals' => [],
            'withdrawal_log' => [],
            'referrals' => [],
            'ad_views_log' => []
        ];
        
        return saveUserData($userData);
    } catch (Exception $e) {
        error_log("createUser ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * БЛОКИРОВКА/РАЗБЛОКИРОВКА ПОЛЬЗОВАТЕЛЯ
 */
function setUserBlocked(int $telegramId, bool $blocked): bool
{
    try {
        $userData = loadUserData();
        
        if (!isset($userData[$telegramId])) {
            error_log("setUserBlocked ERROR: Пользователь {$telegramId} не найден");
            return false;
        }
        
        $userData[$telegramId]['blocked'] = $blocked;
        $userData[$telegramId]['blocked_at'] = $blocked ? time() : null;
        $userData[$telegramId]['last_activity'] = time();
        
        return saveUserData($userData);
    } catch (Exception $e) {
        error_log("setUserBlocked ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ЛОГИРОВАНИЕ ПРОСМОТРА РЕКЛАМЫ
 */
function logAdView(int $userId, string $adType, float $reward): bool
{
    try {
        $db = DatabaseManager::getInstance();
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        return $db->logAdView($userId, $adType, $reward, $ip, $userAgent);
    } catch (Exception $e) {
        error_log("logAdView ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ЛОГИРОВАНИЕ КЛИКА ПО РЕКЛАМЕ
 */
function logAdClick(int $userId, string $adType, string $clickType, string $reason = ''): bool
{
    try {
        $db = DatabaseManager::getInstance();
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        return $db->logAdClick($userId, $adType, $clickType, $reason, $ip, $userAgent);
    } catch (Exception $e) {
        error_log("logAdClick ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ЛИМИТОВ РЕКЛАМЫ ДЛЯ ПОЛЬЗОВАТЕЛЯ
 */
function getUserAdLimits(int $userId): array
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->getUserAdLimits($userId);
    } catch (Exception $e) {
        error_log("getUserAdLimits ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ОБНОВЛЕНИЕ ЛИМИТОВ РЕКЛАМЫ ДЛЯ ПОЛЬЗОВАТЕЛЯ
 */
function updateUserAdLimit(int $userId, string $adType, int $count): bool
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->updateUserAdLimit($userId, $adType, $count);
    } catch (Exception $e) {
        error_log("updateUserAdLimit ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * СОЗДАНИЕ ТОКЕНА РЕКЛАМЫ
 */
function createAdToken(string $token, int $userId, string $adType, int $expiresIn = 300): bool
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->createAdToken($token, $userId, $adType, $expiresIn);
    } catch (Exception $e) {
        error_log("createAdToken ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ИСПОЛЬЗОВАНИЕ ТОКЕНА РЕКЛАМЫ
 */
function useAdToken(string $token): ?array
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->useAdToken($token);
    } catch (Exception $e) {
        error_log("useAdToken ERROR: " . $e->getMessage());
        return null;
    }
}

/**
 * ПОЛУЧЕНИЕ НАСТРОЕК БОТА (SQLite версия)
 */
function getBotSettingsSqlite(): array
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->getBotSettings();
    } catch (Exception $e) {
        error_log("getBotSettingsSqlite ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * СОХРАНЕНИЕ НАСТРОЕК БОТА (SQLite версия)
 */
function saveBotSettingsSqlite(array $settings): bool
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->saveBotSettings($settings);
    } catch (Exception $e) {
        error_log("saveBotSettingsSqlite ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ТЕКСТОВ БОТА (SQLite версия)
 */
function getBotTextsSqlite(string $language = 'ru'): array
{
    try {
        $db = DatabaseManager::getInstance();
        return $db->getBotTexts($language);
    } catch (Exception $e) {
        error_log("getBotTextsSqlite ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ИСПРАВЛЕНИЕ РЕФЕРАЛЬНЫХ СВЯЗЕЙ
 * Функция для совместимости со старым кодом
 */
function fixReferralLinks(array &$userData): bool
{
    $fixed = false;
    
    foreach ($userData as $telegramId => &$user) {
        // Проверяем корректность реферальных связей
        if (isset($user['referrer_id']) && $user['referrer_id']) {
            if (!isset($userData[$user['referrer_id']])) {
                error_log("fixReferralLinks: Некорректная реферальная связь для пользователя {$telegramId}");
                $user['referrer_id'] = null;
                $fixed = true;
            }
        }
        
        // Проверяем массив рефералов
        if (isset($user['referrals']) && is_array($user['referrals'])) {
            $validReferrals = [];
            foreach ($user['referrals'] as $referralId) {
                if (isset($userData[$referralId])) {
                    $validReferrals[] = $referralId;
                } else {
                    $fixed = true;
                }
            }
            $user['referrals'] = $validReferrals;
            $user['referrals_count'] = count($validReferrals);
        }
    }
    
    return $fixed;
}

/**
 * ПОЛУЧЕНИЕ СТАТИСТИКИ БАЗЫ ДАННЫХ
 */
function getDatabaseStats(): array
{
    try {
        $db = DatabaseManager::getInstance();
        $pdo = $db->getPdo();
        
        $stats = [];
        
        // Получаем количество пользователей
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $stats['users'] = $stmt->fetch()['count'];
        
        // Получаем количество просмотров рекламы
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ad_views");
        $stats['ad_views'] = $stmt->fetch()['count'];
        
        // Получаем количество кликов по рекламе
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ad_clicks");
        $stats['ad_clicks'] = $stmt->fetch()['count'];
        
        // Получаем количество токенов
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ad_tokens");
        $stats['ad_tokens'] = $stmt->fetch()['count'];
        
        return $stats;
    } catch (Exception $e) {
        error_log("getDatabaseStats ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ПРОВЕРКА ПОДКЛЮЧЕНИЯ К БАЗЕ ДАННЫХ
 */
function testDatabaseConnection(): bool
{
    try {
        $db = DatabaseManager::getInstance();
        $pdo = $db->getPdo();
        
        // Простой тест запрос
        $stmt = $pdo->query("SELECT 1");
        return $stmt !== false;
    } catch (Exception $e) {
        error_log("testDatabaseConnection ERROR: " . $e->getMessage());
        return false;
    }
}
?>
