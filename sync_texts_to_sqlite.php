<?php
/**
 * sync_texts_to_sqlite.php
 * Синхронизация текстов из bot_texts.json в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';
require_once 'api/bot_texts_functions.php';

echo "🔄 СИНХРОНИЗАЦИЯ ТЕКСТОВ JSON → SQLite\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Загружаем тексты из JSON файла
    $jsonFile = 'bot/bot_texts.json';
    
    if (!file_exists($jsonFile)) {
        throw new Exception("Файл {$jsonFile} не найден!");
    }
    
    echo "📂 Загружаем тексты из {$jsonFile}...\n";
    $jsonContent = file_get_contents($jsonFile);
    $jsonTexts = json_decode($jsonContent, true);
    
    if (!$jsonTexts) {
        throw new Exception("Ошибка декодирования JSON файла!");
    }
    
    echo "✅ JSON файл загружен успешно\n";
    echo "   Языки: " . implode(', ', array_keys($jsonTexts)) . "\n\n";
    
    // Очищаем старые тексты в SQLite
    echo "🗑️ Очищаем старые тексты в SQLite...\n";
    $sqlite->query("DELETE FROM bot_texts");
    echo "✅ Старые тексты удалены\n\n";
    
    // Синхронизируем тексты
    echo "🔄 Синхронизируем тексты...\n";
    $totalSaved = 0;
    
    foreach ($jsonTexts as $language => $sections) {
        echo "  📝 Обрабатываем язык: {$language}\n";
        $langCount = 0;
        
        if (is_array($sections)) {
            foreach ($sections as $section => $sectionTexts) {
                if (is_array($sectionTexts)) {
                    foreach ($sectionTexts as $key => $value) {
                        if (is_string($value)) {
                            $fullKey = $section . '.' . $key;
                            $sql = "INSERT INTO bot_texts (language_code, text_key, text_value, category) VALUES (?, ?, ?, ?)";
                            $sqlite->query($sql, [$language, $fullKey, $value, $section]);
                            $langCount++;
                            $totalSaved++;
                        }
                    }
                }
            }
        }
        
        echo "     ✅ Сохранено: {$langCount} текстов\n";
    }
    
    echo "\n📊 РЕЗУЛЬТАТ СИНХРОНИЗАЦИИ:\n";
    echo "  ✅ Всего синхронизировано: {$totalSaved} текстов\n";
    
    // Проверяем результат
    $result = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts');
    echo "  📊 В SQLite теперь: " . $result[0]['count'] . " записей\n";
    
    // Статистика по языкам
    echo "\n🌍 СТАТИСТИКА ПО ЯЗЫКАМ:\n";
    $langStats = $sqlite->query('SELECT language_code, COUNT(*) as count FROM bot_texts GROUP BY language_code');
    foreach ($langStats as $stat) {
        echo "  - {$stat['language_code']}: {$stat['count']} текстов\n";
    }
    
    // Статистика по категориям
    echo "\n📂 ТОП КАТЕГОРИИ:\n";
    $catStats = $sqlite->query('SELECT category, COUNT(*) as count FROM bot_texts GROUP BY category ORDER BY count DESC LIMIT 10');
    foreach ($catStats as $stat) {
        echo "  - {$stat['category']}: {$stat['count']} текстов\n";
    }
    
    echo "\n✅ СИНХРОНИЗАЦИЯ ЗАВЕРШЕНА УСПЕШНО!\n";
    echo "🎯 Теперь приложение будет получать актуальные тексты из SQLite\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎉 ГОТОВО!\n";
?>
