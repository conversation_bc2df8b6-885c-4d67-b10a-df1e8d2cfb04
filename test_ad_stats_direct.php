<?php
/**
 * test_ad_stats_direct.php
 * Прямой тест статистики рекламы без аутентификации
 */

declare(strict_types=1);

echo "📊 ПРЯМОЙ ТЕСТ СТАТИСТИКИ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    
    echo "1. 🔧 ТЕСТ ЛОГИКИ ИЗ ad_stats_api.php:\n";
    
    $sqlite = new RealSQLiteManager();
    
    // Параметры фильтрации (как в API)
    $date_from = null;
    $date_to = null;
    $ad_type_filter = 'all';
    
    // Строим WHERE условия
    $whereConditions = [];
    $params = [];
    
    if ($date_from) {
        $whereConditions[] = "DATE(timestamp) >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $whereConditions[] = "DATE(timestamp) <= ?";
        $params[] = $date_to;
    }
    
    if ($ad_type_filter !== 'all') {
        $whereConditions[] = "ad_type = ?";
        $params[] = $ad_type_filter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    echo "   📝 WHERE условие: " . ($whereClause ?: 'Нет фильтров') . "\n";
    
    // Получаем статистику по типам рекламы
    $statsQuery = "
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COALESCE(SUM(v.reward), 0) as rewards,
            COALESCE(c.clicks, 0) as clicks,
            CASE 
                WHEN COUNT(v.id) > 0 THEN ROUND((COALESCE(c.clicks, 0) * 100.0 / COUNT(v.id)), 2)
                ELSE 0 
            END as ctr
        FROM ad_views v
        LEFT JOIN (
            SELECT ad_type, COUNT(*) as clicks 
            FROM ad_clicks 
            " . str_replace('v.', '', $whereClause) . "
            GROUP BY ad_type
        ) c ON v.ad_type = c.ad_type
        " . $whereClause . "
        GROUP BY v.ad_type
        ORDER BY views DESC
    ";
    
    echo "   📊 Выполняем запрос статистики по типам...\n";
    $statsResults = $sqlite->query($statsQuery, $params);
    
    $stats_by_type = [];
    foreach ($statsResults as $row) {
        $stats_by_type[$row['ad_type']] = [
            'views' => (int)$row['views'],
            'clicks' => (int)$row['clicks'],
            'rewards' => (float)$row['rewards'],
            'ctr' => (float)$row['ctr']
        ];
    }
    
    echo "   ✅ Получено типов рекламы: " . count($stats_by_type) . "\n";
    
    // Выводим результаты
    echo "\n2. 📈 РЕЗУЛЬТАТЫ ПО ТИПАМ РЕКЛАМЫ:\n";
    foreach ($stats_by_type as $type => $stats) {
        echo "   - {$type}:\n";
        echo "     👁️ Просмотры: {$stats['views']}\n";
        echo "     🖱️ Клики: {$stats['clicks']}\n";
        echo "     📈 CTR: {$stats['ctr']}%\n";
        echo "     💰 Награды: {$stats['rewards']}\n";
    }
    
    // Получаем статистику по странам
    echo "\n3. 🌍 ТЕСТ СТАТИСТИКИ ПО СТРАНАМ:\n";
    
    $countryQuery = "
        SELECT 
            ip_address,
            COUNT(*) as clicks
        FROM ad_clicks 
        WHERE ip_address IS NOT NULL AND ip_address != ''
        " . ($whereClause ? "AND " . str_replace('v.', '', $whereClause) : '') . "
        GROUP BY ip_address
        ORDER BY clicks DESC
        LIMIT 10
    ";
    
    $countryResults = $sqlite->query($countryQuery, $params);
    
    echo "   📊 Найдено IP адресов: " . count($countryResults) . "\n";
    
    // Простая функция определения страны (без внешних API)
    function getSimpleCountryCode($ip) {
        // Простая логика для тестирования
        if (strpos($ip, '192.168.') === 0 || strpos($ip, '127.') === 0) {
            return 'LOCAL';
        } elseif (strpos($ip, '103.') === 0) {
            return 'AS'; // Азия
        } elseif (strpos($ip, '89.') === 0 || strpos($ip, '217.') === 0) {
            return 'EU'; // Европа
        } else {
            return 'XX'; // Неизвестно
        }
    }
    
    $stats_by_country = [];
    foreach ($countryResults as $row) {
        $country = getSimpleCountryCode($row['ip_address']);
        if (!isset($stats_by_country[$country])) {
            $stats_by_country[$country] = 0;
        }
        $stats_by_country[$country] += (int)$row['clicks'];
    }
    
    arsort($stats_by_country);
    
    echo "   🌍 Статистика по регионам:\n";
    foreach ($stats_by_country as $country => $clicks) {
        echo "     - {$country}: {$clicks} кликов\n";
    }
    
    // Почасовая статистика
    echo "\n4. 🕒 ТЕСТ ПОЧАСОВОЙ СТАТИСТИКИ:\n";
    
    $hourlyQuery = "
        SELECT 
            CAST(strftime('%H', timestamp) AS INTEGER) as hour,
            COUNT(*) as views
        FROM ad_views 
        " . $whereClause . "
        GROUP BY hour
        ORDER BY hour
    ";
    
    $hourlyResults = $sqlite->query($hourlyQuery, $params);
    $hourly_stats = array_fill(0, 24, ['clicks' => 0, 'views' => 0]);
    
    foreach ($hourlyResults as $row) {
        $hour = (int)$row['hour'];
        if ($hour >= 0 && $hour < 24) {
            $hourly_stats[$hour]['views'] = (int)$row['views'];
        }
    }
    
    // Активные часы
    $activeHours = [];
    foreach ($hourly_stats as $hour => $stats) {
        if ($stats['views'] > 0) {
            $activeHours[$hour] = $stats['views'];
        }
    }
    
    arsort($activeHours);
    $topHours = array_slice($activeHours, 0, 5, true);
    
    echo "   🕒 ТОП-5 активных часов:\n";
    foreach ($topHours as $hour => $views) {
        echo "     - {$hour}:00 - {$views} просмотров\n";
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:\n";
    echo "   ✅ SQLite запросы работают корректно\n";
    echo "   ✅ Статистика по типам рекламы: " . count($stats_by_type) . " типов\n";
    echo "   ✅ Статистика по странам: " . count($stats_by_country) . " регионов\n";
    echo "   ✅ Почасовая статистика: " . count($activeHours) . " активных часов\n";
    
    // Общие итоги
    $totalViews = array_sum(array_column($stats_by_type, 'views'));
    $totalClicks = array_sum(array_column($stats_by_type, 'clicks'));
    $totalRewards = array_sum(array_column($stats_by_type, 'rewards'));
    
    echo "\n📊 ОБЩИЕ ИТОГИ:\n";
    echo "   👁️ Всего просмотров: {$totalViews}\n";
    echo "   🖱️ Всего кликов: {$totalClicks}\n";
    echo "   💰 Всего наград: {$totalRewards}\n";
    echo "   📈 Общий CTR: " . ($totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0) . "%\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ТЕСТ ЗАВЕРШЕН!\n";
?>
