<?php
/**
 * test_ip_and_ctr_fix.php
 * Тест исправления IP адресов и CTR
 */

declare(strict_types=1);

echo "🔧 ТЕСТ ИСПРАВЛЕНИЯ IP И CTR\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'api/db_mock_final_sqlite.php';
    
    echo "1. 🧪 ТЕСТ ЗАПИСИ КЛИКА С IP:\n";
    
    // Симулируем IP адрес
    $_SERVER['REMOTE_ADDR'] = '*************';
    $_SERVER['HTTP_USER_AGENT'] = 'Test User Agent';
    
    // Записываем тестовый клик
    $testUserId = 12345;
    $testAdType = 'native_banner';
    $testClickType = 'button_click';
    
    $clickResult = logAdClick($testUserId, $testAdType, $testClickType, 'test click');
    
    if ($clickResult) {
        echo "   ✅ Клик записан успешно\n";
        
        // Проверяем, что IP записался
        $sqlite = getSQLiteManager();
        $recentClick = $sqlite->query("SELECT * FROM ad_clicks WHERE user_id = ? ORDER BY timestamp DESC LIMIT 1", [$testUserId]);
        
        if (!empty($recentClick)) {
            $click = $recentClick[0];
            echo "   📊 Записанный клик:\n";
            echo "     - User ID: {$click['user_id']}\n";
            echo "     - Ad Type: {$click['ad_type']}\n";
            echo "     - IP Address: " . ($click['ip_address'] ?? 'NULL') . "\n";
            echo "     - User Agent: " . ($click['user_agent'] ?? 'NULL') . "\n";
            echo "     - Timestamp: {$click['timestamp']}\n";
            
            if ($click['ip_address'] === '*************') {
                echo "   ✅ IP адрес записался правильно!\n";
            } else {
                echo "   ❌ IP адрес не записался или записался неправильно!\n";
            }
        } else {
            echo "   ❌ Клик не найден в базе данных!\n";
        }
    } else {
        echo "   ❌ Ошибка записи клика\n";
    }
    
    echo "\n2. 🧪 ТЕСТ ЗАПИСИ ПРОСМОТРА С IP:\n";
    
    // Записываем тестовый просмотр
    $viewResult = logAdView($testUserId, $testAdType, 10.0);
    
    if ($viewResult) {
        echo "   ✅ Просмотр записан успешно\n";
        
        // Проверяем, что IP записался
        $recentView = $sqlite->query("SELECT * FROM ad_views WHERE user_id = ? ORDER BY timestamp DESC LIMIT 1", [$testUserId]);
        
        if (!empty($recentView)) {
            $view = $recentView[0];
            echo "   📊 Записанный просмотр:\n";
            echo "     - User ID: {$view['user_id']}\n";
            echo "     - Ad Type: {$view['ad_type']}\n";
            echo "     - Reward: {$view['reward']}\n";
            echo "     - IP Address: " . ($view['ip_address'] ?? 'NULL') . "\n";
            echo "     - Timestamp: {$view['timestamp']}\n";
            
            if ($view['ip_address'] === '*************') {
                echo "   ✅ IP адрес в просмотре записался правильно!\n";
            } else {
                echo "   ❌ IP адрес в просмотре не записался!\n";
            }
        }
    } else {
        echo "   ❌ Ошибка записи просмотра\n";
    }
    
    echo "\n3. 📊 ПРОВЕРКА СТАТИСТИКИ ПО IP:\n";
    
    // Проверяем статистику по IP
    $ipStats = $sqlite->query("
        SELECT ip_address, COUNT(*) as clicks 
        FROM ad_clicks 
        WHERE ip_address IS NOT NULL AND ip_address != '' 
        GROUP BY ip_address 
        ORDER BY clicks DESC 
        LIMIT 10
    ");
    
    echo "   📊 Статистика кликов по IP:\n";
    if (empty($ipStats)) {
        echo "     ❌ Нет данных по IP адресам!\n";
    } else {
        foreach ($ipStats as $ip) {
            echo "     - {$ip['ip_address']}: {$ip['clicks']} кликов\n";
        }
    }
    
    echo "\n4. 📈 ПРОВЕРКА РАСЧЕТА CTR:\n";
    
    // Получаем статистику для расчета CTR
    $ctrQuery = "
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COALESCE(c.clicks, 0) as clicks
        FROM ad_views v
        LEFT JOIN (
            SELECT ad_type, COUNT(*) as clicks 
            FROM ad_clicks 
            WHERE ad_type != 'test_banner'
            GROUP BY ad_type
        ) c ON v.ad_type = c.ad_type
        WHERE v.ad_type != 'test_banner'
        GROUP BY v.ad_type
        LIMIT 5
    ";
    
    $ctrResults = $sqlite->query($ctrQuery);
    
    echo "   📈 Расчет CTR по типам рекламы:\n";
    foreach ($ctrResults as $row) {
        $views = (int)$row['views'];
        $clicks = (int)$row['clicks'];
        
        // Правильная формула CTR: (клики / просмотры) * 100
        $ctr = $views > 0 ? round(($clicks / $views) * 100, 2) : 0;
        
        echo "     - {$row['ad_type']}:\n";
        echo "       👁️ Просмотры: {$views}\n";
        echo "       🖱️ Клики: {$clicks}\n";
        echo "       📈 CTR: {$ctr}%\n";
        
        // Проверяем разумность CTR
        if ($ctr >= 0 && $ctr <= 100) {
            echo "       ✅ CTR в разумных пределах\n";
        } else {
            echo "       ⚠️ CTR выходит за разумные пределы\n";
        }
    }
    
    echo "\n5. 🌍 ТЕСТ ОПРЕДЕЛЕНИЯ СТРАН:\n";
    
    // Тестируем функцию определения стран
    $testIPs = [
        '***********' => 'LOCAL',
        '*******' => 'US',
        '*********' => 'RU',
        '*******' => 'US'
    ];
    
    echo "   🌐 Тест определения стран по IP:\n";
    foreach ($testIPs as $ip => $expectedCountry) {
        // Простая функция для тестирования
        function getTestCountry($ip) {
            if (strpos($ip, '192.168.') === 0 || strpos($ip, '127.') === 0) {
                return 'LOCAL';
            } elseif ($ip === '*******' || $ip === '*******') {
                return 'US';
            } elseif ($ip === '*********') {
                return 'RU';
            } else {
                return 'XX';
            }
        }
        
        $detectedCountry = getTestCountry($ip);
        $status = ($detectedCountry === $expectedCountry) ? '✅' : '❌';
        echo "     {$status} {$ip} → {$detectedCountry} (ожидалось: {$expectedCountry})\n";
    }
    
    echo "\n6. 📊 ИТОГОВАЯ ПРОВЕРКА:\n";
    
    // Общая статистика
    $totalViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ad_type != 'test_banner'")[0]['count'];
    $totalClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ad_type != 'test_banner'")[0]['count'];
    $viewsWithIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ip_address IS NOT NULL AND ip_address != '' AND ad_type != 'test_banner'")[0]['count'];
    $clicksWithIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ip_address IS NOT NULL AND ip_address != '' AND ad_type != 'test_banner'")[0]['count'];
    
    $overallCTR = $totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0;
    
    echo "   📊 Общая статистика:\n";
    echo "     👁️ Всего просмотров: {$totalViews}\n";
    echo "     🖱️ Всего кликов: {$totalClicks}\n";
    echo "     📈 Общий CTR: {$overallCTR}%\n";
    echo "     🌐 Просмотров с IP: {$viewsWithIP} (" . round(($viewsWithIP / max($totalViews, 1)) * 100, 1) . "%)\n";
    echo "     🌐 Кликов с IP: {$clicksWithIP} (" . round(($clicksWithIP / max($totalClicks, 1)) * 100, 1) . "%)\n";
    
    echo "\n✅ РЕЗУЛЬТАТ ДИАГНОСТИКИ:\n";
    
    if ($clicksWithIP > 0) {
        echo "   ✅ IP адреса в кликах записываются\n";
    } else {
        echo "   ❌ IP адреса в кликах НЕ записываются\n";
    }
    
    if ($viewsWithIP > 0) {
        echo "   ✅ IP адреса в просмотрах записываются\n";
    } else {
        echo "   ❌ IP адреса в просмотрах НЕ записываются\n";
    }
    
    if ($overallCTR >= 0 && $overallCTR <= 100) {
        echo "   ✅ CTR рассчитывается правильно ({$overallCTR}%)\n";
    } else {
        echo "   ❌ CTR рассчитывается неправильно ({$overallCTR}%)\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ДИАГНОСТИКА ЗАВЕРШЕНА!\n";
?>
