<?php
/**
 * api/db_mock_unified.php
 * Унифицированная версия db_mock.php с использованием единой JSON структуры
 */

declare(strict_types=1);

require_once __DIR__ . "/../database/unified_json_manager.php";

/**
 * ЗАГРУЗКА ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 */
function loadUserData(): array 
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->getUserData();
    } catch (Exception $e) {
        error_log("loadUserData ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * СОХРАНЕНИЕ ДАННЫХ ПОЛЬЗОВАТЕЛЕЙ
 */
function saveUserData(array $userData): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->saveUserData($userData);
    } catch (Exception $e) {
        error_log("saveUserData ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ДАННЫХ КОНКРЕТНОГО ПОЛЬЗОВАТЕЛЯ
 */
function getUserDetails(int $telegramId): ?array
{
    try {
        $userData = loadUserData();
        return $userData[$telegramId] ?? null;
    } catch (Exception $e) {
        error_log("getUserDetails ERROR: " . $e->getMessage());
        return null;
    }
}

/**
 * ЛОГИРОВАНИЕ ПРОСМОТРА РЕКЛАМЫ
 */
function logAdView(int $userId, string $adType, float $reward): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        
        $viewData = [
            "user_id" => $userId,
            "ad_type" => $adType,
            "reward" => $reward,
            "timestamp" => date("Y-m-d H:i:s"),
            "ip" => $_SERVER["REMOTE_ADDR"] ?? null,
            "user_agent" => $_SERVER["HTTP_USER_AGENT"] ?? null
        ];
        
        return $manager->logAdView($viewData);
    } catch (Exception $e) {
        error_log("logAdView ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ЛОГИРОВАНИЕ КЛИКА ПО РЕКЛАМЕ
 */
function logAdClick(int $userId, string $adType, string $clickType, string $reason = ""): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        
        $clickData = [
            "user_id" => $userId,
            "ad_type" => $adType,
            "click_type" => $clickType,
            "reason" => $reason,
            "timestamp" => date("Y-m-d H:i:s"),
            "ip" => $_SERVER["REMOTE_ADDR"] ?? null,
            "user_agent" => $_SERVER["HTTP_USER_AGENT"] ?? null
        ];
        
        return $manager->logAdClick($clickData);
    } catch (Exception $e) {
        error_log("logAdClick ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ЛИМИТОВ РЕКЛАМЫ ДЛЯ ПОЛЬЗОВАТЕЛЯ
 */
function getUserAdLimits(int $userId): array
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->getUserAdLimits($userId);
    } catch (Exception $e) {
        error_log("getUserAdLimits ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ОБНОВЛЕНИЕ ЛИМИТОВ РЕКЛАМЫ ДЛЯ ПОЛЬЗОВАТЕЛЯ
 */
function updateUserAdLimit(int $userId, string $adType, int $count): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->updateUserAdLimit($userId, $adType, $count);
    } catch (Exception $e) {
        error_log("updateUserAdLimit ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * СОЗДАНИЕ ТОКЕНА РЕКЛАМЫ
 */
function createAdToken(string $token, int $userId, string $adType, int $expiresIn = 300): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->createAdToken($token, $userId, $adType, $expiresIn);
    } catch (Exception $e) {
        error_log("createAdToken ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ИСПОЛЬЗОВАНИЕ ТОКЕНА РЕКЛАМЫ
 */
function useAdToken(string $token): ?array
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->useAdToken($token);
    } catch (Exception $e) {
        error_log("useAdToken ERROR: " . $e->getMessage());
        return null;
    }
}

/**
 * ПОЛУЧЕНИЕ НАСТРОЕК БОТА
 */
function getBotSettingsUnified(): array
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->getBotSettings();
    } catch (Exception $e) {
        error_log("getBotSettingsUnified ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * СОХРАНЕНИЕ НАСТРОЕК БОТА
 */
function saveBotSettingsUnified(array $settings): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->saveBotSettings($settings);
    } catch (Exception $e) {
        error_log("saveBotSettingsUnified ERROR: " . $e->getMessage());
        return false;
    }
}

/**
 * ПОЛУЧЕНИЕ ТЕКСТОВ БОТА
 */
function getBotTextsUnified(string $language = "ru"): array
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->getBotTexts($language);
    } catch (Exception $e) {
        error_log("getBotTextsUnified ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ПОЛУЧЕНИЕ СТАТИСТИКИ БАЗЫ ДАННЫХ
 */
function getDatabaseStats(): array
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        return $manager->getStatistics();
    } catch (Exception $e) {
        error_log("getDatabaseStats ERROR: " . $e->getMessage());
        return [];
    }
}

/**
 * ПРОВЕРКА ПОДКЛЮЧЕНИЯ К БАЗЕ ДАННЫХ
 */
function testDatabaseConnection(): bool
{
    try {
        $manager = UnifiedJsonManager::getInstance();
        $stats = $manager->getStatistics();
        return isset($stats["users_count"]);
    } catch (Exception $e) {
        error_log("testDatabaseConnection ERROR: " . $e->getMessage());
        return false;
    }
}
?>