<?php
/**
 * init_coins_system.php
 * Инициализация системы монет в существующей базе данных
 */

declare(strict_types=1);

echo "🪙 ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ МОНЕТ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'api/coins_manager.php';
    
    echo "1. 🔧 СОЗДАНИЕ МЕНЕДЖЕРА МОНЕТ:\n";
    $coinsManager = new CoinsManager();
    echo "   ✅ CoinsManager создан и база расширена\n";
    
    echo "\n2. 📊 ПРОВЕРКА ТАБЛИЦ:\n";
    
    require_once 'database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    // Проверяем существующие таблицы
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
    
    $requiredTables = [
        'users' => 'Пользователи (основная)',
        'coin_transactions' => 'Транзакции монет',
        'coin_settings' => 'Настройки системы',
        'user_daily_stats' => 'Дневная статистика',
        'user_withdrawals' => 'Заявки на вывод'
    ];
    
    $existingTables = array_column($tables, 'name');
    
    foreach ($requiredTables as $table => $description) {
        if (in_array($table, $existingTables)) {
            echo "   ✅ {$description}: {$table}\n";
        } else {
            echo "   ❌ {$description}: {$table} НЕ НАЙДЕНА\n";
        }
    }
    
    echo "\n3. 🔍 ПРОВЕРКА КОЛОНОК В USERS:\n";
    
    $userColumns = $db->query("PRAGMA table_info(users)");
    $columnNames = array_column($userColumns, 'name');
    
    $requiredColumns = [
        'telegram_id' => 'ID пользователя',
        'balance' => 'Баланс',
        'reserved_balance' => 'Зарезервированный баланс',
        'total_earned' => 'Всего заработано'
    ];
    
    foreach ($requiredColumns as $column => $description) {
        if (in_array($column, $columnNames)) {
            echo "   ✅ {$description}: {$column}\n";
        } else {
            echo "   ❌ {$description}: {$column} НЕ НАЙДЕНА\n";
        }
    }
    
    echo "\n4. ⚙️ ПРОВЕРКА НАСТРОЕК:\n";
    
    $settings = $db->query("SELECT setting_key, setting_value FROM coin_settings ORDER BY setting_key");
    
    if (empty($settings)) {
        echo "   ❌ Настройки не найдены\n";
    } else {
        echo "   ✅ Найдено настроек: " . count($settings) . "\n";
        foreach ($settings as $setting) {
            echo "     - {$setting['setting_key']}: {$setting['setting_value']}\n";
        }
    }
    
    echo "\n5. 🧪 ТЕСТ ОСНОВНЫХ ФУНКЦИЙ:\n";
    
    $testUserId = '5880288830';
    
    // Тест получения баланса
    echo "   📊 Тест получения баланса пользователя {$testUserId}:\n";
    $balance = $coinsManager->getUserBalance($testUserId);
    echo "     - Баланс: {$balance['balance']}\n";
    echo "     - Зарезервировано: {$balance['reserved_balance']}\n";
    echo "     - Доступно: {$balance['available_balance']}\n";
    echo "     - Всего заработано: {$balance['total_earned']}\n";
    echo "     - Всего выведено: {$balance['total_withdrawn']}\n";
    
    // Тест настроек
    echo "\n   ⚙️ Тест настроек:\n";
    $nativeBannerReward = $coinsManager->getAdReward('native_banner');
    $videoReward = $coinsManager->getAdReward('rewarded_video');
    $interstitialReward = $coinsManager->getAdReward('interstitial');
    
    echo "     - Награда за native_banner: {$nativeBannerReward} монет\n";
    echo "     - Награда за rewarded_video: {$videoReward} монет\n";
    echo "     - Награда за interstitial: {$interstitialReward} монет\n";
    
    // Тест дневного лимита
    echo "\n   📅 Тест дневного лимита:\n";
    $dailyLimit = $coinsManager->checkDailyEarnLimit($testUserId);
    echo "     - Дневной лимит: {$dailyLimit['daily_limit']} монет\n";
    echo "     - Заработано сегодня: {$dailyLimit['earned_today']} монет\n";
    echo "     - Осталось: {$dailyLimit['remaining']} монет\n";
    echo "     - Можно зарабатывать: " . ($dailyLimit['can_earn'] ? 'Да' : 'Нет') . "\n";
    
    echo "\n6. 📈 СТАТИСТИКА БАЗЫ ДАННЫХ:\n";
    
    // Общая статистика
    $totalUsers = $db->query("SELECT COUNT(*) as count FROM users")[0]['count'];
    $totalTransactions = $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'];
    $totalWithdrawals = $db->query("SELECT COUNT(*) as count FROM user_withdrawals")[0]['count'];
    
    echo "   📊 Всего пользователей: {$totalUsers}\n";
    echo "   💰 Всего транзакций: {$totalTransactions}\n";
    echo "   💸 Всего заявок на вывод: {$totalWithdrawals}\n";
    
    // Статистика балансов
    $balanceStats = $db->query("
        SELECT 
            COUNT(*) as users_with_balance,
            SUM(balance) as total_balance,
            AVG(balance) as avg_balance,
            MAX(balance) as max_balance
        FROM users 
        WHERE balance > 0
    ")[0];
    
    if ($balanceStats['users_with_balance'] > 0) {
        echo "   💰 Пользователей с балансом: {$balanceStats['users_with_balance']}\n";
        echo "   💰 Общий баланс всех пользователей: {$balanceStats['total_balance']}\n";
        echo "   💰 Средний баланс: " . round($balanceStats['avg_balance'], 2) . "\n";
        echo "   💰 Максимальный баланс: {$balanceStats['max_balance']}\n";
    } else {
        echo "   💰 Пользователей с балансом: 0\n";
    }
    
    echo "\n7. 🎯 ГОТОВЫЕ API ENDPOINTS:\n";
    
    $endpoints = [
        'api/get_user_balance.php' => 'Получение баланса пользователя',
        'api/credit_coins.php' => 'Начисление монет за просмотр рекламы',
        'api/create_withdrawal.php' => 'Создание заявки на вывод',
        'api/process_withdrawal.php' => 'Обработка вывода средств',
        'api/get_transactions.php' => 'История транзакций пользователя'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        echo "   📡 {$description}: {$endpoint}\n";
    }
    
    echo "\n✅ СИСТЕМА МОНЕТ УСПЕШНО ИНИЦИАЛИЗИРОВАНА!\n";
    
    echo "\n🎮 СЛЕДУЮЩИЕ ШАГИ:\n";
    echo "   1. 📡 Создать API endpoints для работы с монетами\n";
    echo "   2. 🎨 Интегрировать с фронтендом\n";
    echo "   3. 🧪 Протестировать начисления и списания\n";
    echo "   4. 💸 Настроить систему выводов\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА ИНИЦИАЛИЗАЦИИ: " . $e->getMessage() . "\n";
    echo "📍 Файл: " . $e->getFile() . "\n";
    echo "📍 Строка: " . $e->getLine() . "\n";
}
?>
