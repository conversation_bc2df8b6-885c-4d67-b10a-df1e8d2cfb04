<?php
require_once 'database/real_sqlite_manager.php';
$sqlite = new RealSQLiteManager();

echo "Добавляем переводы для кнопок...\n";

$translations = [
    ['tasks.open_link', 'Открыть ссылку', 'ru', 'tasks'],
    ['tasks.open_link', 'Open Link', 'en', 'tasks'],
    ['tasks.watch_video', 'Смотреть видео', 'ru', 'tasks'],
    ['tasks.watch_video', 'Watch Video', 'en', 'tasks'],
    ['tasks.watch_ad', 'Смотреть рекламу', 'ru', 'tasks'],
    ['tasks.watch_ad', 'Watch Ad', 'en', 'tasks']
];

foreach ($translations as $t) {
    try {
        $sqlite->query("INSERT OR REPLACE INTO bot_texts (text_key, text_value, language_code, category) VALUES (?, ?, ?, ?)", $t);
        echo "✅ {$t[0]} [{$t[2]}]: {$t[1]}\n";
    } catch (Exception $e) {
        echo "❌ Ошибка: " . $e->getMessage() . "\n";
    }
}

echo "Готово!\n";
?>
