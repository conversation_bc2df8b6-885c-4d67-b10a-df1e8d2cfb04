<?php
/**
 * migrate_all_server_data.php
 * Полная миграция всех данных с сервера в SQLite базу данных
 */

declare(strict_types=1);

echo "🚀 ПОЛНАЯ МИГРАЦИЯ ДАННЫХ С СЕРВЕРА\n";
echo "====================================\n\n";

try {
    $dbPath = __DIR__ . '/database/app.sqlite';
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Подключение к базе данных установлено\n\n";
    
    // Создаем недостающие таблицы
    echo "📋 СОЗДАНИЕ НЕДОСТАЮЩИХ ТАБЛИЦ\n";
    echo "===============================\n";
    
    // Таблица для продвинутых отпечатков устройств
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS advanced_fingerprints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id BIGINT NOT NULL,
            fingerprint_hash TEXT NOT NULL UNIQUE,
            components TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_advanced_fingerprints_user_id ON advanced_fingerprints(user_id)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_advanced_fingerprints_hash ON advanced_fingerprints(fingerprint_hash)");
    echo "   ✅ Таблица advanced_fingerprints создана\n";
    
    // Таблица для блокированных устройств
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS blocked_devices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT NOT NULL UNIQUE,
            reason TEXT,
            blocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            blocked_by TEXT DEFAULT 'system'
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blocked_devices_device_id ON blocked_devices(device_id)");
    echo "   ✅ Таблица blocked_devices создана\n";

    // Таблица для VPN анализа
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS vpn_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id BIGINT NOT NULL,
            ip_address TEXT NOT NULL,
            is_vpn BOOLEAN DEFAULT 0,
            vpn_provider TEXT,
            risk_score INTEGER DEFAULT 0,
            analysis_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_vpn_analysis_user_id ON vpn_analysis(user_id)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_vpn_analysis_ip ON vpn_analysis(ip_address)");
    echo "   ✅ Таблица vpn_analysis создана\n";

    // Таблица для VPN блокировок
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS vpn_blocks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address TEXT NOT NULL UNIQUE,
            provider TEXT,
            blocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            reason TEXT
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_vpn_blocks_ip ON vpn_blocks(ip_address)");
    echo "   ✅ Таблица vpn_blocks создана\n";

    // Таблица для использованных токенов
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS used_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token TEXT NOT NULL UNIQUE,
            user_id BIGINT NOT NULL,
            used_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_used_tokens_token ON used_tokens(token)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_used_tokens_user_id ON used_tokens(user_id)");
    echo "   ✅ Таблица used_tokens создана\n\n";
    
    // 1. МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ С ВЫПЛАТАМИ
    echo "1. 👥 МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ И ВЫПЛАТ\n";
    echo "=====================================\n";
    
    $userDataFile = 'database/user_data.json';
    if (file_exists($userDataFile)) {
        $userData = json_decode(file_get_contents($userDataFile), true);
        echo "📁 Найдено " . count($userData) . " пользователей\n";
        
        $migratedUsers = 0;
        $updatedUsers = 0;
        $migratedWithdrawals = 0;
        
        foreach ($userData as $telegramId => $user) {
            try {
                // Проверяем существование пользователя
                $existing = $pdo->prepare("SELECT id FROM users WHERE telegram_id = ?");
                $existing->execute([$telegramId]);
                
                if ($existing->fetch()) {
                    // Обновляем пользователя
                    $stmt = $pdo->prepare("
                        UPDATE users SET 
                            username = ?, first_name = ?, last_name = ?, 
                            balance = ?, total_earned = ?, language = ?,
                            referrer_id = ?, referral_earnings = ?,
                            withdrawals_count = ?, suspicious_activity_count = ?,
                            blocked = ?, updated_at = ?
                        WHERE telegram_id = ?
                    ");
                    $stmt->execute([
                        $user['username'] ?? null,
                        $user['first_name'] ?? '',
                        $user['last_name'] ?? '',
                        $user['balance'] ?? 0,
                        $user['total_earned'] ?? 0,
                        $user['language'] ?? 'ru',
                        $user['referrer_id'] ?? null,
                        $user['referral_earnings'] ?? 0,
                        $user['withdrawals_count'] ?? 0,
                        $user['suspicious_activity_count'] ?? 0,
                        $user['blocked'] ?? false ? 1 : 0,
                        date('Y-m-d H:i:s'),
                        $telegramId
                    ]);
                    $updatedUsers++;
                } else {
                    // Создаем нового пользователя
                    $stmt = $pdo->prepare("
                        INSERT INTO users (
                            telegram_id, username, first_name, last_name,
                            language, balance, total_earned, referrer_id,
                            referral_earnings, withdrawals_count, 
                            suspicious_activity_count, blocked,
                            registered_at, last_activity, joined,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $telegramId,
                        $user['username'] ?? null,
                        $user['first_name'] ?? '',
                        $user['last_name'] ?? '',
                        $user['language'] ?? 'ru',
                        $user['balance'] ?? 0,
                        $user['total_earned'] ?? 0,
                        $user['referrer_id'] ?? null,
                        $user['referral_earnings'] ?? 0,
                        $user['withdrawals_count'] ?? 0,
                        $user['suspicious_activity_count'] ?? 0,
                        $user['blocked'] ?? false ? 1 : 0,
                        $user['registered_at'] ?? time(),
                        $user['last_activity'] ?? time(),
                        $user['joined'] ?? time(),
                        date('Y-m-d H:i:s'),
                        date('Y-m-d H:i:s')
                    ]);
                    $migratedUsers++;
                }
                
                // Мигрируем выплаты пользователя
                if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
                    foreach ($user['withdrawals'] as $withdrawal) {
                        // Проверяем, существует ли уже эта выплата
                        $existingWithdrawal = $pdo->prepare("
                            SELECT id FROM user_withdrawals 
                            WHERE user_id = ? AND amount = ? AND currency = ?
                        ");
                        $existingWithdrawal->execute([
                            $telegramId,
                            $withdrawal['coins_amount'] ?? $withdrawal['amount'] ?? 0,
                            $withdrawal['currency'] ?? 'TON'
                        ]);
                        
                        if (!$existingWithdrawal->fetch()) {
                            // Конвертируем статус
                            $status = 'pending';
                            if (isset($withdrawal['status'])) {
                                switch ($withdrawal['status']) {
                                    case 'finished':
                                    case 'completed':
                                        $status = 'completed';
                                        break;
                                    case 'failed':
                                    case 'error':
                                        $status = 'failed';
                                        break;
                                    case 'processing':
                                        $status = 'processing';
                                        break;
                                    case 'cancelled':
                                        $status = 'cancelled';
                                        break;
                                }
                            }
                            
                            $stmt = $pdo->prepare("
                                INSERT INTO user_withdrawals (
                                    user_id, amount, currency, wallet_address, status,
                                    transaction_hash, requested_at, processed_at, error_message
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            
                            $requestedAt = isset($withdrawal['timestamp']) 
                                ? date('Y-m-d H:i:s', $withdrawal['timestamp'])
                                : ($withdrawal['created_at'] ?? date('Y-m-d H:i:s'));
                            
                            $processedAt = null;
                            if ($status === 'completed' && isset($withdrawal['updated_at'])) {
                                $processedAt = $withdrawal['updated_at'];
                            }
                            
                            $stmt->execute([
                                $telegramId,
                                $withdrawal['coins_amount'] ?? $withdrawal['amount'] ?? 0,
                                strtoupper($withdrawal['currency'] ?? 'TON'),
                                $withdrawal['wallet_address'] ?? $withdrawal['address'] ?? '',
                                $status,
                                $withdrawal['hash'] ?? $withdrawal['transaction_hash'] ?? null,
                                $requestedAt,
                                $processedAt,
                                $withdrawal['error_message'] ?? null
                            ]);
                            
                            $migratedWithdrawals++;
                        }
                    }
                }
                
            } catch (Exception $e) {
                echo "   ❌ Ошибка с пользователем {$telegramId}: " . $e->getMessage() . "\n";
            }
        }
        
        echo "   ✅ Создано пользователей: {$migratedUsers}\n";
        echo "   🔄 Обновлено пользователей: {$updatedUsers}\n";
        echo "   💰 Мигрировано выплат: {$migratedWithdrawals}\n\n";
    }

    // 1.5. МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ
    echo "1.5. 📺 МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ\n";
    echo "===================================\n";

    $adViewsFile = 'database/ad_views.json';
    if (file_exists($adViewsFile)) {
        $adViews = json_decode(file_get_contents($adViewsFile), true);
        echo "📁 Найдено " . count($adViews) . " просмотров\n";

        $migratedViews = 0;
        foreach ($adViews as $view) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM coin_transactions
                    WHERE user_id = ? AND created_at = ? AND amount = ? AND transaction_type = 'earn'
                ");
                $existing->execute([
                    $view['user_id'],
                    $view['timestamp'],
                    $view['reward']
                ]);

                if (!$existing->fetch()) {
                    // Получаем текущий баланс пользователя
                    $balanceStmt = $pdo->prepare("SELECT balance FROM users WHERE telegram_id = ?");
                    $balanceStmt->execute([$view['user_id']]);
                    $currentBalance = $balanceStmt->fetchColumn() ?: 0;

                    $stmt = $pdo->prepare("
                        INSERT INTO coin_transactions (
                            user_id, amount, operation, transaction_type,
                            balance_before, balance_after, source_type,
                            description, metadata, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $view['user_id'],
                        $view['reward'],
                        'credit',
                        'earn',
                        $currentBalance,
                        $currentBalance + $view['reward'],
                        'ad_view',
                        "Ad reward for " . $view['ad_type'],
                        json_encode([
                            'ad_type' => $view['ad_type'],
                            'ip' => $view['ip'] ?? 'unknown',
                            'user_agent' => substr($view['user_agent'] ?? 'unknown', 0, 500),
                            'migrated' => true
                        ]),
                        $view['timestamp']
                    ]);
                    $migratedViews++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка просмотра: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано просмотров: {$migratedViews}\n\n";
    }

    // 1.6. МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ
    echo "1.6. 🖱️ МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ\n";
    echo "==================================\n";

    $adClicksFile = 'database/ad_clicks.json';
    if (file_exists($adClicksFile)) {
        $adClicks = json_decode(file_get_contents($adClicksFile), true);
        echo "📁 Найдено " . count($adClicks) . " кликов\n";

        $migratedClicks = 0;
        foreach ($adClicks as $click) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM ad_clicks
                    WHERE user_id = ? AND timestamp = ? AND click_type = ?
                ");
                $existing->execute([
                    $click['user_id'],
                    $click['timestamp'],
                    $click['click_type']
                ]);

                if (!$existing->fetch()) {
                    $stmt = $pdo->prepare("
                        INSERT INTO ad_clicks (
                            user_id, ad_type, click_type, reason,
                            timestamp, ip_address, user_agent
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $click['user_id'],
                        $click['ad_type'],
                        $click['click_type'],
                        $click['reason'] ?? null,
                        $click['timestamp'],
                        $click['ip'] ?? 'unknown',
                        substr($click['user_agent'] ?? 'unknown', 0, 500)
                    ]);
                    $migratedClicks++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка клика: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано кликов: {$migratedClicks}\n\n";
    }

    // 2. МИГРАЦИЯ FRAUD LOG
    echo "2. 🚨 МИГРАЦИЯ FRAUD LOG\n";
    echo "========================\n";

    $fraudLogFile = 'database/fraud_log.json';
    if (file_exists($fraudLogFile)) {
        $fraudLogs = json_decode(file_get_contents($fraudLogFile), true);
        echo "📁 Найдено " . count($fraudLogs) . " записей fraud log\n";

        $migratedFraud = 0;
        foreach ($fraudLogs as $log) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM fraud_logs
                    WHERE user_id = ? AND timestamp = ? AND violation_type = ?
                ");
                $existing->execute([
                    $log['user_id'],
                    $log['timestamp'],
                    $log['violation_type']
                ]);

                if (!$existing->fetch()) {
                    $stmt = $pdo->prepare("
                        INSERT INTO fraud_logs (
                            timestamp, user_id, violation_type, risk_level,
                            risk_score, action, details, fingerprint, ip_address
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $log['timestamp'],
                        $log['user_id'],
                        $log['violation_type'],
                        $log['risk_level'],
                        $log['risk_score'],
                        $log['action'],
                        $log['details'],
                        $log['fingerprint'] ?? null,
                        $log['ip_address'] ?? null
                    ]);
                    $migratedFraud++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка fraud log: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано: {$migratedFraud}\n\n";
    }

    // 3. МИГРАЦИЯ ADVANCED FINGERPRINTS
    echo "3. 🔍 МИГРАЦИЯ ADVANCED FINGERPRINTS\n";
    echo "====================================\n";

    $fingerprintsFile = 'database/advanced_fingerprints.json';
    if (file_exists($fingerprintsFile)) {
        $fingerprints = json_decode(file_get_contents($fingerprintsFile), true);
        echo "📁 Найдено " . count($fingerprints) . " отпечатков\n";

        $migratedFingerprints = 0;
        foreach ($fingerprints as $hash => $data) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM advanced_fingerprints WHERE fingerprint_hash = ?
                ");
                $existing->execute([$hash]);

                if (!$existing->fetch()) {
                    $stmt = $pdo->prepare("
                        INSERT INTO advanced_fingerprints (
                            user_id, fingerprint_hash, components
                        ) VALUES (?, ?, ?)
                    ");
                    $stmt->execute([
                        $data['user_id'],
                        $hash,
                        json_encode($data['components'] ?? [])
                    ]);
                    $migratedFingerprints++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка fingerprint: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано: {$migratedFingerprints}\n\n";
    }

    // 4. МИГРАЦИЯ VPN BLOCKS
    echo "4. 🚫 МИГРАЦИЯ VPN BLOCKS\n";
    echo "=========================\n";

    $vpnBlocksFile = 'database/vpn_blocks.json';
    if (file_exists($vpnBlocksFile)) {
        $vpnBlocks = json_decode(file_get_contents($vpnBlocksFile), true);
        echo "📁 Найдено " . count($vpnBlocks) . " VPN блокировок\n";

        $migratedVpnBlocks = 0;
        foreach ($vpnBlocks as $block) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM vpn_blocks WHERE ip_address = ?
                ");
                $existing->execute([$block['ip_address'] ?? $block['ip']]);

                if (!$existing->fetch()) {
                    $stmt = $pdo->prepare("
                        INSERT INTO vpn_blocks (ip_address, provider, reason)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([
                        $block['ip_address'] ?? $block['ip'],
                        $block['provider'] ?? null,
                        $block['reason'] ?? 'VPN detected'
                    ]);
                    $migratedVpnBlocks++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка VPN block: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано: {$migratedVpnBlocks}\n\n";
    }

    // 5. МИГРАЦИЯ USED TOKENS
    echo "5. 🎫 МИГРАЦИЯ USED TOKENS\n";
    echo "==========================\n";

    $usedTokensFile = 'database/used_richads_tokens.json';
    if (file_exists($usedTokensFile)) {
        $usedTokens = json_decode(file_get_contents($usedTokensFile), true);
        echo "📁 Найдено " . count($usedTokens) . " использованных токенов\n";

        $migratedTokens = 0;
        foreach ($usedTokens as $token => $data) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("SELECT id FROM used_tokens WHERE token = ?");
                $existing->execute([$token]);

                if (!$existing->fetch()) {
                    $stmt = $pdo->prepare("
                        INSERT INTO used_tokens (token, user_id, used_at)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([
                        $token,
                        $data['user_id'] ?? 0,
                        isset($data['used_at']) ? date('Y-m-d H:i:s', $data['used_at']) : date('Y-m-d H:i:s')
                    ]);
                    $migratedTokens++;
                }
            } catch (Exception $e) {
                echo "   ❌ Ошибка token: " . $e->getMessage() . "\n";
            }
        }

        echo "   ✅ Мигрировано: {$migratedTokens}\n\n";
    }

    echo "🎉 МИГРАЦИЯ ЗАВЕРШЕНА!\n";
    echo "======================\n";

    // Финальная статистика
    $finalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $finalWithdrawals = $pdo->query("SELECT COUNT(*) FROM user_withdrawals")->fetchColumn();
    $finalTransactions = $pdo->query("SELECT COUNT(*) FROM coin_transactions")->fetchColumn();
    $finalAdClicks = $pdo->query("SELECT COUNT(*) FROM ad_clicks")->fetchColumn();
    $finalFraud = $pdo->query("SELECT COUNT(*) FROM fraud_logs")->fetchColumn();
    $finalFingerprints = $pdo->query("SELECT COUNT(*) FROM advanced_fingerprints")->fetchColumn();
    $finalVpnBlocks = $pdo->query("SELECT COUNT(*) FROM vpn_blocks")->fetchColumn();
    $finalTokens = $pdo->query("SELECT COUNT(*) FROM used_tokens")->fetchColumn();
    $totalBalance = $pdo->query("SELECT SUM(balance) FROM users")->fetchColumn();

    echo "📊 ИТОГОВАЯ СТАТИСТИКА:\n";
    echo "   👥 Пользователей: {$finalUsers}\n";
    echo "   💰 Выплат: {$finalWithdrawals}\n";
    echo "   💎 Транзакций (просмотры): {$finalTransactions}\n";
    echo "   🖱️ Кликов: {$finalAdClicks}\n";
    echo "   🚨 Fraud записей: {$finalFraud}\n";
    echo "   🔍 Отпечатков: {$finalFingerprints}\n";
    echo "   🚫 VPN блокировок: {$finalVpnBlocks}\n";
    echo "   🎫 Использованных токенов: {$finalTokens}\n";
    echo "   💵 Общий баланс: " . number_format($totalBalance, 2) . " монет\n\n";

    echo "✅ Все данные с сервера успешно мигрированы в SQLite!\n";
    echo "🚀 Теперь можно тестировать систему с реальными данными.\n";

} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}
?>
