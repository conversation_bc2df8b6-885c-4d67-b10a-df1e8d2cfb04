@echo off
REM run_with_ospanel_php.bat
REM Запуск PHP скриптов с использованием OSPanel PHP (с поддержкой SQLite)

echo 🚀 Запуск с OSPanel PHP (поддержка SQLite включена)
echo ================================================

REM Устанавливаем путь к OSPanel PHP
set OSPANEL_PHP="d:\ospanel\modules\php\PHP_8.1\php.exe"

REM Проверяем, существует ли файл
if not exist %OSPANEL_PHP% (
    echo ❌ Ошибка: OSPanel PHP не найден по пути %OSPANEL_PHP%
    pause
    exit /b 1
)

REM Запускаем переданный скрипт
if "%1"=="" (
    echo ❌ Ошибка: Не указан PHP файл для запуска
    echo 💡 Использование: run_with_ospanel_php.bat script.php
    pause
    exit /b 1
)

echo 📄 Запускаем: %1
echo.

REM Запускаем PHP скрипт с OSPanel PHP
%OSPANEL_PHP% %1

echo.
echo ✅ Выполнение завершено
pause
