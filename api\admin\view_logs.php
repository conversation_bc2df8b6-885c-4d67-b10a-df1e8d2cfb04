<?php
/**
 * Просмотр логов ошибок для отладки webhook
 */

require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

include 'templates/header.php';

// Функция для чтения последних строк из лога
function getLastLogLines($logFile, $lines = 50) {
    if (!file_exists($logFile)) {
        return ["Файл лога не найден: {$logFile}"];
    }
    
    $content = file_get_contents($logFile);
    if (empty($content)) {
        return ["Файл лога пуст"];
    }
    
    $allLines = explode("\n", $content);
    $lastLines = array_slice($allLines, -$lines);
    
    // Фильтруем только строки связанные с webhook
    $webhookLines = array_filter($lastLines, function($line) {
        return stripos($line, 'webhook') !== false || 
               stripos($line, 'SUPPORT_WEBHOOK') !== false ||
               stripos($line, 'telegram') !== false ||
               stripos($line, 'bot') !== false;
    });
    
    return array_reverse($webhookLines); // Новые сверху
}

// Возможные пути к логам
$logPaths = [
    'PHP Error Log' => ini_get('error_log'),
    'Apache Error Log' => 'C:/OSPanel/logs/Apache_error.log',
    'Local Error Log' => __DIR__ . '/../../error.log',
    'Database Error Log' => __DIR__ . '/../../database/error.log'
];

?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">📋 Логи ошибок</h1>
                <button class="btn btn-primary" onclick="location.reload()">🔄 Обновить</button>
            </div>

            <div class="alert alert-info">
                <strong>💡 Совет:</strong> Попробуйте обновить webhook в настройках бота, затем обновите эту страницу для просмотра новых логов.
            </div>

            <?php foreach ($logPaths as $logName => $logPath): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <strong><?= htmlspecialchars($logName) ?></strong>
                        <small class="text-muted"><?= htmlspecialchars($logPath) ?></small>
                    </div>
                    <div class="card-body">
                        <?php
                        $logLines = getLastLogLines($logPath, 30);
                        if (empty($logLines) || (count($logLines) == 1 && empty(trim($logLines[0])))) {
                            echo '<p class="text-muted">Нет записей связанных с webhook</p>';
                        } else {
                            echo '<pre style="max-height: 300px; overflow-y: auto; font-size: 12px;">';
                            foreach ($logLines as $line) {
                                $line = trim($line);
                                if (empty($line)) continue;
                                
                                // Подсветка разных типов сообщений
                                if (stripos($line, 'ERROR') !== false) {
                                    echo '<span style="color: #dc3545;">' . htmlspecialchars($line) . '</span>' . "\n";
                                } elseif (stripos($line, 'SUCCESS') !== false) {
                                    echo '<span style="color: #28a745;">' . htmlspecialchars($line) . '</span>' . "\n";
                                } elseif (stripos($line, 'WARNING') !== false) {
                                    echo '<span style="color: #ffc107;">' . htmlspecialchars($line) . '</span>' . "\n";
                                } else {
                                    echo htmlspecialchars($line) . "\n";
                                }
                            }
                            echo '</pre>';
                        }
                        ?>
                    </div>
                </div>
            <?php endforeach; ?>

            <div class="card">
                <div class="card-header">
                    <strong>🔧 Действия для отладки</strong>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="test_webhook_direct.php" class="btn btn-primary">
                            🧪 Прямой тест webhook
                        </a>
                        <a href="webhook_diagnostics.php" class="btn btn-info">
                            🔍 Полная диагностика
                        </a>
                        <a href="bot_settings.php" class="btn btn-secondary">
                            ⚙️ Настройки бота
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Автообновление каждые 30 секунд
setTimeout(() => {
    location.reload();
}, 30000);
</script>

<?php include 'templates/footer.php'; ?>
