<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест миграции данных</title>
    <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: #000; padding: 20px; border-radius: 10px; border: 2px solid #00ff00; }
        .output { background: #111; padding: 15px; border-radius: 5px; white-space: pre-wrap; font-size: 14px; max-height: 600px; overflow-y: auto; }
        .button { background: #00ff00; color: #000; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 10px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #004400; border: 1px solid #00ff00; }
        .error { background: #440000; border: 1px solid #ff0000; color: #ff0000; }
        .info { background: #004444; border: 1px solid #00ffff; color: #00ffff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ТЕСТ МИГРАЦИИ ДАННЫХ</h1>
        
        <?php if (!isset($_GET['action'])): ?>
            <div class="status info">
                <h3>Выберите действие:</h3>
            </div>
            <div style="text-align: center;">
                <a href="?action=test_connection" class="button">🔍 ТЕСТ ПОДКЛЮЧЕНИЯ</a>
                <a href="?action=migrate" class="button">🚀 МИГРАЦИЯ ДАННЫХ</a>
                <a href="?action=check_data" class="button">📊 ПРОВЕРИТЬ ДАННЫЕ</a>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['action'])): ?>
            <div class="output">
<?php
            try {
                switch ($_GET['action']) {
                    case 'test_connection':
                        echo "🔍 ТЕСТ ПОДКЛЮЧЕНИЯ К SQLITE\n";
                        echo "============================\n\n";
                        
                        // Проверяем файлы
                        $dbPath = __DIR__ . '/database/app.sqlite';
                        echo "📁 База данных: " . $dbPath . "\n";
                        echo "   Существует: " . (file_exists($dbPath) ? "✅ ДА" : "❌ НЕТ") . "\n";
                        echo "   Размер: " . (file_exists($dbPath) ? filesize($dbPath) . " байт" : "0 байт") . "\n\n";
                        
                        // Тест PDO
                        try {
                            $pdo = new PDO('sqlite:' . $dbPath);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo "✅ PDO подключение успешно\n";
                            
                            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
                            echo "📋 Найдено таблиц: " . count($tables) . "\n";
                            foreach ($tables as $table) {
                                echo "   - {$table}\n";
                            }
                            
                            if (in_array('users', $tables)) {
                                $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                                echo "👥 Пользователей: {$userCount}\n";
                            }
                            
                        } catch (Exception $e) {
                            echo "❌ Ошибка PDO: " . $e->getMessage() . "\n";
                        }
                        
                        // Проверяем JSON файлы
                        echo "\n📁 JSON ФАЙЛЫ:\n";
                        $files = ['user_data.json', 'ad_views.json', 'ad_clicks.json'];
                        foreach ($files as $file) {
                            $path = "database/{$file}";
                            if (file_exists($path)) {
                                $data = json_decode(file_get_contents($path), true);
                                $count = is_array($data) ? count($data) : 0;
                                echo "   ✅ {$file}: {$count} записей\n";
                            } else {
                                echo "   ❌ {$file}: НЕ НАЙДЕН\n";
                            }
                        }
                        break;
                        
                    case 'migrate':
                        echo "🚀 ЗАПУСК МИГРАЦИИ ДАННЫХ\n";
                        echo "=========================\n\n";
                        
                        // Простая миграция без внешних классов
                        $dbPath = __DIR__ . '/database/app.sqlite';
                        $pdo = new PDO('sqlite:' . $dbPath);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        echo "✅ Подключение к базе установлено\n\n";
                        
                        // 1. Миграция пользователей
                        echo "1. 👥 МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ\n";
                        $userDataFile = 'database/user_data.json';
                        if (file_exists($userDataFile)) {
                            $userData = json_decode(file_get_contents($userDataFile), true);
                            echo "📁 Найдено " . count($userData) . " пользователей\n";
                            
                            $migrated = 0;
                            $updated = 0;
                            
                            foreach ($userData as $telegramId => $user) {
                                try {
                                    // Проверяем существование
                                    $existing = $pdo->prepare("SELECT id FROM users WHERE telegram_id = ?");
                                    $existing->execute([$telegramId]);
                                    
                                    if ($existing->fetch()) {
                                        // Обновляем
                                        $stmt = $pdo->prepare("
                                            UPDATE users SET 
                                                username = ?, first_name = ?, last_name = ?, 
                                                balance = ?, total_earned = ?, updated_at = ?
                                            WHERE telegram_id = ?
                                        ");
                                        $stmt->execute([
                                            $user['username'] ?? null,
                                            $user['first_name'] ?? '',
                                            $user['last_name'] ?? '',
                                            $user['balance'] ?? 0,
                                            $user['total_earned'] ?? 0,
                                            date('Y-m-d H:i:s'),
                                            $telegramId
                                        ]);
                                        $updated++;
                                    } else {
                                        // Создаем
                                        $stmt = $pdo->prepare("
                                            INSERT INTO users (
                                                telegram_id, username, first_name, last_name,
                                                language_code, balance, total_earned, 
                                                created_at, updated_at
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                        ");
                                        $stmt->execute([
                                            $telegramId,
                                            $user['username'] ?? null,
                                            $user['first_name'] ?? '',
                                            $user['last_name'] ?? '',
                                            $user['language'] ?? 'ru',
                                            $user['balance'] ?? 0,
                                            $user['total_earned'] ?? 0,
                                            date('Y-m-d H:i:s'),
                                            date('Y-m-d H:i:s')
                                        ]);
                                        $migrated++;
                                    }
                                } catch (Exception $e) {
                                    echo "   ❌ Ошибка с пользователем {$telegramId}: " . $e->getMessage() . "\n";
                                }
                            }
                            
                            echo "   ✅ Создано: {$migrated}\n";
                            echo "   🔄 Обновлено: {$updated}\n\n";
                        }
                        
                        // 2. Миграция просмотров рекламы
                        echo "2. 📺 МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ\n";
                        $adViewsFile = 'database/ad_views.json';
                        if (file_exists($adViewsFile)) {
                            $adViews = json_decode(file_get_contents($adViewsFile), true);
                            echo "📁 Найдено " . count($adViews) . " просмотров\n";
                            
                            $migrated = 0;
                            foreach ($adViews as $view) {
                                try {
                                    // Проверяем дубликаты
                                    $existing = $pdo->prepare("
                                        SELECT id FROM coin_transactions 
                                        WHERE user_id = ? AND created_at = ? AND amount = ?
                                    ");
                                    $existing->execute([
                                        $view['user_id'],
                                        $view['timestamp'],
                                        $view['reward']
                                    ]);
                                    
                                    if (!$existing->fetch()) {
                                        $stmt = $pdo->prepare("
                                            INSERT INTO coin_transactions (
                                                user_id, amount, operation, transaction_type,
                                                from_user_id, description, created_at
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                        ");
                                        $stmt->execute([
                                            $view['user_id'],
                                            $view['reward'],
                                            'credit',
                                            'ad_reward',
                                            'system',
                                            "Ad reward for " . $view['ad_type'],
                                            $view['timestamp']
                                        ]);
                                        $migrated++;
                                    }
                                } catch (Exception $e) {
                                    echo "   ❌ Ошибка просмотра: " . $e->getMessage() . "\n";
                                }
                            }
                            
                            echo "   ✅ Мигрировано: {$migrated}\n\n";
                        }
                        
                        // 3. Создание тестовых выплат
                        echo "3. 💰 СОЗДАНИЕ ТЕСТОВЫХ ВЫПЛАТ\n";
                        $topUsers = $pdo->query("
                            SELECT telegram_id, first_name, balance 
                            FROM users 
                            WHERE balance > 50 
                            ORDER BY balance DESC 
                            LIMIT 5
                        ")->fetchAll(PDO::FETCH_ASSOC);
                        
                        if ($topUsers) {
                            $statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
                            $created = 0;
                            
                            foreach ($topUsers as $index => $user) {
                                $amount = min($user['balance'] * 0.3, 100);
                                $status = $statuses[$index % count($statuses)];
                                
                                $stmt = $pdo->prepare("
                                    INSERT INTO user_withdrawals (
                                        user_id, amount, currency, wallet_address, status,
                                        admin_notes, created_at, updated_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                ");
                                $stmt->execute([
                                    $user['telegram_id'],
                                    $amount,
                                    'TON',
                                    'EQD' . bin2hex(random_bytes(24)),
                                    $status,
                                    "Тестовая выплата (статус: {$status})",
                                    date('Y-m-d H:i:s', time() - rand(3600, 86400)),
                                    date('Y-m-d H:i:s')
                                ]);
                                $created++;
                                
                                echo "   ✅ Создана выплата для {$user['first_name']} ({$amount} TON, {$status})\n";
                            }
                            
                            echo "   📊 Всего создано: {$created}\n\n";
                        }
                        
                        echo "🎉 МИГРАЦИЯ ЗАВЕРШЕНА!\n";
                        break;
                        
                    case 'check_data':
                        echo "📊 ПРОВЕРКА ДАННЫХ\n";
                        echo "==================\n\n";
                        
                        $dbPath = __DIR__ . '/database/app.sqlite';
                        $pdo = new PDO('sqlite:' . $dbPath);
                        
                        $users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                        $transactions = $pdo->query("SELECT COUNT(*) FROM coin_transactions")->fetchColumn();
                        $withdrawals = $pdo->query("SELECT COUNT(*) FROM user_withdrawals")->fetchColumn();
                        $totalBalance = $pdo->query("SELECT SUM(balance) FROM users")->fetchColumn();
                        
                        echo "👥 Пользователей: {$users}\n";
                        echo "💰 Транзакций: {$transactions}\n";
                        echo "💸 Выплат: {$withdrawals}\n";
                        echo "💎 Общий баланс: " . number_format($totalBalance, 2) . " монет\n\n";
                        
                        if ($withdrawals > 0) {
                            echo "📋 Статистика выплат:\n";
                            $stats = $pdo->query("
                                SELECT status, COUNT(*) as count, SUM(amount) as total 
                                FROM user_withdrawals 
                                GROUP BY status
                            ")->fetchAll(PDO::FETCH_ASSOC);
                            
                            foreach ($stats as $stat) {
                                echo "   - {$stat['status']}: {$stat['count']} шт. ({$stat['total']} TON)\n";
                            }
                        }
                        break;
                }
                
            } catch (Exception $e) {
                echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
                echo "Стек: " . $e->getTraceAsString() . "\n";
            }
?>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="?" class="button">🔙 НАЗАД</a>
                <a href="demo_withdrawal_history.html" class="button">📋 ТЕСТ ВЫПЛАТ</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
