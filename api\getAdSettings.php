<?php
/**
 * api/getAdSettings.php
 * API для получения настроек рекламы (награды за типы рекламы)
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Получаем все настройки рекламы из базы данных
    $adSettings = $db->query(
        "SELECT setting_key, setting_value, description 
         FROM coin_settings 
         WHERE setting_key LIKE 'ad_reward_%' 
         ORDER BY setting_key"
    );
    
    // Формируем массив наград
    $rewards = [];
    $fallbackRewards = [
        'ad_reward_native_banner' => 10,
        'ad_reward_rewarded_video' => 1,
        'ad_reward_interstitial' => 10
    ];
    
    // Заполняем награды из базы данных
    foreach ($adSettings as $setting) {
        $key = $setting['setting_key'];
        $value = (int)$setting['setting_value'];
        
        // Преобразуем ключ в тип рекламы
        $adType = str_replace('ad_reward_', '', $key);
        $rewards[$adType] = $value;
    }
    
    // Добавляем fallback значения для отсутствующих типов
    foreach ($fallbackRewards as $key => $value) {
        $adType = str_replace('ad_reward_', '', $key);
        if (!isset($rewards[$adType])) {
            $rewards[$adType] = $value;
        }
    }
    
    // Получаем дополнительные настройки системы
    $systemSettings = $coinsManager->getSystemSettings();
    
    // Получаем настройки cooldown (если есть)
    $cooldownSettings = $db->query(
        "SELECT setting_key, setting_value 
         FROM coin_settings 
         WHERE setting_key LIKE '%cooldown%' OR setting_key LIKE '%limit%'"
    );
    
    $cooldowns = [];
    $limits = [];
    
    foreach ($cooldownSettings as $setting) {
        $key = $setting['setting_key'];
        $value = $setting['setting_value'];
        
        if (strpos($key, 'cooldown') !== false) {
            $cooldowns[$key] = (int)$value;
        } elseif (strpos($key, 'limit') !== false) {
            $limits[$key] = (int)$value;
        }
    }
    
    // Маппинг типов рекламы для совместимости
    $typeMapping = [
        'native_banner' => ['banner', 'link', 'native'],
        'rewarded_video' => ['video', 'rewarded', 'watch'],
        'interstitial' => ['interstitial', 'fullscreen', 'popup']
    ];
    
    // Создаем расширенный массив наград с альтернативными названиями
    $extendedRewards = $rewards;
    foreach ($typeMapping as $mainType => $aliases) {
        if (isset($rewards[$mainType])) {
            foreach ($aliases as $alias) {
                $extendedRewards[$alias] = $rewards[$mainType];
            }
        }
    }
    
    // Формируем ответ
    $response = [
        'success' => true,
        'ad_rewards' => $rewards,
        'ad_rewards_extended' => $extendedRewards, // С альтернативными названиями
        'system_settings' => [
            'daily_earn_limit' => (int)($systemSettings['daily_earn_limit'] ?? 200),
            'coin_rate_usd' => (float)($systemSettings['coin_rate_usd'] ?? 0.001),
            'min_withdrawal_coins' => (int)($systemSettings['min_withdrawal_coins'] ?? 1000)
        ],
        'cooldowns' => $cooldowns,
        'limits' => $limits,
        'type_mapping' => $typeMapping,
        'api_info' => [
            'version' => '1.0',
            'source' => 'sqlite_coin_settings',
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ];
    
    // Логируем успешный запрос
    error_log("getAdSettings SUCCESS: Returned " . count($rewards) . " ad reward settings");
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("getAdSettings ERROR: " . $e->getMessage());
    
    // В случае ошибки возвращаем fallback значения
    $fallbackResponse = [
        'success' => false,
        'error' => 'Failed to load ad settings',
        'fallback_rewards' => [
            'native_banner' => 10,
            'rewarded_video' => 1,
            'interstitial' => 10,
            'banner' => 5,
            'video' => 10,
            'rewarded' => 15
        ],
        'system_settings' => [
            'daily_earn_limit' => 200,
            'coin_rate_usd' => 0.001,
            'min_withdrawal_coins' => 1000
        ],
        'api_info' => [
            'version' => '1.0',
            'source' => 'fallback',
            'error_message' => $e->getMessage(),
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ];
    
    http_response_code(500);
    echo json_encode($fallbackResponse, JSON_UNESCAPED_UNICODE);
}
?>
