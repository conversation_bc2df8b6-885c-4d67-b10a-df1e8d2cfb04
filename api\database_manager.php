<?php
/**
 * api/database_manager.php
 * Универсальный менеджер базы данных для замены JSON файлов
 */

declare(strict_types=1);

class DatabaseManager
{
    private static ?DatabaseManager $instance = null;
    private PDO $pdo;
    private string $dbPath;

    private function __construct()
    {
        $this->dbPath = __DIR__ . '/../database/app.sqlite';
        $this->initializeConnection();
    }

    /**
     * Получение единственного экземпляра (Singleton)
     */
    public static function getInstance(): DatabaseManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Инициализация подключения к базе данных
     */
    private function initializeConnection(): void
    {
        try {
            $this->pdo = new PDO("sqlite:{$this->dbPath}");
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $this->pdo->exec('PRAGMA foreign_keys = ON');
        } catch (PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new Exception("Ошибка подключения к базе данных");
        }
    }

    /**
     * ПОЛЬЗОВАТЕЛИ - Загрузка данных пользователей (замена loadUserData)
     */
    public function loadUserData(): array
    {
        try {
            $stmt = $this->pdo->query("
                SELECT telegram_id, balance, total_earned, referrer_id, referral_earnings,
                       first_name, last_name, username, language, registered_at, last_activity,
                       suspicious_activity_count, withdrawals_count, blocked, blocked_at,
                       referrals_count, joined, suspicious_activity
                FROM users
            ");
            
            $users = [];
            while ($row = $stmt->fetch()) {
                $telegramId = $row['telegram_id'];
                
                // Преобразуем в формат совместимый со старым JSON
                $users[$telegramId] = [
                    'id' => $telegramId,
                    'balance' => (float)$row['balance'],
                    'total_earned' => (float)$row['total_earned'],
                    'referrer_id' => $row['referrer_id'],
                    'referral_earnings' => (float)$row['referral_earnings'],
                    'first_name' => $row['first_name'] ?? '',
                    'last_name' => $row['last_name'] ?? '',
                    'username' => $row['username'] ?? '',
                    'language' => $row['language'] ?? 'ru',
                    'registered_at' => $row['registered_at'],
                    'last_activity' => $row['last_activity'],
                    'suspicious_activity_count' => $row['suspicious_activity_count'],
                    'withdrawals_count' => $row['withdrawals_count'],
                    'blocked' => (bool)$row['blocked'],
                    'blocked_at' => $row['blocked_at'],
                    'referrals_count' => $row['referrals_count'],
                    'joined' => $row['joined'],
                    'suspicious_activity' => $row['suspicious_activity'],
                    
                    // Дополнительные поля для совместимости
                    'withdrawals' => $this->getUserWithdrawals($telegramId),
                    'withdrawal_log' => $this->getUserWithdrawalLog($telegramId),
                    'referrals' => $this->getUserReferrals($telegramId),
                    'ad_views_log' => $this->getUserAdViewsLog($telegramId)
                ];
            }
            
            return $users;
            
        } catch (PDOException $e) {
            error_log("Error loading user data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * ПОЛЬЗОВАТЕЛИ - Сохранение данных пользователей (замена saveUserData)
     */
    public function saveUserData(array $userData): bool
    {
        try {
            $this->pdo->beginTransaction();
            
            $stmt = $this->pdo->prepare("
                INSERT OR REPLACE INTO users (
                    telegram_id, balance, total_earned, referrer_id, referral_earnings,
                    first_name, last_name, username, language, registered_at, last_activity,
                    suspicious_activity_count, withdrawals_count, blocked, blocked_at,
                    referrals_count, joined, suspicious_activity, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ");

            foreach ($userData as $telegramId => $user) {
                $stmt->execute([
                    $telegramId,
                    $user['balance'] ?? 0,
                    $user['total_earned'] ?? 0,
                    $user['referrer_id'] ?? null,
                    $user['referral_earnings'] ?? 0,
                    $user['first_name'] ?? '',
                    $user['last_name'] ?? '',
                    $user['username'] ?? '',
                    $user['language'] ?? 'ru',
                    $user['registered_at'] ?? time(),
                    $user['last_activity'] ?? time(),
                    $user['suspicious_activity_count'] ?? 0,
                    $user['withdrawals_count'] ?? 0,
                    $user['blocked'] ?? false,
                    $user['blocked_at'] ?? null,
                    $user['referrals_count'] ?? 0,
                    $user['joined'] ?? time(),
                    $user['suspicious_activity'] ?? 0
                ]);
            }
            
            $this->pdo->commit();
            return true;
            
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log("Error saving user data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * ПОЛЬЗОВАТЕЛИ - Получение данных конкретного пользователя
     */
    public function getUserDetails(int $telegramId): ?array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM users WHERE telegram_id = ?
            ");
            $stmt->execute([$telegramId]);
            
            $user = $stmt->fetch();
            if (!$user) {
                return null;
            }
            
            // Преобразуем в формат совместимый со старым JSON
            return [
                'id' => $user['telegram_id'],
                'balance' => (float)$user['balance'],
                'total_earned' => (float)$user['total_earned'],
                'referrer_id' => $user['referrer_id'],
                'referral_earnings' => (float)$user['referral_earnings'],
                'first_name' => $user['first_name'] ?? '',
                'last_name' => $user['last_name'] ?? '',
                'username' => $user['username'] ?? '',
                'language' => $user['language'] ?? 'ru',
                'registered_at' => $user['registered_at'],
                'last_activity' => $user['last_activity'],
                'suspicious_activity_count' => $user['suspicious_activity_count'],
                'withdrawals_count' => $user['withdrawals_count'],
                'blocked' => (bool)$user['blocked'],
                'blocked_at' => $user['blocked_at'],
                'referrals_count' => $user['referrals_count'],
                'joined' => $user['joined'],
                'suspicious_activity' => $user['suspicious_activity'],
                
                // Дополнительные поля
                'withdrawals' => $this->getUserWithdrawals($user['telegram_id']),
                'withdrawal_log' => $this->getUserWithdrawalLog($user['telegram_id']),
                'referrals' => $this->getUserReferrals($user['telegram_id']),
                'ad_views_log' => $this->getUserAdViewsLog($user['telegram_id'])
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting user details: " . $e->getMessage());
            return null;
        }
    }

    /**
     * РЕКЛАМА - Логирование просмотра рекламы
     */
    public function logAdView(int $userId, string $adType, float $reward, string $ip = null, string $userAgent = null): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO ad_views (user_id, ad_type, reward, timestamp, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $userId,
                $adType,
                $reward,
                date('Y-m-d H:i:s'),
                $ip,
                $userAgent
            ]);
            
        } catch (PDOException $e) {
            error_log("Error logging ad view: " . $e->getMessage());
            return false;
        }
    }

    /**
     * РЕКЛАМА - Логирование клика по рекламе
     */
    public function logAdClick(int $userId, string $adType, string $clickType, string $reason = '', string $ip = null, string $userAgent = null): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO ad_clicks (user_id, ad_type, click_type, reason, timestamp, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            return $stmt->execute([
                $userId,
                $adType,
                $clickType,
                $reason,
                date('Y-m-d H:i:s'),
                $ip,
                $userAgent
            ]);
            
        } catch (PDOException $e) {
            error_log("Error logging ad click: " . $e->getMessage());
            return false;
        }
    }

    /**
     * РЕКЛАМА - Получение лимитов пользователя
     */
    public function getUserAdLimits(int $userId): array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ad_type, daily_count, last_reset_date
                FROM ad_limits 
                WHERE user_id = ? AND last_reset_date = ?
            ");
            $stmt->execute([$userId, date('Y-m-d')]);
            
            $limits = [];
            while ($row = $stmt->fetch()) {
                $limits[$row['ad_type']] = $row['daily_count'];
            }
            
            return $limits;
            
        } catch (PDOException $e) {
            error_log("Error getting user ad limits: " . $e->getMessage());
            return [];
        }
    }

    /**
     * РЕКЛАМА - Обновление лимитов пользователя
     */
    public function updateUserAdLimit(int $userId, string $adType, int $count): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT OR REPLACE INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                VALUES (?, ?, ?, ?)
            ");
            
            return $stmt->execute([$userId, $adType, $count, date('Y-m-d')]);
            
        } catch (PDOException $e) {
            error_log("Error updating user ad limit: " . $e->getMessage());
            return false;
        }
    }

    /**
     * ТОКЕНЫ - Создание токена рекламы
     */
    public function createAdToken(string $token, int $userId, string $adType, int $expiresIn = 300): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO ad_tokens (token, user_id, ad_type, used, created_at, expires_at)
                VALUES (?, ?, ?, 0, ?, ?)
            ");
            
            $now = time();
            return $stmt->execute([$token, $userId, $adType, $now, $now + $expiresIn]);
            
        } catch (PDOException $e) {
            error_log("Error creating ad token: " . $e->getMessage());
            return false;
        }
    }

    /**
     * ТОКЕНЫ - Проверка и использование токена
     */
    public function useAdToken(string $token): ?array
    {
        try {
            $this->pdo->beginTransaction();
            
            // Проверяем токен
            $stmt = $this->pdo->prepare("
                SELECT * FROM ad_tokens 
                WHERE token = ? AND used = 0 AND expires_at > ?
            ");
            $stmt->execute([$token, time()]);
            
            $tokenData = $stmt->fetch();
            if (!$tokenData) {
                $this->pdo->rollBack();
                return null;
            }
            
            // Помечаем токен как использованный
            $updateStmt = $this->pdo->prepare("
                UPDATE ad_tokens 
                SET used = 1, used_at = ? 
                WHERE token = ?
            ");
            $updateStmt->execute([time(), $token]);
            
            $this->pdo->commit();
            
            return [
                'user_id' => $tokenData['user_id'],
                'ad_type' => $tokenData['ad_type']
            ];
            
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log("Error using ad token: " . $e->getMessage());
            return null;
        }
    }

    /**
     * НАСТРОЙКИ БОТА - Получение настроек
     */
    public function getBotSettings(): array
    {
        try {
            $stmt = $this->pdo->query("
                SELECT setting_key, setting_value 
                FROM bot_settings
            ");
            
            $settings = [];
            while ($row = $stmt->fetch()) {
                $value = $row['setting_value'];
                
                // Пытаемся декодировать JSON
                $decoded = json_decode($value, true);
                $settings[$row['setting_key']] = $decoded !== null ? $decoded : $value;
            }
            
            return $settings;
            
        } catch (PDOException $e) {
            error_log("Error getting bot settings: " . $e->getMessage());
            return [];
        }
    }

    /**
     * НАСТРОЙКИ БОТА - Сохранение настроек
     */
    public function saveBotSettings(array $settings): bool
    {
        try {
            $this->pdo->beginTransaction();
            
            $stmt = $this->pdo->prepare("
                INSERT OR REPLACE INTO bot_settings (setting_key, setting_value, updated_by)
                VALUES (?, ?, ?)
            ");
            
            foreach ($settings as $key => $value) {
                if ($key === 'updated_by') continue;
                
                $settingValue = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
                $updatedBy = $settings['updated_by'] ?? 'system';
                
                $stmt->execute([$key, $settingValue, $updatedBy]);
            }
            
            $this->pdo->commit();
            return true;
            
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log("Error saving bot settings: " . $e->getMessage());
            return false;
        }
    }

    /**
     * ТЕКСТЫ БОТА - Получение текстов
     */
    public function getBotTexts(string $language = 'ru'): array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT text_key, text_value, category
                FROM bot_texts 
                WHERE language_code = ?
                ORDER BY category, text_key
            ");
            $stmt->execute([$language]);
            
            $texts = [];
            while ($row = $stmt->fetch()) {
                $category = $row['category'];
                $key = $row['text_key'];
                $value = $row['text_value'];
                
                // Убираем префикс категории из ключа
                if (strpos($key, $category . '.') === 0) {
                    $key = substr($key, strlen($category) + 1);
                }
                
                // Пытаемся декодировать JSON
                $decoded = json_decode($value, true);
                $finalValue = $decoded !== null ? $decoded : $value;
                
                if (!isset($texts[$category])) {
                    $texts[$category] = [];
                }
                
                $texts[$category][$key] = $finalValue;
            }
            
            return $texts;
            
        } catch (PDOException $e) {
            error_log("Error getting bot texts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Вспомогательные методы для совместимости
     */
    private function getUserWithdrawals(int $telegramId): array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM user_withdrawals 
                WHERE user_id = ? 
                ORDER BY requested_at DESC
            ");
            $stmt->execute([$telegramId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }

    private function getUserWithdrawalLog(int $telegramId): array
    {
        // Для совместимости - возвращаем тот же формат что и withdrawals
        return $this->getUserWithdrawals($telegramId);
    }

    private function getUserReferrals(int $telegramId): array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT referred_id FROM user_referrals 
                WHERE referrer_id = ?
            ");
            $stmt->execute([$telegramId]);
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            return [];
        }
    }

    private function getUserAdViewsLog(int $telegramId): array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT UNIX_TIMESTAMP(timestamp) as timestamp
                FROM ad_views 
                WHERE user_id = ? 
                ORDER BY timestamp DESC
                LIMIT 100
            ");
            $stmt->execute([$telegramId]);
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * Получение PDO объекта для прямых запросов
     */
    public function getPdo(): PDO
    {
        return $this->pdo;
    }
}
?>
