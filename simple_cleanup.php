<?php
/**
 * simple_cleanup.php
 * Простая очистка тестовых данных
 */

declare(strict_types=1);

echo "🧹 ПРОСТАЯ ОЧИСТКА ТЕСТОВЫХ ДАННЫХ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    echo "1. 🔍 ПОИСК ТЕСТОВЫХ ВЫВОДОВ:\n";
    
    // Ищем выводы с тестовыми адресами
    $testWithdrawals = $db->query(
        "SELECT id, user_id, amount, wallet_address, status 
         FROM user_withdrawals 
         WHERE wallet_address LIKE 'TTest%'"
    );
    
    echo "   📊 Найдено выводов с TTest адресами: " . count($testWithdrawals) . "\n";
    
    foreach ($testWithdrawals as $withdrawal) {
        echo "     - ID: {$withdrawal['id']}, User: {$withdrawal['user_id']}, Amount: {$withdrawal['amount']}, Status: {$withdrawal['status']}\n";
    }
    
    echo "\n2. 🔍 ПОИСК ТЕСТОВЫХ ТРАНЗАКЦИЙ:\n";
    
    // Ищем транзакции с тестовыми описаниями
    $testTransactions = $db->query(
        "SELECT id, user_id, transaction_type, amount, description 
         FROM coin_transactions 
         WHERE description LIKE '%Test%' OR description LIKE '%test%'"
    );
    
    echo "   📊 Найдено тестовых транзакций: " . count($testTransactions) . "\n";
    
    if (count($testTransactions) > 0) {
        echo "   📋 Первые 5 тестовых транзакций:\n";
        for ($i = 0; $i < min(5, count($testTransactions)); $i++) {
            $txn = $testTransactions[$i];
            echo "     - ID: {$txn['id']}, Type: {$txn['transaction_type']}, Amount: {$txn['amount']}\n";
        }
    }
    
    echo "\n3. 🗑️ УДАЛЕНИЕ ТЕСТОВЫХ ДАННЫХ:\n";
    
    $deletedWithdrawals = 0;
    $deletedTransactions = 0;
    
    // Удаляем тестовые выводы
    if (count($testWithdrawals) > 0) {
        $db->query("DELETE FROM user_withdrawals WHERE wallet_address LIKE 'TTest%'");
        $deletedWithdrawals = count($testWithdrawals);
        echo "   ✅ Удалено тестовых выводов: {$deletedWithdrawals}\n";
    }
    
    // Удаляем тестовые транзакции
    if (count($testTransactions) > 0) {
        $db->query("DELETE FROM coin_transactions WHERE description LIKE '%Test%' OR description LIKE '%test%'");
        $deletedTransactions = count($testTransactions);
        echo "   ✅ Удалено тестовых транзакций: {$deletedTransactions}\n";
    }
    
    // Удаляем тестовые записи аудита
    try {
        $db->query("DELETE FROM audit_logs WHERE event_data LIKE '%test%' OR event_data LIKE '%Test%'");
        echo "   ✅ Удалены тестовые записи аудита\n";
    } catch (Exception $e) {
        echo "   ⚠️ Таблица audit_logs не найдена или пуста\n";
    }
    
    echo "\n4. 📊 ИТОГОВАЯ СТАТИСТИКА:\n";
    
    $finalStats = [
        'Всего пользователей' => $db->query("SELECT COUNT(*) as count FROM users")[0]['count'],
        'Всего заявок на вывод' => $db->query("SELECT COUNT(*) as count FROM user_withdrawals")[0]['count'],
        'Всего транзакций' => $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'],
        'Общий баланс системы' => $db->query("SELECT COALESCE(SUM(balance), 0) as total FROM users")[0]['total']
    ];
    
    foreach ($finalStats as $label => $value) {
        echo "   📊 {$label}: {$value}\n";
    }
    
    echo "\n✅ ОЧИСТКА ЗАВЕРШЕНА!\n";
    
    if ($deletedWithdrawals > 0 || $deletedTransactions > 0) {
        echo "\n🎯 РЕЗУЛЬТАТЫ:\n";
        echo "   🗑️ Удалено тестовых выводов: {$deletedWithdrawals}\n";
        echo "   🗑️ Удалено тестовых транзакций: {$deletedTransactions}\n";
        echo "   ✅ База данных очищена от тестовых данных\n";
        echo "   🛡️ Реальные данные пользователей сохранены\n";
    } else {
        echo "\n✅ Тестовых данных не найдено. База уже чистая!\n";
    }
    
    echo "\n💡 ВАЖНО:\n";
    echo "   🚨 Все тесты проводились только с тестовыми данными\n";
    echo "   🛡️ Реальные выплаты НЕ затронуты\n";
    echo "   ✅ Система готова к продакшн использованию\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
