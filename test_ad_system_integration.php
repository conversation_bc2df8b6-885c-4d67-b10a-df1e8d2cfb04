<?php
/**
 * test_ad_system_integration.php
 * Тест интеграции системы рекламы с SQLite
 */

declare(strict_types=1);

echo "📊 ТЕСТ СИСТЕМЫ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ТЕКУЩАЯ СТАТИСТИКА В SQLITE:\n";
    
    // Просмотры рекламы
    $viewStats = $sqlite->query("SELECT COUNT(*) as total, SUM(reward) as total_rewards FROM ad_views");
    $totalViews = $viewStats[0]['total'] ?? 0;
    $totalRewards = $viewStats[0]['total_rewards'] ?? 0;
    
    echo "   👁️ Всего просмотров: {$totalViews}\n";
    echo "   💰 Всего наград: {$totalRewards}\n";
    
    // Клики по рекламе
    $clickStats = $sqlite->query("SELECT COUNT(*) as total FROM ad_clicks");
    $totalClicks = $clickStats[0]['total'] ?? 0;
    
    echo "   🖱️ Всего кликов: {$totalClicks}\n";
    
    // CTR
    $ctr = $totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0;
    echo "   📈 CTR: {$ctr}%\n";
    
    echo "\n2. 📊 СТАТИСТИКА ПО ТИПАМ РЕКЛАМЫ:\n";
    
    // Просмотры по типам
    $viewsByType = $sqlite->query("
        SELECT ad_type, COUNT(*) as views, SUM(reward) as rewards 
        FROM ad_views 
        GROUP BY ad_type 
        ORDER BY views DESC
    ");
    
    foreach ($viewsByType as $view) {
        echo "   👁️ {$view['ad_type']}: {$view['views']} просмотров, {$view['rewards']} наград\n";
    }
    
    // Клики по типам
    $clicksByType = $sqlite->query("
        SELECT ad_type, COUNT(*) as clicks 
        FROM ad_clicks 
        GROUP BY ad_type 
        ORDER BY clicks DESC
    ");
    
    foreach ($clicksByType as $click) {
        echo "   🖱️ {$click['ad_type']}: {$click['clicks']} кликов\n";
    }
    
    echo "\n3. 🕒 ПОСЛЕДНИЕ АКТИВНОСТИ:\n";
    
    // Последние просмотры
    $recentViews = $sqlite->query("
        SELECT user_id, ad_type, reward, timestamp 
        FROM ad_views 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    echo "   👁️ Последние просмотры:\n";
    foreach ($recentViews as $view) {
        echo "      User {$view['user_id']}: {$view['ad_type']} (+{$view['reward']}) - {$view['timestamp']}\n";
    }
    
    // Последние клики
    $recentClicks = $sqlite->query("
        SELECT user_id, ad_type, click_type, timestamp 
        FROM ad_clicks 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    echo "   🖱️ Последние клики:\n";
    foreach ($recentClicks as $click) {
        echo "      User {$click['user_id']}: {$click['ad_type']} ({$click['click_type']}) - {$click['timestamp']}\n";
    }
    
    echo "\n4. 🌍 СТАТИСТИКА ПО IP:\n";
    
    // IP статистика для просмотров
    $ipStats = $sqlite->query("
        SELECT 
            CASE 
                WHEN ip_address IS NULL OR ip_address = '' THEN 'Неизвестно'
                ELSE SUBSTR(ip_address, 1, 12) || '...'
            END as ip_masked,
            COUNT(*) as views
        FROM ad_views 
        GROUP BY ip_address 
        ORDER BY views DESC 
        LIMIT 10
    ");
    
    foreach ($ipStats as $ip) {
        echo "   🌐 {$ip['ip_masked']}: {$ip['views']} просмотров\n";
    }
    
    echo "\n5. 🔧 ТЕСТ API ENDPOINTS:\n";
    
    // Проверяем доступность API
    $endpoints = [
        'recordAdView.php' => 'Запрос токена для просмотра',
        'secure_reward.php' => 'Подтверждение награды',
        'logAdClick.php' => 'Логирование кликов'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        $url = "http://argun-clear.loc/api/{$endpoint}";
        $headers = @get_headers($url);
        
        if ($headers && (strpos($headers[0], '200') !== false || strpos($headers[0], '400') !== false)) {
            echo "   ✅ {$endpoint}: Доступен ({$description})\n";
        } else {
            echo "   ❌ {$endpoint}: Недоступен\n";
        }
    }
    
    echo "\n6. 📊 СРАВНЕНИЕ С ГЛАВНОЙ СТРАНИЦЕЙ АДМИНКИ:\n";
    
    // Данные как на главной странице
    echo "   Данные для карточек админки:\n";
    echo "   - Просмотров рекламы: " . number_format($totalViews) . "\n";
    echo "   - Кликов по рекламе: " . number_format($totalClicks) . "\n";
    echo "   - CTR: {$ctr}%\n";
    echo "   - Награды за рекламу: " . number_format($totalRewards) . "\n";
    
    echo "\n✅ РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:\n";
    
    if ($totalViews > 0) {
        echo "   ✅ Система записи просмотров работает\n";
    } else {
        echo "   ⚠️ Нет данных о просмотрах\n";
    }
    
    if ($totalClicks > 0) {
        echo "   ✅ Система записи кликов работает\n";
    } else {
        echo "   ⚠️ Нет данных о кликах\n";
    }
    
    echo "   ✅ SQLite интеграция функционирует\n";
    echo "   ✅ API endpoints доступны\n";
    echo "   ✅ Статистика для админки готова\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ТЕСТ ЗАВЕРШЕН!\n";
?>
