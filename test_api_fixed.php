<?php
/**
 * test_api_fixed.php
 * Тест исправленного API лимитов
 */

declare(strict_types=1);

echo "🔧 ТЕСТ ИСПРАВЛЕННОГО API ЛИМИТОВ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $testUserId = 7176766994;
    
    echo "1. 📡 ТЕСТ API В РЕЖИМЕ ТЕСТИРОВАНИЯ:\n";
    
    // Подготавливаем данные для API
    $apiData = json_encode([
        'test_mode' => true,
        'user_id' => $testUserId
    ]);
    
    // Симулируем POST запрос
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $apiData
        ]
    ]);
    
    // Вызываем API
    $response = file_get_contents('http://argun-clear.loc/api/get_user_limits.php', false, $context);
    
    if ($response === false) {
        echo "   ❌ Ошибка вызова API\n";
    } else {
        echo "   ✅ API ответил\n";
        
        $data = json_decode($response, true);
        
        if ($data && $data['success']) {
            echo "   📊 Лимиты из API:\n";
            foreach ($data['limits'] as $adType => $limit) {
                echo "     - {$adType}: {$limit['current']}/{$limit['limit']} (осталось: {$limit['remaining']})\n";
                echo "       Кнопка: '{$limit['button_text']}'\n";
                echo "       Процент: {$limit['percentage']}%\n";
            }
            
            echo "   📊 Общая статистика:\n";
            echo "     - Всего использовано: {$data['summary']['total_current']}/{$data['summary']['total_limit']}\n";
            echo "     - Общий процент: {$data['summary']['total_percentage']}%\n";
        } else {
            echo "   ❌ API вернул ошибку: " . ($data['error'] ?? 'неизвестная ошибка') . "\n";
        }
    }
    
    echo "\n2. 🎯 ТЕСТ УВЕЛИЧЕНИЯ ЛИМИТА:\n";
    
    require_once 'api/ad_limits_sqlite.php';
    $limitsManager = new AdLimitsSQLite();
    
    $testAdType = 'native_banner';
    $beforeCount = $limitsManager->getCurrentCount($testUserId, $testAdType);
    
    echo "   📊 До увеличения: {$beforeCount}/20\n";
    
    $incrementResult = $limitsManager->incrementCount($testUserId, $testAdType);
    
    if ($incrementResult) {
        $afterCount = $limitsManager->getCurrentCount($testUserId, $testAdType);
        echo "   ✅ После увеличения: {$afterCount}/20\n";
        
        // Проверяем API снова
        $response2 = file_get_contents('http://argun-clear.loc/api/get_user_limits.php', false, $context);
        $data2 = json_decode($response2, true);
        
        if ($data2 && $data2['success']) {
            $newButtonText = $data2['limits'][$testAdType]['button_text'];
            echo "   🔘 Новый текст кнопки: '{$newButtonText}'\n";
        }
    } else {
        echo "   ❌ Ошибка увеличения счетчика\n";
    }
    
    echo "\n✅ API ИСПРАВЛЕН И РАБОТАЕТ!\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ГОТОВО!\n";
?>
