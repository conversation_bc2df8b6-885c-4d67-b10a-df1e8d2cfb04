/**
 * js/ad-reward-integration.js
 * Интеграция начисления монет за рекламу с SQLite системой
 */

class AdRewardIntegration {
  constructor() {
    this.apiClient = null;
    this.balanceManager = null;
    this.isInitialized = false;
    this.adSettings = null;
    this.adRewards = {};
  }

  /**
   * Инициализация интеграции
   */
  init() {
    console.log('[AdRewardIntegration] 🚀 Инициализация интеграции начислений за рекламу...');
    
    // Ждем загрузки зависимостей
    this.waitForDependencies().then(async () => {
      this.apiClient = window.apiClient;
      this.balanceManager = window.balanceManager;

      // Загружаем настройки рекламы
      await this.loadAdSettings();

      this.isInitialized = true;

      console.log('[AdRewardIntegration] ✅ Интеграция инициализирована');

      // Подключаемся к существующим менеджерам рекламы
      this.integrateWithAdManagers();

    }).catch(error => {
      console.error('[AdRewardIntegration] ❌ Ошибка инициализации:', error);
    });
  }

  /**
   * Загрузка настроек рекламы из базы данных
   */
  async loadAdSettings() {
    try {
      console.log('[AdRewardIntegration] 📡 Загрузка настроек рекламы...');

      const response = await fetch('/api/getAdSettings.php', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        this.adSettings = data;
        this.adRewards = data.ad_rewards_extended || data.ad_rewards || {};

        console.log('[AdRewardIntegration] ✅ Настройки рекламы загружены:', this.adRewards);

        // Обновляем отображение наград в интерфейсе
        this.updateAdRewardsDisplay();

      } else {
        // Используем fallback значения
        this.adRewards = data.fallback_rewards || {
          'native_banner': 10,
          'rewarded_video': 1,
          'interstitial': 10,
          'banner': 5,
          'video': 10,
          'rewarded': 15
        };

        console.warn('[AdRewardIntegration] ⚠️ Используем fallback настройки рекламы');
      }

    } catch (error) {
      console.error('[AdRewardIntegration] ❌ Ошибка загрузки настроек рекламы:', error);

      // Fallback значения при ошибке
      this.adRewards = {
        'native_banner': 10,
        'rewarded_video': 1,
        'interstitial': 10,
        'banner': 5,
        'video': 10,
        'rewarded': 15
      };
    }
  }

  /**
   * Обновление отображения наград в интерфейсе
   */
  updateAdRewardsDisplay() {
    try {
      // Обновляем кружочки с наградами в кнопках рекламы
      const adButtons = document.querySelectorAll('[data-ad-type]');

      adButtons.forEach(button => {
        const adType = button.getAttribute('data-ad-type');
        const reward = this.getRewardForAdType(adType);

        if (reward > 0) {
          // Ищем элемент с наградой в кнопке
          const rewardElement = button.querySelector('.ad-reward, .reward-amount, .coins-amount');
          if (rewardElement) {
            rewardElement.textContent = reward;
          }

          // Обновляем data-атрибут для других скриптов
          button.setAttribute('data-reward', reward.toString());
        }
      });

      // Обновляем глобальные настройки для совместимости
      if (window.appSettings) {
        window.appSettings.ad_rewards = this.adRewards;
      }

      console.log('[AdRewardIntegration] ✅ Отображение наград обновлено');

    } catch (error) {
      console.warn('[AdRewardIntegration] ⚠️ Ошибка обновления отображения наград:', error);
    }
  }

  /**
   * Получить награду для типа рекламы
   */
  getRewardForAdType(adType) {
    // Проверяем прямое соответствие
    if (this.adRewards[adType]) {
      return this.adRewards[adType];
    }

    // Проверяем альтернативные названия
    const typeMapping = {
      'openLinkButton': 'native_banner',
      'watchVideoButton': 'rewarded_video',
      'openAdButton': 'interstitial',
      'banner': 'native_banner',
      'video': 'rewarded_video',
      'link': 'native_banner',
      'watch': 'rewarded_video'
    };

    const mappedType = typeMapping[adType];
    if (mappedType && this.adRewards[mappedType]) {
      return this.adRewards[mappedType];
    }

    // Fallback значение
    return 10;
  }

  /**
   * Ожидание загрузки зависимостей
   */
  async waitForDependencies() {
    const maxWait = 10000; // 10 секунд
    const checkInterval = 100; // 100ms
    let waited = 0;

    return new Promise((resolve, reject) => {
      const check = () => {
        if (window.apiClient && window.balanceManager) {
          resolve();
        } else if (waited >= maxWait) {
          reject(new Error('Timeout waiting for dependencies'));
        } else {
          waited += checkInterval;
          setTimeout(check, checkInterval);
        }
      };
      check();
    });
  }

  /**
   * Интеграция с существующими менеджерами рекламы
   */
  integrateWithAdManagers() {
    console.log('[AdRewardIntegration] 🔗 Интеграция с менеджерами рекламы...');

    // Создаем глобальную функцию для начисления награды
    window.recordAdViewSQLite = this.recordAdView.bind(this);
    
    // Создаем обертку для старой функции
    if (window.recordAdView && typeof window.recordAdView === 'function') {
      const originalRecordAdView = window.recordAdView;
      window.recordAdView = async (adType) => {
        try {
          return await this.recordAdView(adType);
        } catch (error) {
          console.warn('[AdRewardIntegration] Fallback to original recordAdView:', error);
          return await originalRecordAdView(adType);
        }
      };
    } else {
      window.recordAdView = this.recordAdView.bind(this);
    }

    console.log('[AdRewardIntegration] ✅ Интеграция с менеджерами завершена');
  }

  /**
   * Основная функция начисления награды за рекламу
   */
  async recordAdView(adType = 'default') {
    if (!this.isInitialized) {
      throw new Error('AdRewardIntegration not initialized');
    }

    console.log(`[AdRewardIntegration] 📺 Начисление награды за рекламу: ${adType}`);

    try {
      // Показываем статус загрузки
      this.showStatus('Начисление награды...', 'info');

      // Вызываем API через ApiClient
      const response = await this.apiClient.recordAdView(adType);

      if (response.success) {
        // Награда уже начислена в ApiClient, просто показываем уведомления
        this.handleSuccessfulReward(response, adType);
        return response;
      } else {
        throw new Error(response.error || 'Ошибка начисления награды');
      }

    } catch (error) {
      console.error('[AdRewardIntegration] ❌ Ошибка начисления награды:', error);
      this.handleRewardError(error);
      throw error;
    }
  }

  /**
   * Обработка успешного начисления награды
   */
  handleSuccessfulReward(response, adType) {
    const reward = response.reward || 0;
    const newBalance = response.newBalance || 0;
    const expectedReward = this.getRewardForAdType(adType);

    console.log(`[AdRewardIntegration] ✅ Награда начислена: +${reward} монет (ожидалось: ${expectedReward}), новый баланс: ${newBalance}`);

    // Проверяем соответствие награды настройкам
    if (reward !== expectedReward && expectedReward > 0) {
      console.warn(`[AdRewardIntegration] ⚠️ Награда не соответствует настройкам: получено ${reward}, ожидалось ${expectedReward}`);
    }

    // Показываем уведомление о награде
    const coinsText = this.getCoinsText();
    this.showStatus(`🎉 Награда зачислена: +${reward} ${coinsText}!`, 'success');

    // Воспроизводим звук монет
    this.playCoinsSound(reward);

    // Вибрация (если доступна)
    this.vibrate('success');

    // Обновляем дневную статистику в интерфейсе
    if (response.daily_stats) {
      this.updateDailyStatsDisplay(response.daily_stats);
    }

    // Запускаем cooldown для кнопки (если есть активная кнопка)
    this.startCooldownIfNeeded(adType);
  }

  /**
   * Обработка ошибки начисления
   */
  handleRewardError(error) {
    let errorMessage = 'Ошибка начисления награды';
    
    if (error.message.includes('limit')) {
      errorMessage = 'Дневной лимит исчерпан';
    } else if (error.message.includes('wait')) {
      errorMessage = 'Подождите перед следующим просмотром';
    }

    this.showStatus(errorMessage, 'error');
    this.vibrate('error');
  }

  /**
   * Показать статус пользователю
   */
  showStatus(message, type = 'info') {
    if (window.appUtils && window.appUtils.showStatus) {
      window.appUtils.showStatus(message, type);
    } else if (window.showStatus) {
      window.showStatus(message, type);
    } else {
      console.log(`[AdRewardIntegration] Status: ${message}`);
    }
  }

  /**
   * Воспроизвести звук монет
   */
  playCoinsSound(amount) {
    try {
      if (window.audioManager && window.audioManager.playCoinsSound) {
        window.audioManager.playCoinsSound(amount);
      } else if (window.playCoinsSound) {
        window.playCoinsSound(amount);
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка воспроизведения звука:', error);
    }
  }

  /**
   * Вибрация
   */
  vibrate(type = 'light') {
    try {
      if (window.Telegram?.WebApp?.HapticFeedback) {
        if (type === 'success') {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('success');
        } else if (type === 'error') {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('error');
        } else {
          window.Telegram.WebApp.HapticFeedback.impactOccurred('light');
        }
      } else if (window.appUtils && window.appUtils.vibrate) {
        window.appUtils.vibrate(type);
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка вибрации:', error);
    }
  }

  /**
   * Получить текст для монет с учетом локализации
   */
  getCoinsText() {
    if (window.appLocalization && window.appLocalization.get) {
      return window.appLocalization.get('currency.coins');
    }
    return 'монет';
  }

  /**
   * Обновить отображение дневной статистики
   */
  updateDailyStatsDisplay(dailyStats) {
    try {
      const progressEl = document.getElementById('daily-progress');
      if (progressEl) {
        progressEl.textContent = `${dailyStats.earned_today}/${dailyStats.limit}`;
      }

      // Обновляем прогресс-бар если есть
      const progressBarEl = document.querySelector('.daily-progress-bar');
      if (progressBarEl) {
        const percentage = (dailyStats.earned_today / dailyStats.limit) * 100;
        progressBarEl.style.width = `${Math.min(percentage, 100)}%`;
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка обновления дневной статистики:', error);
    }
  }

  /**
   * Запустить cooldown для кнопки рекламы
   */
  startCooldownIfNeeded(adType) {
    try {
      // Ищем активную кнопку рекламы
      const adButtons = document.querySelectorAll(`[data-ad-type="${adType}"], .ad-button, .watch-ad-btn`);
      
      adButtons.forEach(button => {
        if (button && !button.disabled) {
          // Запускаем cooldown через существующие менеджеры
          if (window.adsManager && window.adsManager.startCountdown) {
            window.adsManager.startCountdown(button);
          } else if (window.startCountdown) {
            window.startCountdown(button);
          }
        }
      });
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка запуска cooldown:', error);
    }
  }

  /**
   * Получить статистику начислений
   */
  async getRewardStats() {
    if (!this.isInitialized) {
      return null;
    }

    try {
      // Можно добавить API для получения статистики
      return {
        total_earned: 0,
        today_earned: 0,
        ads_watched: 0
      };
    } catch (error) {
      console.error('[AdRewardIntegration] Ошибка получения статистики:', error);
      return null;
    }
  }

  /**
   * Получить текущие настройки рекламы
   */
  getAdSettings() {
    return {
      rewards: this.adRewards,
      settings: this.adSettings,
      isLoaded: this.adSettings !== null
    };
  }

  /**
   * Обновить настройки рекламы (перезагрузить из базы)
   */
  async refreshAdSettings() {
    console.log('[AdRewardIntegration] 🔄 Обновление настроек рекламы...');
    await this.loadAdSettings();
    return this.getAdSettings();
  }

  /**
   * Получить все доступные типы рекламы с наградами
   */
  getAvailableAdTypes() {
    const types = [];

    for (const [adType, reward] of Object.entries(this.adRewards)) {
      types.push({
        type: adType,
        reward: reward,
        description: this.getAdTypeDescription(adType)
      });
    }

    return types;
  }

  /**
   * Получить описание типа рекламы
   */
  getAdTypeDescription(adType) {
    const descriptions = {
      'native_banner': 'Нативный баннер',
      'rewarded_video': 'Видео с наградой',
      'interstitial': 'Полноэкранная реклама',
      'banner': 'Баннер',
      'video': 'Видео',
      'rewarded': 'Награда',
      'link': 'Ссылка'
    };

    return descriptions[adType] || adType;
  }
}

// Создаем глобальный экземпляр
window.adRewardIntegration = new AdRewardIntegration();

// Автоматическая инициализация при загрузке
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.adRewardIntegration.init();
  });
} else {
  window.adRewardIntegration.init();
}

// Экспорт для модульных систем
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdRewardIntegration;
}

console.log('📺 [AdRewardIntegration] Модуль интеграции начислений за рекламу загружен');
