/**
 * js/ad-reward-integration.js
 * Интеграция начисления монет за рекламу с SQLite системой
 */

class AdRewardIntegration {
  constructor() {
    this.apiClient = null;
    this.balanceManager = null;
    this.isInitialized = false;
  }

  /**
   * Инициализация интеграции
   */
  init() {
    console.log('[AdRewardIntegration] 🚀 Инициализация интеграции начислений за рекламу...');
    
    // Ждем загрузки зависимостей
    this.waitForDependencies().then(() => {
      this.apiClient = window.apiClient;
      this.balanceManager = window.balanceManager;
      this.isInitialized = true;
      
      console.log('[AdRewardIntegration] ✅ Интеграция инициализирована');
      
      // Подключаемся к существующим менеджерам рекламы
      this.integrateWithAdManagers();
      
    }).catch(error => {
      console.error('[AdRewardIntegration] ❌ Ошибка инициализации:', error);
    });
  }

  /**
   * Ожидание загрузки зависимостей
   */
  async waitForDependencies() {
    const maxWait = 10000; // 10 секунд
    const checkInterval = 100; // 100ms
    let waited = 0;

    return new Promise((resolve, reject) => {
      const check = () => {
        if (window.apiClient && window.balanceManager) {
          resolve();
        } else if (waited >= maxWait) {
          reject(new Error('Timeout waiting for dependencies'));
        } else {
          waited += checkInterval;
          setTimeout(check, checkInterval);
        }
      };
      check();
    });
  }

  /**
   * Интеграция с существующими менеджерами рекламы
   */
  integrateWithAdManagers() {
    console.log('[AdRewardIntegration] 🔗 Интеграция с менеджерами рекламы...');

    // Создаем глобальную функцию для начисления награды
    window.recordAdViewSQLite = this.recordAdView.bind(this);
    
    // Создаем обертку для старой функции
    if (window.recordAdView && typeof window.recordAdView === 'function') {
      const originalRecordAdView = window.recordAdView;
      window.recordAdView = async (adType) => {
        try {
          return await this.recordAdView(adType);
        } catch (error) {
          console.warn('[AdRewardIntegration] Fallback to original recordAdView:', error);
          return await originalRecordAdView(adType);
        }
      };
    } else {
      window.recordAdView = this.recordAdView.bind(this);
    }

    console.log('[AdRewardIntegration] ✅ Интеграция с менеджерами завершена');
  }

  /**
   * Основная функция начисления награды за рекламу
   */
  async recordAdView(adType = 'default') {
    if (!this.isInitialized) {
      throw new Error('AdRewardIntegration not initialized');
    }

    console.log(`[AdRewardIntegration] 📺 Начисление награды за рекламу: ${adType}`);

    try {
      // Показываем статус загрузки
      this.showStatus('Начисление награды...', 'info');

      // Вызываем API через ApiClient
      const response = await this.apiClient.recordAdView(adType);

      if (response.success) {
        // Награда уже начислена в ApiClient, просто показываем уведомления
        this.handleSuccessfulReward(response, adType);
        return response;
      } else {
        throw new Error(response.error || 'Ошибка начисления награды');
      }

    } catch (error) {
      console.error('[AdRewardIntegration] ❌ Ошибка начисления награды:', error);
      this.handleRewardError(error);
      throw error;
    }
  }

  /**
   * Обработка успешного начисления награды
   */
  handleSuccessfulReward(response, adType) {
    const reward = response.reward || 0;
    const newBalance = response.newBalance || 0;

    console.log(`[AdRewardIntegration] ✅ Награда начислена: +${reward} монет, новый баланс: ${newBalance}`);

    // Показываем уведомление о награде
    const coinsText = this.getCoinsText();
    this.showStatus(`🎉 Награда зачислена: +${reward} ${coinsText}!`, 'success');

    // Воспроизводим звук монет
    this.playCoinsSound(reward);

    // Вибрация (если доступна)
    this.vibrate('success');

    // Обновляем дневную статистику в интерфейсе
    if (response.daily_stats) {
      this.updateDailyStatsDisplay(response.daily_stats);
    }

    // Запускаем cooldown для кнопки (если есть активная кнопка)
    this.startCooldownIfNeeded(adType);
  }

  /**
   * Обработка ошибки начисления
   */
  handleRewardError(error) {
    let errorMessage = 'Ошибка начисления награды';
    
    if (error.message.includes('limit')) {
      errorMessage = 'Дневной лимит исчерпан';
    } else if (error.message.includes('wait')) {
      errorMessage = 'Подождите перед следующим просмотром';
    }

    this.showStatus(errorMessage, 'error');
    this.vibrate('error');
  }

  /**
   * Показать статус пользователю
   */
  showStatus(message, type = 'info') {
    if (window.appUtils && window.appUtils.showStatus) {
      window.appUtils.showStatus(message, type);
    } else if (window.showStatus) {
      window.showStatus(message, type);
    } else {
      console.log(`[AdRewardIntegration] Status: ${message}`);
    }
  }

  /**
   * Воспроизвести звук монет
   */
  playCoinsSound(amount) {
    try {
      if (window.audioManager && window.audioManager.playCoinsSound) {
        window.audioManager.playCoinsSound(amount);
      } else if (window.playCoinsSound) {
        window.playCoinsSound(amount);
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка воспроизведения звука:', error);
    }
  }

  /**
   * Вибрация
   */
  vibrate(type = 'light') {
    try {
      if (window.Telegram?.WebApp?.HapticFeedback) {
        if (type === 'success') {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('success');
        } else if (type === 'error') {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('error');
        } else {
          window.Telegram.WebApp.HapticFeedback.impactOccurred('light');
        }
      } else if (window.appUtils && window.appUtils.vibrate) {
        window.appUtils.vibrate(type);
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка вибрации:', error);
    }
  }

  /**
   * Получить текст для монет с учетом локализации
   */
  getCoinsText() {
    if (window.appLocalization && window.appLocalization.get) {
      return window.appLocalization.get('currency.coins');
    }
    return 'монет';
  }

  /**
   * Обновить отображение дневной статистики
   */
  updateDailyStatsDisplay(dailyStats) {
    try {
      const progressEl = document.getElementById('daily-progress');
      if (progressEl) {
        progressEl.textContent = `${dailyStats.earned_today}/${dailyStats.limit}`;
      }

      // Обновляем прогресс-бар если есть
      const progressBarEl = document.querySelector('.daily-progress-bar');
      if (progressBarEl) {
        const percentage = (dailyStats.earned_today / dailyStats.limit) * 100;
        progressBarEl.style.width = `${Math.min(percentage, 100)}%`;
      }
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка обновления дневной статистики:', error);
    }
  }

  /**
   * Запустить cooldown для кнопки рекламы
   */
  startCooldownIfNeeded(adType) {
    try {
      // Ищем активную кнопку рекламы
      const adButtons = document.querySelectorAll(`[data-ad-type="${adType}"], .ad-button, .watch-ad-btn`);
      
      adButtons.forEach(button => {
        if (button && !button.disabled) {
          // Запускаем cooldown через существующие менеджеры
          if (window.adsManager && window.adsManager.startCountdown) {
            window.adsManager.startCountdown(button);
          } else if (window.startCountdown) {
            window.startCountdown(button);
          }
        }
      });
    } catch (error) {
      console.warn('[AdRewardIntegration] Ошибка запуска cooldown:', error);
    }
  }

  /**
   * Получить статистику начислений
   */
  async getRewardStats() {
    if (!this.isInitialized) {
      return null;
    }

    try {
      // Можно добавить API для получения статистики
      return {
        total_earned: 0,
        today_earned: 0,
        ads_watched: 0
      };
    } catch (error) {
      console.error('[AdRewardIntegration] Ошибка получения статистики:', error);
      return null;
    }
  }
}

// Создаем глобальный экземпляр
window.adRewardIntegration = new AdRewardIntegration();

// Автоматическая инициализация при загрузке
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.adRewardIntegration.init();
  });
} else {
  window.adRewardIntegration.init();
}

// Экспорт для модульных систем
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdRewardIntegration;
}

console.log('📺 [AdRewardIntegration] Модуль интеграции начислений за рекламу загружен');
