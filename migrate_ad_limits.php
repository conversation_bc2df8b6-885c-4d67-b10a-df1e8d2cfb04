<?php
/**
 * migrate_ad_limits.php
 * Миграция лимитов рекламы из JSON в SQLite
 */

declare(strict_types=1);

echo "🎯 МИГРАЦИЯ ЛИМИТОВ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ПРОВЕРКА ТАБЛИЦЫ ad_limits:\n";
    
    // Проверяем, существует ли таблица
    $tableExists = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table' AND name='ad_limits'");
    
    if (empty($tableExists)) {
        echo "   ❌ Таблица ad_limits НЕ СУЩЕСТВУЕТ!\n";
        echo "   🔧 Создаем таблицу...\n";
        
        $createTable = "
            CREATE TABLE IF NOT EXISTS ad_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id BIGINT NOT NULL,
                ad_type TEXT NOT NULL,
                daily_count INTEGER DEFAULT 0,
                last_reset_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, ad_type, last_reset_date)
            )
        ";
        
        $sqlite->query($createTable);
        echo "   ✅ Таблица ad_limits создана\n";
    } else {
        echo "   ✅ Таблица ad_limits существует\n";
    }
    
    echo "\n2. 📄 ЗАГРУЗКА ДАННЫХ ИЗ JSON:\n";
    
    $adLimitsFile = 'database/ad_limits.json';
    if (!file_exists($adLimitsFile)) {
        echo "   ❌ Файл {$adLimitsFile} не найден!\n";
        exit;
    }
    
    $adLimitsData = json_decode(file_get_contents($adLimitsFile), true);
    if (!$adLimitsData) {
        echo "   ❌ Ошибка декодирования {$adLimitsFile}!\n";
        exit;
    }
    
    echo "   ✅ JSON файл загружен\n";
    echo "   📊 Структура данных:\n";
    
    if (isset($adLimitsData['daily_limits'])) {
        echo "     - Дневные лимиты:\n";
        foreach ($adLimitsData['daily_limits'] as $type => $limit) {
            echo "       * {$type}: {$limit}\n";
        }
    }
    
    if (isset($adLimitsData['user_counts'])) {
        echo "     - Счетчики пользователей: " . count($adLimitsData['user_counts']) . " пользователей\n";
    }
    
    echo "     - Последний сброс: " . ($adLimitsData['last_reset_date'] ?? 'не указан') . "\n";
    
    echo "\n3. 🔄 МИГРАЦИЯ ЛИМИТОВ:\n";
    
    $limitsCount = 0;
    $updatedCount = 0;
    $today = date('Y-m-d');
    
    // Мигрируем счетчики пользователей
    if (isset($adLimitsData['user_counts'])) {
        foreach ($adLimitsData['user_counts'] as $userIdStr => $counts) {
            // Извлекаем ID пользователя из строки "user_7176766994"
            $userId = (int)str_replace('user_', '', $userIdStr);
            
            echo "   👤 Обрабатываем пользователя {$userId}:\n";
            
            foreach ($counts as $adType => $count) {
                // Пропускаем test_banner
                if ($adType === 'test_banner') {
                    echo "     ⚠️ Пропускаем {$adType}\n";
                    continue;
                }
                
                // Проверяем, есть ли уже запись
                $existing = $sqlite->query("
                    SELECT id, daily_count FROM ad_limits 
                    WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                ", [$userId, $adType, $today]);
                
                if (empty($existing)) {
                    // Добавляем новую запись
                    $sqlite->query("
                        INSERT INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                        VALUES (?, ?, ?, ?)
                    ", [$userId, $adType, $count, $today]);
                    
                    $limitsCount++;
                    echo "     ✅ Добавлено: {$adType} = {$count}\n";
                } else {
                    // Обновляем существующую запись
                    $oldCount = $existing[0]['daily_count'];
                    $sqlite->query("
                        UPDATE ad_limits 
                        SET daily_count = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                    ", [$count, $userId, $adType, $today]);
                    
                    $updatedCount++;
                    echo "     🔄 Обновлено: {$adType} = {$oldCount} → {$count}\n";
                }
            }
        }
    }
    
    echo "\n4. 📊 РЕЗУЛЬТАТЫ МИГРАЦИИ:\n";
    
    $totalLimits = $sqlite->query("SELECT COUNT(*) as count FROM ad_limits")[0]['count'];
    echo "   📊 Всего записей в ad_limits: {$totalLimits}\n";
    echo "   ✅ Добавлено новых: {$limitsCount}\n";
    echo "   🔄 Обновлено существующих: {$updatedCount}\n";
    
    echo "\n5. 🎯 ПРОВЕРКА ЛИМИТОВ:\n";
    
    // Показываем лимиты для всех пользователей
    $allLimits = $sqlite->query("
        SELECT user_id, ad_type, daily_count, last_reset_date 
        FROM ad_limits 
        WHERE last_reset_date = ?
        ORDER BY user_id, ad_type
    ", [$today]);
    
    $userGroups = [];
    foreach ($allLimits as $limit) {
        $userGroups[$limit['user_id']][] = $limit;
    }
    
    foreach ($userGroups as $userId => $limits) {
        echo "   👤 Пользователь {$userId}:\n";
        foreach ($limits as $limit) {
            $remaining = 20 - $limit['daily_count'];
            $status = $remaining > 0 ? "✅ Доступно ({$remaining} осталось)" : "❌ Лимит исчерпан";
            echo "     - {$limit['ad_type']}: {$limit['daily_count']}/20 {$status}\n";
        }
    }
    
    echo "\n6. 🔧 ТЕСТ ФУНКЦИЙ:\n";
    
    // Тестируем функцию получения лимитов
    echo "   📊 Тест получения лимитов для пользователя:\n";
    
    $testUserId = 7176766994;
    $userLimits = $sqlite->query("
        SELECT ad_type, daily_count 
        FROM ad_limits 
        WHERE user_id = ? AND last_reset_date = ?
        ORDER BY ad_type
    ", [$testUserId, $today]);
    
    if (!empty($userLimits)) {
        echo "   👤 Лимиты для пользователя {$testUserId}:\n";
        foreach ($userLimits as $limit) {
            $remaining = 20 - $limit['daily_count'];
            echo "     - {$limit['ad_type']}: {$limit['daily_count']}/20 (осталось: {$remaining})\n";
        }
    } else {
        echo "   ℹ️ Нет лимитов для пользователя {$testUserId}\n";
    }
    
    echo "\n✅ МИГРАЦИЯ ЛИМИТОВ ЗАВЕРШЕНА!\n";
    echo "🎯 Теперь можно:\n";
    echo "   - Отслеживать лимиты показов для каждого пользователя\n";
    echo "   - Отображать счетчики в кнопках (например: 'Открыть ссылку (3/20)')\n";
    echo "   - Блокировать показ рекламы при достижении лимита\n";
    echo "   - Сбрасывать счетчики каждый день\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ГОТОВО!\n";
?>
