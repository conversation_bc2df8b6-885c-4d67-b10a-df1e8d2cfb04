<?php
/**
 * test_ad_stats_api.php
 * Тест API статистики рекламы
 */

declare(strict_types=1);

echo "🔧 ТЕСТ API СТАТИСТИКИ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Симулируем аутентификацию
session_start();
$_SESSION['authenticated'] = true;

try {
    echo "1. 📊 ТЕСТ ПРЯМОГО ВЫЗОВА API:\n";
    
    // Подключаем API напрямую
    ob_start();
    $_GET = []; // Без фильтров
    require 'api/admin/ad_stats_api.php';
    $apiResponse = ob_get_clean();
    
    $data = json_decode($apiResponse, true);
    
    if ($data && $data['success']) {
        echo "   ✅ API работает успешно\n";
        echo "   📊 Типы рекламы: " . count($data['stats_by_type']) . "\n";
        echo "   🌍 Стран: " . count($data['stats_by_country']) . "\n";
        
        // Детали по типам рекламы
        echo "\n2. 📈 СТАТИСТИКА ПО ТИПАМ:\n";
        foreach ($data['stats_by_type'] as $type => $stats) {
            echo "   - {$type}:\n";
            echo "     👁️ Просмотры: {$stats['views']}\n";
            echo "     🖱️ Клики: {$stats['clicks']}\n";
            echo "     📈 CTR: {$stats['ctr']}%\n";
            echo "     💰 Награды: {$stats['rewards']}\n";
        }
        
        // Статистика по странам
        echo "\n3. 🌍 ТОП-10 СТРАН:\n";
        $topCountries = array_slice($data['stats_by_country'], 0, 10, true);
        foreach ($topCountries as $country => $clicks) {
            echo "   - {$country}: {$clicks} кликов\n";
        }
        
        // Почасовая статистика
        echo "\n4. 🕒 АКТИВНОСТЬ ПО ЧАСАМ (ТОП-5):\n";
        $hourlyActivity = [];
        foreach ($data['hourly_stats'] as $hour => $stats) {
            if ($stats['views'] > 0 || $stats['clicks'] > 0) {
                $hourlyActivity[$hour] = $stats['views'] + $stats['clicks'];
            }
        }
        arsort($hourlyActivity);
        $topHours = array_slice($hourlyActivity, 0, 5, true);
        
        foreach ($topHours as $hour => $activity) {
            $views = $data['hourly_stats'][$hour]['views'];
            $clicks = $data['hourly_stats'][$hour]['clicks'];
            echo "   - {$hour}:00 - Просмотры: {$views}, Клики: {$clicks}\n";
        }
        
        // Общая информация
        echo "\n5. 📊 ОБЩАЯ ИНФОРМАЦИЯ:\n";
        if (isset($data['data_info'])) {
            $info = $data['data_info'];
            echo "   - Всего просмотров: " . ($info['total_views'] ?? 'N/A') . "\n";
            echo "   - Всего кликов: " . ($info['total_clicks'] ?? 'N/A') . "\n";
            echo "   - Всего наград: " . ($info['total_rewards'] ?? 'N/A') . "\n";
            echo "   - Стран: " . ($info['total_countries'] ?? 'N/A') . "\n";
        }
        
    } else {
        echo "   ❌ API вернул ошибку\n";
        if ($data && isset($data['error'])) {
            echo "   Ошибка: " . $data['error'] . "\n";
        }
        echo "   Ответ: " . substr($apiResponse, 0, 500) . "\n";
    }
    
    echo "\n6. 🔧 ТЕСТ С ФИЛЬТРАМИ:\n";
    
    // Тест с фильтром по типу рекламы
    ob_start();
    $_GET = ['ad_type' => 'native_banner'];
    require 'api/admin/ad_stats_api.php';
    $filteredResponse = ob_get_clean();
    
    $filteredData = json_decode($filteredResponse, true);
    
    if ($filteredData && $filteredData['success']) {
        echo "   ✅ Фильтр по типу рекламы работает\n";
        echo "   📊 Типов в результате: " . count($filteredData['stats_by_type']) . "\n";
        
        foreach ($filteredData['stats_by_type'] as $type => $stats) {
            echo "   - {$type}: {$stats['views']} просмотров, {$stats['clicks']} кликов\n";
        }
    } else {
        echo "   ❌ Фильтр не работает\n";
    }
    
    echo "\n7. 🌐 ТЕСТ HTTP ЗАПРОСА:\n";
    
    // Тест через HTTP
    $url = 'http://argun-clear.loc/api/admin/ad_stats_api.php';
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Cookie: " . session_name() . "=" . session_id() . "\r\n"
        ]
    ]);
    
    $httpResponse = @file_get_contents($url, false, $context);
    
    if ($httpResponse) {
        $httpData = json_decode($httpResponse, true);
        if ($httpData && $httpData['success']) {
            echo "   ✅ HTTP запрос работает\n";
            echo "   📊 Данные получены корректно\n";
        } else {
            echo "   ❌ HTTP запрос вернул некорректные данные\n";
        }
    } else {
        echo "   ❌ HTTP запрос не удался\n";
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:\n";
    echo "   ✅ API статистики рекламы работает\n";
    echo "   ✅ Данные загружаются из SQLite\n";
    echo "   ✅ Фильтрация функционирует\n";
    echo "   ✅ Статистика по странам доступна\n";
    echo "   ✅ Почасовая статистика работает\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ТЕСТ ЗАВЕРШЕН!\n";
?>
