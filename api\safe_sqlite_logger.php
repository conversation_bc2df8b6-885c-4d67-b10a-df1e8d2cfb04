<?php
/**
 * api/safe_sqlite_logger.php
 * Безопасный логгер для SQLite базы данных
 * Замена для SafeJsonLogger с использованием SQLite вместо JSON файлов
 */

declare(strict_types=1);

require_once __DIR__ . '/database_manager.php';

class SafeSqliteLogger 
{
    private DatabaseManager $db;
    private int $maxRetries = 3;
    private int $retryDelay = 100000; // 100ms в микросекундах

    public function __construct()
    {
        $this->db = DatabaseManager::getInstance();
    }

    /**
     * Безопасно добавляет запись о просмотре рекламы
     * Замена для appendToJsonFile для ad_views.json
     */
    public function logAdView(array $viewData): bool
    {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                $success = $this->db->logAdView(
                    $viewData['user_id'],
                    $viewData['ad_type'],
                    $viewData['reward'],
                    $viewData['ip'] ?? null,
                    $viewData['user_agent'] ?? null
                );
                
                if ($success) {
                    error_log("SafeSqliteLogger: Просмотр рекламы записан успешно для пользователя {$viewData['user_id']}");
                    return true;
                }
                
                error_log("SafeSqliteLogger: Ошибка записи просмотра рекламы, попытка {$attempts}");
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                }
                
            } catch (Exception $e) {
                error_log("SafeSqliteLogger: Исключение при записи просмотра рекламы: " . $e->getMessage());
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                } else {
                    return false;
                }
            }
        }
        
        error_log("SafeSqliteLogger: Не удалось записать просмотр рекламы после {$this->maxRetries} попыток");
        return false;
    }

    /**
     * Безопасно добавляет запись о клике по рекламе
     * Замена для appendToJsonFile для ad_clicks.json
     */
    public function logAdClick(array $clickData): bool
    {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                $success = $this->db->logAdClick(
                    $clickData['user_id'],
                    $clickData['ad_type'],
                    $clickData['click_type'],
                    $clickData['reason'] ?? '',
                    $clickData['ip'] ?? null,
                    $clickData['user_agent'] ?? null
                );
                
                if ($success) {
                    error_log("SafeSqliteLogger: Клик по рекламе записан успешно для пользователя {$clickData['user_id']}");
                    return true;
                }
                
                error_log("SafeSqliteLogger: Ошибка записи клика по рекламе, попытка {$attempts}");
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                }
                
            } catch (Exception $e) {
                error_log("SafeSqliteLogger: Исключение при записи клика по рекламе: " . $e->getMessage());
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                } else {
                    return false;
                }
            }
        }
        
        error_log("SafeSqliteLogger: Не удалось записать клик по рекламе после {$this->maxRetries} попыток");
        return false;
    }

    /**
     * Логирование анализа мошенничества
     */
    public function logFraudAnalysis(array $fraudData): bool
    {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                $pdo = $this->db->getPdo();
                
                $stmt = $pdo->prepare("
                    INSERT INTO fraud_analysis (timestamp, user_id, ip_address, fingerprint, violations_count, risk_score, should_block, violations)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $violations = isset($fraudData['violations']) ? json_encode($fraudData['violations'], JSON_UNESCAPED_UNICODE) : '[]';
                
                $success = $stmt->execute([
                    $fraudData['timestamp'] ?? date('Y-m-d H:i:s'),
                    $fraudData['user_id'],
                    $fraudData['ip_address'] ?? null,
                    $fraudData['fingerprint'] ?? null,
                    $fraudData['violations_count'] ?? 0,
                    $fraudData['risk_score'] ?? 0,
                    $fraudData['should_block'] ?? false,
                    $violations
                ]);
                
                if ($success) {
                    error_log("SafeSqliteLogger: Анализ мошенничества записан для пользователя {$fraudData['user_id']}");
                    return true;
                }
                
                error_log("SafeSqliteLogger: Ошибка записи анализа мошенничества, попытка {$attempts}");
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                }
                
            } catch (Exception $e) {
                error_log("SafeSqliteLogger: Исключение при записи анализа мошенничества: " . $e->getMessage());
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                } else {
                    return false;
                }
            }
        }
        
        error_log("SafeSqliteLogger: Не удалось записать анализ мошенничества после {$this->maxRetries} попыток");
        return false;
    }

    /**
     * Сохранение сообщения поддержки
     */
    public function logSupportMessage(array $messageData): bool
    {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                $pdo = $this->db->getPdo();
                
                $stmt = $pdo->prepare("
                    INSERT INTO support_messages (chat_id, message_id, from_user, text, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $success = $stmt->execute([
                    $messageData['chat_id'],
                    $messageData['message_id'],
                    $messageData['from_user'],
                    $messageData['text'],
                    $messageData['created_at'] ?? date('Y-m-d H:i:s')
                ]);
                
                if ($success) {
                    error_log("SafeSqliteLogger: Сообщение поддержки записано для чата {$messageData['chat_id']}");
                    return true;
                }
                
                error_log("SafeSqliteLogger: Ошибка записи сообщения поддержки, попытка {$attempts}");
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                }
                
            } catch (Exception $e) {
                error_log("SafeSqliteLogger: Исключение при записи сообщения поддержки: " . $e->getMessage());
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                } else {
                    return false;
                }
            }
        }
        
        error_log("SafeSqliteLogger: Не удалось записать сообщение поддержки после {$this->maxRetries} попыток");
        return false;
    }

    /**
     * Обновление отпечатка устройства
     */
    public function updateDeviceFingerprint(int $userId, string $fingerprintHash, array $deviceInfo): bool
    {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                $pdo = $this->db->getPdo();
                
                // Проверяем существует ли уже такой отпечаток
                $checkStmt = $pdo->prepare("
                    SELECT id, usage_count FROM device_fingerprints 
                    WHERE user_id = ? AND fingerprint_hash = ?
                ");
                $checkStmt->execute([$userId, $fingerprintHash]);
                $existing = $checkStmt->fetch();
                
                if ($existing) {
                    // Обновляем существующий
                    $updateStmt = $pdo->prepare("
                        UPDATE device_fingerprints 
                        SET last_seen = CURRENT_TIMESTAMP, usage_count = usage_count + 1, device_info = ?
                        WHERE id = ?
                    ");
                    $success = $updateStmt->execute([json_encode($deviceInfo, JSON_UNESCAPED_UNICODE), $existing['id']]);
                } else {
                    // Создаем новый
                    $insertStmt = $pdo->prepare("
                        INSERT INTO device_fingerprints (user_id, fingerprint_hash, device_info, first_seen, last_seen, usage_count)
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1)
                    ");
                    $success = $insertStmt->execute([$userId, $fingerprintHash, json_encode($deviceInfo, JSON_UNESCAPED_UNICODE)]);
                }
                
                if ($success) {
                    error_log("SafeSqliteLogger: Отпечаток устройства обновлен для пользователя {$userId}");
                    return true;
                }
                
                error_log("SafeSqliteLogger: Ошибка обновления отпечатка устройства, попытка {$attempts}");
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                }
                
            } catch (Exception $e) {
                error_log("SafeSqliteLogger: Исключение при обновлении отпечатка устройства: " . $e->getMessage());
                
                if ($attempts < $this->maxRetries) {
                    usleep($this->retryDelay);
                } else {
                    return false;
                }
            }
        }
        
        error_log("SafeSqliteLogger: Не удалось обновить отпечаток устройства после {$this->maxRetries} попыток");
        return false;
    }

    /**
     * Получение статистики просмотров рекламы
     */
    public function getAdViewsStats(int $limit = 1000): array
    {
        try {
            $pdo = $this->db->getPdo();
            
            $stmt = $pdo->prepare("
                SELECT user_id, ad_type, reward, timestamp, ip_address
                FROM ad_views 
                ORDER BY timestamp DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("SafeSqliteLogger: Ошибка получения статистики просмотров: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получение статистики кликов по рекламе
     */
    public function getAdClicksStats(int $limit = 1000): array
    {
        try {
            $pdo = $this->db->getPdo();
            
            $stmt = $pdo->prepare("
                SELECT user_id, ad_type, click_type, reason, timestamp, ip_address
                FROM ad_clicks 
                ORDER BY timestamp DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("SafeSqliteLogger: Ошибка получения статистики кликов: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Очистка старых записей (для оптимизации)
     */
    public function cleanupOldRecords(int $daysToKeep = 30): bool
    {
        try {
            $pdo = $this->db->getPdo();
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
            
            $pdo->beginTransaction();
            
            // Очищаем старые просмотры рекламы
            $stmt = $pdo->prepare("DELETE FROM ad_views WHERE timestamp < ?");
            $stmt->execute([$cutoffDate]);
            $deletedViews = $stmt->rowCount();
            
            // Очищаем старые клики по рекламе
            $stmt = $pdo->prepare("DELETE FROM ad_clicks WHERE timestamp < ?");
            $stmt->execute([$cutoffDate]);
            $deletedClicks = $stmt->rowCount();
            
            // Очищаем старый анализ мошенничества
            $stmt = $pdo->prepare("DELETE FROM fraud_analysis WHERE timestamp < ?");
            $stmt->execute([$cutoffDate]);
            $deletedFraud = $stmt->rowCount();
            
            $pdo->commit();
            
            error_log("SafeSqliteLogger: Очистка завершена. Удалено: {$deletedViews} просмотров, {$deletedClicks} кликов, {$deletedFraud} записей анализа");
            
            return true;
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log("SafeSqliteLogger: Ошибка очистки старых записей: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверка целостности базы данных
     */
    public function checkDatabaseIntegrity(): array
    {
        try {
            $pdo = $this->db->getPdo();
            
            $stats = [];
            
            // Проверяем основные таблицы
            $tables = ['users', 'ad_views', 'ad_clicks', 'ad_tokens', 'support_messages', 'fraud_analysis'];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $result = $stmt->fetch();
                $stats[$table] = $result['count'];
            }
            
            // Проверяем целостность внешних ключей
            $stmt = $pdo->query("PRAGMA foreign_key_check");
            $foreignKeyErrors = $stmt->fetchAll();
            
            $stats['foreign_key_errors'] = count($foreignKeyErrors);
            $stats['integrity_check'] = empty($foreignKeyErrors) ? 'OK' : 'ERRORS';
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("SafeSqliteLogger: Ошибка проверки целостности: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
}

// Глобальные функции для совместимости со старым кодом
function logAdViewSafe(array $viewData): bool
{
    static $logger = null;
    if ($logger === null) {
        $logger = new SafeSqliteLogger();
    }
    return $logger->logAdView($viewData);
}

function logAdClickSafe(array $clickData): bool
{
    static $logger = null;
    if ($logger === null) {
        $logger = new SafeSqliteLogger();
    }
    return $logger->logAdClick($clickData);
}

function logFraudAnalysisSafe(array $fraudData): bool
{
    static $logger = null;
    if ($logger === null) {
        $logger = new SafeSqliteLogger();
    }
    return $logger->logFraudAnalysis($fraudData);
}
?>
