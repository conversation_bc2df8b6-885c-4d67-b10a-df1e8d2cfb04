<?php
/**
 * Отладка проблем с webhook
 * Проверяет доступность URL, токены ботов и статус webhook
 */

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/../../includes/bot_config_loader.php';
defineBotConstants();

header('Content-Type: application/json');

/**
 * Проверяет доступность URL
 */
function checkUrlAccessibility($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'TelegramBot/1.0');
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'accessible' => ($httpCode >= 200 && $httpCode < 400),
        'http_code' => $httpCode,
        'error' => $error,
        'response_length' => strlen($result)
    ];
}

/**
 * Проверяет токен бота
 */
function checkBotToken($token) {
    $url = "https://api.telegram.org/bot{$token}/getMe";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($result && $httpCode === 200) {
        $response = json_decode($result, true);
        return $response;
    }
    
    return ['ok' => false, 'description' => "HTTP Error: {$httpCode}"];
}

/**
 * Получает информацию о webhook
 */
function getWebhookInfo($token) {
    $url = "https://api.telegram.org/bot{$token}/getWebhookInfo";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($result && $httpCode === 200) {
        return json_decode($result, true);
    }
    
    return ['ok' => false, 'description' => "HTTP Error: {$httpCode}"];
}

/**
 * Пытается установить webhook
 */
function testSetWebhook($token, $webhookUrl) {
    $url = "https://api.telegram.org/bot{$token}/setWebhook";
    $data = [
        'url' => $webhookUrl,
        'allowed_updates' => ['message', 'callback_query'],
        'drop_pending_updates' => true
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $response = [
        'http_code' => $httpCode,
        'curl_error' => $error,
        'raw_response' => $result
    ];
    
    if ($result && $httpCode === 200) {
        $response['parsed'] = json_decode($result, true);
    }
    
    return $response;
}

try {
    $botType = $_GET['bot'] ?? 'both'; // main, support, both
    
    $results = [];
    
    if ($botType === 'main' || $botType === 'both') {
        $mainToken = TELEGRAM_BOT_TOKEN;
        $mainWebhookUrl = WEBHOOK_URL;
        
        $results['main_bot'] = [
            'token_check' => checkBotToken($mainToken),
            'webhook_url' => $mainWebhookUrl,
            'url_accessibility' => checkUrlAccessibility($mainWebhookUrl),
            'current_webhook' => getWebhookInfo($mainToken),
            'test_set_webhook' => testSetWebhook($mainToken, $mainWebhookUrl)
        ];
    }
    
    if ($botType === 'support' || $botType === 'both') {
        $supportToken = SUPPORT_BOT_TOKEN;
        $supportWebhookUrl = SUPPORT_WEBHOOK_URL;
        
        $results['support_bot'] = [
            'token_check' => checkBotToken($supportToken),
            'webhook_url' => $supportWebhookUrl,
            'url_accessibility' => checkUrlAccessibility($supportWebhookUrl),
            'current_webhook' => getWebhookInfo($supportToken),
            'test_set_webhook' => testSetWebhook($supportToken, $supportWebhookUrl)
        ];
    }
    
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'results' => $results
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
