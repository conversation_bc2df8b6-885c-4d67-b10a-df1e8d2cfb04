// === modules-loader.js ===
// Файл: js/modules-loader.js
// Описание: Главный дирижёр приложения. Загружает все модули и запускает инициализацию.

class ModulesLoader {
  constructor() {
    this.loadedModules = new Set();
    this.failedModules = new Set();
    this.loadStartTime = Date.now();

    this.MODULE_ORDER = [
      'js/ads-config.js', // 🔧 НОВАЯ ЦЕНТРАЛИЗОВАННАЯ КОНФИГУРАЦИЯ РЕКЛАМЫ
      'js/richads-security.js', // 🔒 СИСТЕМА БЕЗОПАСНОСТИ RICHADS
      'js/ads-state-manager.js', // 🏗️ НОВЫЙ МЕНЕДЖЕР СОСТОЯНИЯ
      'js/config.js',
      'js/localization.js', // 🌐 СИСТЕМА ЛОКАЛИЗАЦИИ (загружается рано для переводов!)
      'js/status-localization.js', // 📝 ЛОКАЛИЗАЦИЯ СТАТУСНЫХ СООБЩЕНИЙ
      'js/currency-data-manager.js', // 🔄 ЕДИНЫЙ МЕНЕДЖЕР ДАННЫХ ВАЛЮТ
      'js/api-client.js',
      'js/balance-api-client.js', // 📡 НОВЫЙ КЛИЕНТ ДЛЯ SQLITE API БАЛАНСА
      'js/app-utils.js',
      'js/smooth-scroll.js', // 📜 ПЛАВНАЯ ПРОКРУТКА
      'js/balance-manager.js',
      'js/audio-manager.js',
      'js/page-manager.js',
      'js/reward-badges-manager.js', // 🏆 ДИНАМИЧЕСКИЕ НАГРАДЫ ИЗ АДМИНКИ
      'js/ad-counters.js', // 📊 ЛОКАЛЬНАЯ СИСТЕМА СЧЕТЧИКОВ (fallback)
      'js/server-ad-counters.js', // 📊 СЕРВЕРНАЯ СИСТЕМА СЧЕТЧИКОВ
      'js/ads-manager-full.js', // 🎯 ОРИГИНАЛЬНЫЙ МЕНЕДЖЕР РЕКЛАМЫ
      'js/referral-system.js',
      'js/calculator-original.js',
      'js/withdrawal-tabs-manager.js',
      'js/withdrawal-form-blocker.js', // 🔒 БЛОКИРОВЩИК ФОРМЫ ВЫПЛАТ
      'js/withdrawal-form-manager.js',
      'js/withdrawal-manager.js',
      'js/main.js'
    ];
  }

  loadScript(src) {
    return new Promise((resolve, reject) => {
      console.log(`[Loader] 📥 Загружаем ${src}...`);
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        this.loadedModules.add(src);
        console.log(`[Loader] ✅ ${src} загружен успешно`);
        resolve();
      };
      script.onerror = (error) => {
        this.failedModules.add(src);
        console.error(`[Loader] ❌ Ошибка загрузки ${src}:`, error);
        reject(new Error(`Не удалось загрузить ${src}`));
      };
      document.head.appendChild(script);
    });
  }

  async loadAll() {
    console.log('📦 [Loader] Начинаем загрузку всех модулей...');
    for (const moduleSrc of this.MODULE_ORDER) {
      try {
        await this.loadScript(moduleSrc);
        // Проверяем, что модуль действительно загрузился
        const moduleName = this.getModuleNameFromPath(moduleSrc);
        if (moduleName && !window[moduleName]) {
          console.warn(`[Loader] ⚠️ Модуль ${moduleSrc} загружен, но ${moduleName} не найден в window`);
        }
      } catch (error) {
        console.error(`[Loader] 🛑 Критическая ошибка при загрузке ${moduleSrc}:`, error);
        return;
      }
    }
    const loadTime = Date.now() - this.loadStartTime;
    console.log(`[Loader] ✨ Все модули загружены за ${loadTime}мс. Запускаем приложение...`);

    // Даем время на выполнение кода в модулях
    setTimeout(() => {
      // ИСПРАВЛЕНИЕ: Инициализируем неактивное состояние кнопок и скрытые разделы
      this.initializeInactiveState();

      // ИСПРАВЛЕНИЕ: Инициализируем AudioManager для звука монет
      this.initializeAudioManager();

      // Принудительно обновляем заголовки кнопок рекламы
      setTimeout(() => {
        if (window.adsManagerUnified && window.adsManagerUnified.updateAllButtonTitles) {
          console.log('[Loader] 🔄 Принудительное обновление заголовков кнопок рекламы...');
          window.adsManagerUnified.updateAllButtonTitles();
        }
      }, 2000);

      if (window.UniQPaidApp) {
        console.log('[Loader] ✅ Найден класс UniQPaidApp. main.js сам запустит приложение.');
        // main.js сам запустит приложение через DOMContentLoaded или сразу
      } else {
        console.error("[Loader] 🛑 Не найден главный класс приложения UniQPaidApp.");
        console.log('[Loader] 🔍 Доступные объекты в window:', Object.keys(window).filter(key => key.includes('App') || key.includes('Manager')));
      }
    }, 100);
  }

  getModuleNameFromPath(path) {
    const moduleMap = {
      'js/config.js': null, // Не создает глобальный объект с именем модуля
      'js/localization.js': 'appLocalization', // 🌐 СИСТЕМА ЛОКАЛИЗАЦИИ
      'js/status-localization.js': null, // 📝 ЛОКАЛИЗАЦИЯ СТАТУСНЫХ СООБЩЕНИЙ (только функции)
      'js/currency-data-manager.js': 'currencyDataManager', // 🔄 ЕДИНЫЙ МЕНЕДЖЕР ДАННЫХ ВАЛЮТ
      'js/api-client.js': 'apiClient',
      'js/app-utils.js': 'appUtils',
      'js/balance-manager.js': 'balanceManager',
      'js/audio-manager.js': 'audioManager',
      'js/page-manager.js': 'pageManager',
      'js/reward-badges-manager.js': 'rewardBadgesManager', // 🏆 ДИНАМИЧЕСКИЕ НАГРАДЫ ИЗ АДМИНКИ
      'js/ad-counters.js': 'adCountersManager', // 📊 ЛОКАЛЬНАЯ СИСТЕМА СЧЕТЧИКОВ (fallback)
      'js/server-ad-counters.js': 'serverAdCountersManager', // 📊 СЕРВЕРНАЯ СИСТЕМА СЧЕТЧИКОВ
      'js/ads-manager-full.js': 'adsManagerFull', // 🎯 ЕДИНСТВЕННЫЙ рекламный менеджер!
      'js/referral-system.js': 'referralManager',
      'js/calculator-original.js': 'calculatorManager',
      'js/withdrawal-tabs-manager.js': 'withdrawalTabsManager',
      'js/withdrawal-form-blocker.js': 'withdrawalFormBlocker', // 🔒 БЛОКИРОВЩИК ФОРМЫ ВЫПЛАТ
      'js/withdrawal-form-manager.js': 'withdrawalFormManager',
      'js/withdrawal-manager.js': 'withdrawalManager',
      'js/main.js': 'UniQPaidApp'
    };
    return moduleMap[path];
  }

  /**
   * Инициализирует неактивное состояние кнопок и скрытые разделы (из оригинала)
   */
  initializeInactiveState() {
    console.log('[Loader] 🔧 Инициализация неактивного состояния...');

    // ИСПРАВЛЕНИЕ: Убеждаемся что кнопки неактивны
    const calculatorTab = document.getElementById('calculator-tab');
    const withdrawalTab = document.getElementById('withdrawal-tab');

    if (calculatorTab) {
      calculatorTab.classList.remove('active');
      calculatorTab.classList.add('inactive');
      // ИСПРАВЛЕНИЕ: Убираем disabled чтобы кнопка была кликабельной
      calculatorTab.disabled = false;
      calculatorTab.style.pointerEvents = 'auto';
      console.log('[Loader] ✅ Кнопка калькулятора сделана неактивной но кликабельной');
    }

    if (withdrawalTab) {
      withdrawalTab.classList.remove('active');
      withdrawalTab.classList.add('inactive');
      // ИСПРАВЛЕНИЕ: Убираем disabled чтобы кнопка была кликабельной
      withdrawalTab.disabled = false;
      withdrawalTab.style.pointerEvents = 'auto';
      console.log('[Loader] ✅ Кнопка выплат сделана неактивной но кликабельной');
    }

    // ИСПРАВЛЕНИЕ: Убеждаемся что разделы скрыты
    const calculatorSection = document.getElementById('calculator-section');
    const withdrawalSection = document.getElementById('withdrawal-section');

    if (calculatorSection) {
      calculatorSection.classList.add('hidden');
      calculatorSection.classList.remove('active');
      console.log('[Loader] ✅ Раздел калькулятора скрыт');
    }

    if (withdrawalSection) {
      withdrawalSection.classList.add('hidden');
      withdrawalSection.classList.remove('active');
      console.log('[Loader] ✅ Раздел выплат скрыт');
    }

    // ИСПРАВЛЕНИЕ: Убираем активный класс с табов валют, КРОМЕ TON (по умолчанию)
    const currencyTabs = document.querySelectorAll('.currency-tab');
    currencyTabs.forEach(tab => {
      if (tab.dataset.currency === 'ton') {
        // TON остается активным по умолчанию
        tab.classList.add('active');
        console.log(`[Loader] ✅ Таб TON оставлен активным по умолчанию`);
      } else {
        tab.classList.remove('active');
        console.log(`[Loader] ✅ Таб валюты ${tab.dataset.currency} сделан неактивным`);
      }
    });

    console.log('[Loader] ✨ Неактивное состояние инициализировано');
  }

  /**
   * Инициализирует AudioManager для звука монет (из оригинала)
   */
  initializeAudioManager() {
    console.log('[Loader] 🎵 Инициализация AudioManager...');

    if (window.audioManager) {
      // Инициализируем AudioManager
      if (typeof window.audioManager.init === 'function') {
        window.audioManager.init();
        console.log('[Loader] ✅ AudioManager инициализирован');
      } else {
        console.warn('[Loader] ⚠️ Метод init не найден в AudioManager');
      }

      // Проверяем доступность звука
      if (typeof window.audioManager.playCoinsSound === 'function') {
        console.log('[Loader] ✅ Метод playCoinsSound доступен');
      } else {
        console.warn('[Loader] ⚠️ Метод playCoinsSound не найден');
      }
    } else {
      console.warn('[Loader] ⚠️ AudioManager не найден');
    }

    // Проверяем глобальную функцию
    if (window.playCoinsSound) {
      console.log('[Loader] ✅ Глобальная функция playCoinsSound доступна');
    } else {
      console.warn('[Loader] ⚠️ Глобальная функция playCoinsSound не найдена');
    }

    console.log('[Loader] 🎵 Инициализация AudioManager завершена');
  }
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new ModulesLoader().loadAll());
} else {
    new ModulesLoader().loadAll();
}