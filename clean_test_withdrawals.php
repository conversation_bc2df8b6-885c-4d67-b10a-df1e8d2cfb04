<?php
/**
 * clean_test_withdrawals.php
 * Удаление всех тестовых выплат из базы данных
 */

declare(strict_types=1);

echo "🗑️ ОЧИСТКА ТЕСТОВЫХ ВЫПЛАТ\n";
echo "==========================\n\n";

try {
    $dbPath = __DIR__ . '/database/app.sqlite';
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Подключение к базе данных установлено\n\n";
    
    // Проверяем текущее количество выплат
    $currentCount = $pdo->query("SELECT COUNT(*) FROM user_withdrawals")->fetchColumn();
    echo "📊 Текущее количество выплат: {$currentCount}\n\n";
    
    if ($currentCount > 0) {
        // Показываем все выплаты перед удалением
        echo "📋 Список всех выплат:\n";
        $withdrawals = $pdo->query("
            SELECT id, user_id, amount, currency, status, requested_at 
            FROM user_withdrawals 
            ORDER BY requested_at DESC
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($withdrawals as $withdrawal) {
            echo "   - ID: {$withdrawal['id']}, User: {$withdrawal['user_id']}, Amount: {$withdrawal['amount']} {$withdrawal['currency']}, Status: {$withdrawal['status']}, Date: {$withdrawal['requested_at']}\n";
        }
        
        echo "\n🗑️ Удаляем все выплаты...\n";
        
        // Удаляем все выплаты
        $deletedCount = $pdo->exec("DELETE FROM user_withdrawals");
        
        echo "✅ Удалено выплат: {$deletedCount}\n";
        
        // Сбрасываем автоинкремент
        $pdo->exec("DELETE FROM sqlite_sequence WHERE name='user_withdrawals'");
        echo "✅ Счетчик ID сброшен\n";
        
        // Проверяем результат
        $finalCount = $pdo->query("SELECT COUNT(*) FROM user_withdrawals")->fetchColumn();
        echo "📊 Выплат после очистки: {$finalCount}\n\n";
        
        echo "🎉 ОЧИСТКА ЗАВЕРШЕНА!\n";
        echo "Таблица user_withdrawals теперь пуста и готова для реальных выплат.\n";
        
    } else {
        echo "ℹ️ Таблица user_withdrawals уже пуста.\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}
?>
