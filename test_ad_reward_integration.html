<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест интеграции начислений за рекламу</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-title {
            color: var(--cyber-accent-neon);
            font-size: 18px;
            margin-bottom: 15px;
        }
        .ad-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .ad-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .ad-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .balance-display {
            text-align: center;
            padding: 20px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 10px;
            margin: 20px 0;
        }
        .balance-amount {
            font-size: 32px;
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 10px var(--cyber-glow);
        }
        .daily-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: rgba(255, 107, 53, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: var(--cyber-accent-neon);
        }
        .stat-label {
            font-size: 12px;
            color: var(--cyber-text-secondary);
            margin-top: 5px;
        }
        .status-message {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .status-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--cyber-border);
        }
        .cooldown-timer {
            font-size: 12px;
            color: #ffa500;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); margin-bottom: 30px;">
            📺 Тест интеграции начислений за рекламу
        </h1>

        <!-- Текущий баланс -->
        <div class="test-section">
            <div class="test-title">💰 Текущий баланс</div>
            <div class="balance-display">
                <div id="current-balance" class="balance-amount">0 монет</div>
                <div id="balance-usd" style="color: var(--cyber-text-secondary); margin-top: 10px;">≈ $0.00</div>
            </div>
            <div style="text-align: center;">
                <button class="ad-button" onclick="loadBalance()" style="background: #2196f3;">
                    Обновить баланс
                </button>
            </div>
        </div>

        <!-- Дневная статистика -->
        <div class="test-section">
            <div class="test-title">📊 Дневная статистика</div>
            <div class="daily-stats">
                <div class="stat-item">
                    <div id="earned-today" class="stat-value">0</div>
                    <div class="stat-label">Заработано сегодня</div>
                </div>
                <div class="stat-item">
                    <div id="daily-limit" class="stat-value">200</div>
                    <div class="stat-label">Дневной лимит</div>
                </div>
                <div class="stat-item">
                    <div id="remaining-today" class="stat-value">200</div>
                    <div class="stat-label">Осталось</div>
                </div>
                <div class="stat-item">
                    <div id="daily-progress" class="stat-value">0%</div>
                    <div class="stat-label">Прогресс</div>
                </div>
            </div>
        </div>

        <!-- Кнопки рекламы -->
        <div class="test-section">
            <div class="test-title">📺 Тестирование рекламы</div>
            <div style="text-align: center;">
                <button class="ad-button" data-ad-type="banner" onclick="testAdReward('banner')">
                    📱 Баннер (+5 монет)
                    <div class="cooldown-timer" id="cooldown-banner"></div>
                </button>
                <button class="ad-button" data-ad-type="video" onclick="testAdReward('video')">
                    🎥 Видео (+10 монет)
                    <div class="cooldown-timer" id="cooldown-video"></div>
                </button>
                <button class="ad-button" data-ad-type="rewarded" onclick="testAdReward('rewarded')">
                    🎁 Награда (+15 монет)
                    <div class="cooldown-timer" id="cooldown-rewarded"></div>
                </button>
                <button class="ad-button" data-ad-type="interstitial" onclick="testAdReward('interstitial')">
                    📺 Полноэкранная (+8 монет)
                    <div class="cooldown-timer" id="cooldown-interstitial"></div>
                </button>
            </div>
        </div>

        <!-- Статус -->
        <div class="test-section">
            <div class="test-title">📋 Статус операций</div>
            <div id="status-message" class="status-message status-info">
                Готов к тестированию
            </div>
        </div>

        <!-- Лог операций -->
        <div class="test-section">
            <div class="test-title">📝 Лог операций</div>
            <div id="operation-log" class="log-container">
                Ожидание операций...<br>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="ad-button" onclick="clearLog()" style="background: #666; font-size: 12px; padding: 8px 16px;">
                    Очистить лог
                </button>
            </div>
        </div>
    </div>

    <!-- Подключение скриптов -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="js/balance-api-client.js"></script>
    <script src="js/balance-manager.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/ad-reward-integration.js"></script>

    <script>
        // Эмуляция Telegram WebApp
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1640995200&hash=test_hash',
                    initDataUnsafe: {
                        user: { id: 5880288830, first_name: 'Test User', username: 'testuser' }
                    },
                    ready: () => log('Telegram WebApp готов (эмуляция)', 'success'),
                    expand: () => log('Telegram WebApp развернут (эмуляция)', 'info'),
                    HapticFeedback: {
                        impactOccurred: (type) => log(`Вибрация: ${type}`, 'info'),
                        notificationOccurred: (type) => log(`Уведомление: ${type}`, 'info')
                    }
                }
            };
        }

        let currentBalanceData = null;
        let cooldownTimers = {};

        function log(message, type = 'info') {
            const logEl = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#74c0fc',
                success: '#51cf66',
                error: '#ff6b6b',
                warning: '#ffd43b'
            };
            const color = colors[type] || colors.info;
            logEl.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('operation-log').innerHTML = 'Лог очищен...<br>';
        }

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-message');
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type}`;
        }

        async function loadBalance() {
            try {
                log('Загрузка баланса...', 'info');
                showStatus('Загрузка баланса...', 'info');

                if (!window.balanceApiClient) {
                    throw new Error('BalanceApiClient не загружен');
                }

                const data = await window.balanceApiClient.getUserBalance();
                
                if (data.success) {
                    currentBalanceData = data;
                    updateBalanceDisplay(data);
                    log('Баланс загружен успешно', 'success');
                    showStatus('Баланс обновлен', 'success');
                } else {
                    throw new Error('Ошибка загрузки баланса');
                }

            } catch (error) {
                log(`Ошибка загрузки баланса: ${error.message}`, 'error');
                showStatus(`Ошибка: ${error.message}`, 'error');
            }
        }

        function updateBalanceDisplay(data) {
            document.getElementById('current-balance').textContent = `${data.balance.toLocaleString()} монет`;
            document.getElementById('balance-usd').textContent = `≈ $${data.balance_usd.toFixed(4)}`;

            if (data.daily_stats) {
                const stats = data.daily_stats;
                document.getElementById('earned-today').textContent = stats.earned_today;
                document.getElementById('daily-limit').textContent = stats.limit;
                document.getElementById('remaining-today').textContent = stats.remaining;
                
                const progress = Math.round((stats.earned_today / stats.limit) * 100);
                document.getElementById('daily-progress').textContent = `${progress}%`;
            }
        }

        async function testAdReward(adType) {
            const button = document.querySelector(`[data-ad-type="${adType}"]`);
            
            try {
                log(`Тестирование рекламы типа: ${adType}`, 'info');
                showStatus(`Просмотр рекламы: ${adType}...`, 'info');
                
                // Отключаем кнопку
                button.disabled = true;
                
                // Используем интеграцию начислений
                if (window.adRewardIntegration && window.adRewardIntegration.isInitialized) {
                    const response = await window.adRewardIntegration.recordAdView(adType);
                    
                    if (response.success) {
                        log(`Награда начислена: +${response.reward} монет`, 'success');
                        showStatus(`Награда начислена: +${response.reward} монет!`, 'success');
                        
                        // Обновляем баланс
                        await loadBalance();
                        
                        // Запускаем cooldown
                        startCooldown(button, adType, 30);
                        
                    } else {
                        throw new Error(response.error || 'Ошибка начисления');
                    }
                } else {
                    throw new Error('AdRewardIntegration не инициализирован');
                }

            } catch (error) {
                log(`Ошибка начисления за ${adType}: ${error.message}`, 'error');
                showStatus(`Ошибка: ${error.message}`, 'error');
                button.disabled = false;
            }
        }

        function startCooldown(button, adType, seconds) {
            const timerEl = document.getElementById(`cooldown-${adType}`);
            let remaining = seconds;
            
            const updateTimer = () => {
                if (remaining > 0) {
                    timerEl.textContent = `Ожидание: ${remaining}с`;
                    remaining--;
                    setTimeout(updateTimer, 1000);
                } else {
                    timerEl.textContent = '';
                    button.disabled = false;
                    log(`Cooldown для ${adType} завершен`, 'info');
                }
            };
            
            updateTimer();
        }

        // Инициализация при загрузке
        window.addEventListener('load', () => {
            log('Страница тестирования загружена', 'info');
            showStatus('Инициализация...', 'info');
            
            // Ждем инициализации интеграции
            setTimeout(() => {
                if (window.adRewardIntegration) {
                    log('AdRewardIntegration найден', 'success');
                    showStatus('Система готова к тестированию', 'success');
                } else {
                    log('AdRewardIntegration не найден', 'error');
                    showStatus('Ошибка инициализации', 'error');
                }
            }, 2000);
            
            // Загружаем баланс
            setTimeout(() => {
                loadBalance();
            }, 3000);
        });

        // Глобальная функция для совместимости
        window.showStatus = showStatus;
    </script>
</body>
</html>
