<?php
/**
 * sqlite_manager_clean.php
 * Чистый SQLite менеджер без эмулятора
 */

declare(strict_types=1);

class SQLiteManagerClean
{
    private ?SQLite3 $sqlite = null;
    private ?PDO $pdo = null;
    private string $dbPath;
    private bool $connected = false;

    public function __construct(string $dbPath = null)
    {
        $this->dbPath = $dbPath ?? __DIR__ . '/app.sqlite';
        $this->connect();
    }

    /**
     * Подключение к SQLite базе данных
     */
    private function connect(): void
    {
        try {
            // Пробуем PDO
            $this->pdo = new PDO('sqlite:' . $this->dbPath);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connected = true;
            echo "✅ Подключение к SQLite через PDO: {$this->dbPath}\n";
            
            // Создаем схему если нужно
            $this->createSchema();
            
        } catch (Exception $e) {
            try {
                // Пробуем SQLite3
                $this->sqlite = new SQLite3($this->dbPath);
                $this->sqlite->enableExceptions(true);
                $this->connected = true;
                echo "✅ Подключение к SQLite через SQLite3: {$this->dbPath}\n";
                
                // Создаем схему если нужно
                $this->createSchema();
                
            } catch (Exception $e2) {
                throw new Exception("Не удалось подключиться к SQLite: " . $e2->getMessage());
            }
        }
    }

    /**
     * Создание схемы базы данных
     */
    private function createSchema(): void
    {
        $schemaFile = __DIR__ . '/schema.sql';
        if (!file_exists($schemaFile)) {
            echo "⚠️ Файл схемы не найден: {$schemaFile}\n";
            return;
        }

        $schema = file_get_contents($schemaFile);
        $statements = $this->splitSQLStatements($schema);

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement)) continue;

            try {
                $this->execute($statement);
            } catch (Exception $e) {
                // Игнорируем ошибки "table already exists"
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "⚠️ Ошибка создания схемы: " . $e->getMessage() . "\n";
                }
            }
        }
    }

    /**
     * Разбивка SQL на отдельные команды
     */
    private function splitSQLStatements(string $sql): array
    {
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';

        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];

            if (!$inString && ($char === '"' || $char === "'")) {
                $inString = true;
                $stringChar = $char;
            } elseif ($inString && $char === $stringChar) {
                $inString = false;
                $stringChar = '';
            }

            if (!$inString && $char === ';') {
                $statements[] = trim($current);
                $current = '';
            } else {
                $current .= $char;
            }
        }

        if (!empty(trim($current))) {
            $statements[] = trim($current);
        }

        return array_filter($statements);
    }

    /**
     * Выполнение SQL запроса
     */
    public function query(string $sql, array $params = []): array
    {
        if (!$this->connected) {
            throw new Exception("Нет подключения к базе данных");
        }

        try {
            if ($this->pdo) {
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute($params);
                
                if (stripos($sql, 'SELECT') === 0) {
                    return $stmt->fetchAll(PDO::FETCH_ASSOC);
                } else {
                    return ['affected_rows' => $stmt->rowCount()];
                }
                
            } elseif ($this->sqlite) {
                $stmt = $this->sqlite->prepare($sql);
                
                foreach ($params as $index => $value) {
                    $stmt->bindValue($index + 1, $value);
                }
                
                $result = $stmt->execute();
                
                if (stripos($sql, 'SELECT') === 0) {
                    $rows = [];
                    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                        $rows[] = $row;
                    }
                    return $rows;
                } else {
                    return ['affected_rows' => $this->sqlite->changes()];
                }
            }
            
        } catch (Exception $e) {
            throw new Exception("Ошибка выполнения запроса: " . $e->getMessage() . " | SQL: " . $sql);
        }

        return [];
    }

    /**
     * Выполнение SQL без возврата результата
     */
    public function execute(string $sql, array $params = []): bool
    {
        try {
            $this->query($sql, $params);
            return true;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Получение всех пользователей
     */
    public function getAllUsers(): array
    {
        return $this->query("SELECT * FROM users ORDER BY telegram_id");
    }

    /**
     * Получение пользователя по Telegram ID
     */
    public function getUserByTelegramId(string $telegramId): ?array
    {
        $result = $this->query("SELECT * FROM users WHERE telegram_id = ?", [$telegramId]);
        return $result[0] ?? null;
    }

    /**
     * Создание нового пользователя
     */
    public function createUser(array $userData): bool
    {
        $sql = "INSERT INTO users (
            telegram_id, username, first_name, last_name, 
            language_code, is_bot, is_premium, 
            balance, total_earned, referrer_id, 
            referral_earnings, withdrawals_count, 
            suspicious_activity_count, is_blocked, 
            blocked_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $userData['telegram_id'],
            $userData['username'] ?? null,
            $userData['first_name'] ?? '',
            $userData['last_name'] ?? '',
            $userData['language_code'] ?? 'ru',
            $userData['is_bot'] ?? 0,
            $userData['is_premium'] ?? 0,
            $userData['balance'] ?? 0,
            $userData['total_earned'] ?? 0,
            $userData['referrer_id'] ?? null,
            $userData['referral_earnings'] ?? 0,
            $userData['withdrawals_count'] ?? 0,
            $userData['suspicious_activity_count'] ?? 0,
            $userData['is_blocked'] ?? 0,
            $userData['blocked_at'] ?? null,
            $userData['created_at'] ?? date('Y-m-d H:i:s'),
            $userData['updated_at'] ?? date('Y-m-d H:i:s')
        ];
        
        try {
            $this->query($sql, $params);
            return true;
        } catch (Exception $e) {
            throw new Exception("Ошибка создания пользователя: " . $e->getMessage());
        }
    }

    /**
     * Обновление баланса пользователя
     */
    public function updateUserBalance(string $telegramId, float $newBalance): bool
    {
        $sql = "UPDATE users SET balance = ?, updated_at = CURRENT_TIMESTAMP WHERE telegram_id = ?";
        try {
            $this->query($sql, [$newBalance, $telegramId]);
            return true;
        } catch (Exception $e) {
            throw new Exception("Ошибка обновления баланса: " . $e->getMessage());
        }
    }

    /**
     * Получение информации о базе данных
     */
    public function getDatabaseInfo(): array
    {
        $info = [
            'database_type' => 'real_sqlite',
            'file_path' => $this->dbPath,
            'file_size' => file_exists($this->dbPath) ? filesize($this->dbPath) : 0,
            'last_updated' => date('Y-m-d H:i:s'),
            'connection_type' => $this->pdo ? 'PDO' : 'SQLite3'
        ];

        // Получаем статистику таблиц
        $tables = ['users', 'coin_transactions', 'user_withdrawals', 'ad_clicks'];
        foreach ($tables as $table) {
            try {
                $result = $this->query("SELECT COUNT(*) as count FROM {$table}");
                $info['tables'][$table] = $result[0]['count'] ?? 0;
            } catch (Exception $e) {
                $info['tables'][$table] = 0;
            }
        }

        return $info;
    }

    /**
     * Закрытие соединения
     */
    public function close(): void
    {
        if ($this->pdo) {
            $this->pdo = null;
        }
        if ($this->sqlite) {
            $this->sqlite->close();
        }
        $this->connected = false;
    }

    /**
     * Деструктор
     */
    public function __destruct()
    {
        $this->close();
    }
}
?>
