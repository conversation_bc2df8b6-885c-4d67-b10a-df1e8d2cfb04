<?php
/**
 * test_admin_sqlite.php
 * Тестирование админки с SQLite базой данных
 */

declare(strict_types=1);

echo "🧪 ТЕСТИРОВАНИЕ АДМИНКИ С SQLITE\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Подключаем SQLite API
require_once __DIR__ . '/api/db_mock_final_sqlite.php';

try {
    echo "🔍 Тест 1: Подключение к SQLite\n";
    $sqlite = getSQLiteManager();
    $dbType = $sqlite->getDatabaseType();
    echo "  📊 Тип базы данных: {$dbType}\n";
    
    if ($dbType === 'real_sqlite') {
        echo "  ✅ Используется настоящая SQLite!\n";
    } else {
        echo "  ⚠️ Используется эмулированная SQLite\n";
    }
    
    echo "\n🔍 Тест 2: Загрузка пользователей\n";
    $users = loadUserData();
    $userCount = count($users);
    echo "  👥 Загружено пользователей: {$userCount}\n";
    
    if ($userCount > 0) {
        echo "  ✅ Пользователи загружаются из SQLite\n";
        
        // Показываем первых 3 пользователей
        $firstUsers = array_slice($users, 0, 3, true);
        foreach ($firstUsers as $telegramId => $user) {
            $balance = $user['balance'] ?? 0;
            $firstName = $user['first_name'] ?? 'Неизвестно';
            echo "    👤 {$telegramId}: {$firstName} (баланс: {$balance})\n";
        }
    } else {
        echo "  ❌ Пользователи не найдены\n";
    }
    
    echo "\n🔍 Тест 3: Статистика базы данных\n";
    $stats = getDatabaseStats();
    echo "  📊 Статистика:\n";
    echo "    👥 Пользователи: " . ($stats['users_count'] ?? 0) . "\n";
    echo "    👁️ Просмотры рекламы: " . ($stats['ad_views_count'] ?? 0) . "\n";
    echo "    🖱️ Клики по рекламе: " . ($stats['ad_clicks_count'] ?? 0) . "\n";
    echo "    🎫 Токены рекламы: " . ($stats['ad_tokens_count'] ?? 0) . "\n";
    echo "    💾 Размер файла: " . number_format($stats['file_size'] ?? 0) . " bytes\n";
    echo "    🔧 Тип БД: " . ($stats['database_type'] ?? 'неизвестно') . "\n";
    
    if (($stats['users_count'] ?? 0) > 0) {
        echo "  ✅ Статистика загружается из SQLite\n";
    } else {
        echo "  ❌ Проблема со статистикой\n";
    }
    
    echo "\n🔍 Тест 4: Проверка конкретного пользователя\n";
    if ($userCount > 0) {
        $firstUserId = array_key_first($users);
        $userDetails = getUserDetails($firstUserId);
        
        if ($userDetails) {
            echo "  👤 Пользователь {$firstUserId}:\n";
            echo "    📛 Имя: " . ($userDetails['first_name'] ?? 'Неизвестно') . "\n";
            echo "    💰 Баланс: " . ($userDetails['balance'] ?? 0) . "\n";
            echo "    💎 Заработано: " . ($userDetails['total_earned'] ?? 0) . "\n";
            echo "    📅 Регистрация: " . date('Y-m-d H:i:s', $userDetails['registered_at'] ?? time()) . "\n";
            echo "  ✅ Детали пользователя загружаются из SQLite\n";
        } else {
            echo "  ❌ Не удалось загрузить детали пользователя\n";
        }
    }
    
    echo "\n🔍 Тест 5: Тестирование функций рекламы\n";
    if ($userCount > 0) {
        $testUserId = array_key_first($users);
        
        // Тест логирования просмотра рекламы
        $viewResult = logAdView($testUserId, 'test_banner', 0.001);
        if ($viewResult) {
            echo "  ✅ Логирование просмотра рекламы работает\n";
        } else {
            echo "  ❌ Ошибка логирования просмотра рекламы\n";
        }
        
        // Тест логирования клика
        $clickResult = logAdClick($testUserId, 'test_banner', 'button_click', 'test');
        if ($clickResult) {
            echo "  ✅ Логирование клика по рекламе работает\n";
        } else {
            echo "  ❌ Ошибка логирования клика по рекламе\n";
        }
    }
    
    echo "\n🔍 Тест 6: Проверка файлов админки\n";
    $adminFiles = [
        'api/admin/users.php' => 'Управление пользователями',
        'api/admin/stats.php' => 'Статистика',
        'api/admin/user_detail.php' => 'Детали пользователя',
        'api/admin/ad_stats_api.php' => 'API статистики рекламы',
        'api/admin/user_stats_api.php' => 'API статистики пользователей'
    ];

    $adminOkCount = 0;
    foreach ($adminFiles as $file => $description) {
        $fullPath = __DIR__ . '/' . $file;
        if (file_exists($fullPath)) {
            $content = file_get_contents($fullPath);
            if (strpos($content, 'db_mock_final_sqlite.php') !== false ||
                strpos($content, 'ОБНОВЛЕНО: Использует SQLite') !== false) {
                echo "  ✅ {$description}: использует SQLite\n";
                $adminOkCount++;
            } else {
                echo "  ⚠️ {$description}: возможно использует старый API\n";
            }
        } else {
            echo "  ❌ {$description}: файл не найден\n";
        }
    }
    
    echo "\n📋 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ АДМИНКИ:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if ($dbType === 'real_sqlite' && $userCount > 0 && $adminOkCount >= 4) {
        echo "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!\n";
        echo "✅ Админка полностью использует SQLite базу данных\n";
        echo "✅ Все основные функции работают корректно\n";
        echo "✅ Данные загружаются из настоящей SQLite\n";
        echo "\n🚀 АДМИНКА ГОТОВА К ИСПОЛЬЗОВАНИЮ!\n";
    } else {
        echo "⚠️ ОБНАРУЖЕНЫ ПРОБЛЕМЫ:\n";
        if ($dbType !== 'real_sqlite') {
            echo "  - Не используется настоящая SQLite\n";
        }
        if ($userCount === 0) {
            echo "  - Нет пользователей в базе данных\n";
        }
        if ($adminOkCount < 4) {
            echo "  - Не все файлы админки обновлены\n";
        }
        echo "\n🔧 ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ НАСТРОЙКА\n";
    }
    
    echo "\n📊 ИТОГОВАЯ СТАТИСТИКА:\n";
    echo "  🗄️ Тип базы данных: {$dbType}\n";
    echo "  👥 Пользователей в базе: {$userCount}\n";
    echo "  📁 Обновленных файлов админки: {$adminOkCount}/5\n";
    echo "  💾 Размер SQLite базы: " . number_format($stats['file_size'] ?? 0) . " bytes\n";
    
} catch (Exception $e) {
    echo "\n❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "🔧 Проверьте настройки SQLite и повторите тест\n";
}

echo "\nДата тестирования: " . date('Y-m-d H:i:s') . "\n";
?>
