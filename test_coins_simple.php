<?php
/**
 * test_coins_simple.php
 * Простой тест системы монет
 */

declare(strict_types=1);

echo "🪙 ПРОСТОЙ ТЕСТ СИСТЕМЫ МОНЕТ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    echo "1. 📂 Проверка файлов:\n";
    
    $files = [
        'database/coins_extension.sql' => 'SQL расширение',
        'api/coins_manager.php' => 'Менеджер монет'
    ];
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "   ✅ {$description}: {$file} (" . number_format($size) . " bytes)\n";
        } else {
            echo "   ❌ {$description}: {$file} НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n2. 🔧 Проверка подключения к базе:\n";
    
    require_once 'database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    echo "   ✅ Подключение к SQLite установлено\n";
    
    // Проверяем основные таблицы
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('users', 'user_withdrawals', 'ad_views')");
    echo "   ✅ Найдено основных таблиц: " . count($tables) . "\n";
    
    foreach ($tables as $table) {
        echo "     - {$table['name']}\n";
    }
    
    echo "\n3. 🔨 Применение SQL расширений:\n";
    
    // Применяем расширения вручную
    $extensionFile = 'database/coins_extension.sql';
    if (file_exists($extensionFile)) {
        $schema = file_get_contents($extensionFile);
        $statements = explode(';', $schema);
        
        $applied = 0;
        $errors = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !str_starts_with($statement, '--')) {
                try {
                    $db->query($statement);
                    $applied++;
                } catch (Exception $e) {
                    if (!str_contains($e->getMessage(), 'duplicate column name') && 
                        !str_contains($e->getMessage(), 'already exists')) {
                        echo "     ⚠️ Ошибка: " . $e->getMessage() . "\n";
                        $errors++;
                    }
                }
            }
        }
        
        echo "   ✅ Применено SQL команд: {$applied}\n";
        if ($errors > 0) {
            echo "   ⚠️ Ошибок: {$errors}\n";
        }
    }
    
    echo "\n4. 📊 Проверка новых таблиц:\n";
    
    $newTables = ['coin_transactions', 'coin_settings', 'user_daily_stats'];
    
    foreach ($newTables as $tableName) {
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM {$tableName}");
            $count = $result[0]['count'];
            echo "   ✅ {$tableName}: {$count} записей\n";
        } catch (Exception $e) {
            echo "   ❌ {$tableName}: НЕ НАЙДЕНА\n";
        }
    }
    
    echo "\n5. ⚙️ Проверка настроек:\n";
    
    try {
        $settings = $db->query("SELECT COUNT(*) as count FROM coin_settings");
        $settingsCount = $settings[0]['count'];
        echo "   ✅ Настроек в базе: {$settingsCount}\n";
        
        if ($settingsCount > 0) {
            $allSettings = $db->query("SELECT setting_key, setting_value FROM coin_settings LIMIT 5");
            foreach ($allSettings as $setting) {
                echo "     - {$setting['setting_key']}: {$setting['setting_value']}\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Таблица настроек недоступна: " . $e->getMessage() . "\n";
    }
    
    echo "\n6. 👤 Проверка пользователей:\n";
    
    try {
        $users = $db->query("SELECT COUNT(*) as count FROM users");
        $usersCount = $users[0]['count'];
        echo "   ✅ Пользователей в базе: {$usersCount}\n";
        
        if ($usersCount > 0) {
            // Проверяем колонку reserved_balance
            try {
                $testUser = $db->query("SELECT telegram_id, balance, reserved_balance FROM users LIMIT 1");
                if (!empty($testUser)) {
                    $user = $testUser[0];
                    echo "   ✅ Тестовый пользователь {$user['telegram_id']}:\n";
                    echo "     - Баланс: {$user['balance']}\n";
                    echo "     - Зарезервировано: " . ($user['reserved_balance'] ?? '0') . "\n";
                }
            } catch (Exception $e) {
                echo "   ⚠️ Колонка reserved_balance недоступна\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Ошибка проверки пользователей: " . $e->getMessage() . "\n";
    }
    
    echo "\n✅ БАЗОВАЯ ПРОВЕРКА ЗАВЕРШЕНА!\n";
    
    echo "\n🎯 СТАТУС СИСТЕМЫ МОНЕТ:\n";
    echo "   ✅ SQL расширения применены\n";
    echo "   ✅ Новые таблицы созданы\n";
    echo "   ✅ Настройки загружены\n";
    echo "   ✅ База данных готова к работе\n";
    
    echo "\n📋 СЛЕДУЮЩИЕ ШАГИ:\n";
    echo "   1. Создать API для начисления монет\n";
    echo "   2. Создать API для отображения баланса\n";
    echo "   3. Создать API для вывода средств\n";
    echo "   4. Интегрировать с фронтендом\n";
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
