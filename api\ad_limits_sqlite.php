<?php
/**
 * ad_limits_sqlite.php
 * Менеджер лимитов показов рекламы для SQLite
 */

declare(strict_types=1);

require_once __DIR__ . '/../database/real_sqlite_manager.php';

class AdLimitsSQLite
{
    private RealSQLiteManager $sqlite;
    private const DAILY_LIMIT = 20; // Лимит показов в день для каждого типа рекламы
    
    public function __construct()
    {
        $this->sqlite = new RealSQLiteManager();
    }
    
    /**
     * Получить текущий счетчик показов для пользователя и типа рекламы
     */
    public function getCurrentCount(int $userId, string $adType): int
    {
        $today = date('Y-m-d');
        
        $result = $this->sqlite->query("
            SELECT daily_count 
            FROM ad_limits 
            WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
        ", [$userId, $adType, $today]);
        
        return $result[0]['daily_count'] ?? 0;
    }
    
    /**
     * Получить количество оставшихся показов
     */
    public function getRemainingCount(int $userId, string $adType): int
    {
        $currentCount = $this->getCurrentCount($userId, $adType);
        return max(0, self::DAILY_LIMIT - $currentCount);
    }
    
    /**
     * Проверить, можно ли показать рекламу
     */
    public function canShowAd(int $userId, string $adType): bool
    {
        return $this->getRemainingCount($userId, $adType) > 0;
    }
    
    /**
     * Увеличить счетчик показов
     */
    public function incrementCount(int $userId, string $adType): bool
    {
        $today = date('Y-m-d');
        
        try {
            // Проверяем, есть ли запись
            $existing = $this->sqlite->query("
                SELECT id, daily_count 
                FROM ad_limits 
                WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
            ", [$userId, $adType, $today]);
            
            if (empty($existing)) {
                // Создаем новую запись
                $this->sqlite->query("
                    INSERT INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                    VALUES (?, ?, 1, ?)
                ", [$userId, $adType, $today]);
            } else {
                // Увеличиваем существующий счетчик
                $newCount = $existing[0]['daily_count'] + 1;
                $this->sqlite->query("
                    UPDATE ad_limits 
                    SET daily_count = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                ", [$newCount, $userId, $adType, $today]);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Ошибка увеличения счетчика лимитов: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получить текст для кнопки с счетчиком
     */
    public function getButtonText(int $userId, string $adType, string $baseText = 'Открыть ссылку'): string
    {
        $current = $this->getCurrentCount($userId, $adType);
        $remaining = $this->getRemainingCount($userId, $adType);
        
        if ($remaining <= 0) {
            return "{$baseText} (лимит исчерпан)";
        }
        
        return "{$baseText} ({$current}/" . self::DAILY_LIMIT . ")";
    }
    
    /**
     * Получить все лимиты пользователя за сегодня
     */
    public function getUserLimits(int $userId): array
    {
        $today = date('Y-m-d');
        
        $results = $this->sqlite->query("
            SELECT ad_type, daily_count 
            FROM ad_limits 
            WHERE user_id = ? AND last_reset_date = ?
            ORDER BY ad_type
        ", [$userId, $today]);
        
        $limits = [];
        foreach ($results as $row) {
            $limits[$row['ad_type']] = [
                'current' => (int)$row['daily_count'],
                'limit' => self::DAILY_LIMIT,
                'remaining' => max(0, self::DAILY_LIMIT - (int)$row['daily_count']),
                'can_show' => (int)$row['daily_count'] < self::DAILY_LIMIT
            ];
        }
        
        return $limits;
    }
}

/**
 * Глобальные функции для удобства использования
 */

/**
 * Проверить, можно ли показать рекламу пользователю
 */
function canShowAdToUser(int $userId, string $adType): bool
{
    $manager = new AdLimitsSQLite();
    return $manager->canShowAd($userId, $adType);
}

/**
 * Увеличить счетчик показов рекламы
 */
function incrementAdCount(int $userId, string $adType): bool
{
    $manager = new AdLimitsSQLite();
    return $manager->incrementCount($userId, $adType);
}

/**
 * Получить текст для кнопки с счетчиком
 */
function getAdButtonText(int $userId, string $adType, string $baseText = 'Открыть ссылку'): string
{
    $manager = new AdLimitsSQLite();
    return $manager->getButtonText($userId, $adType, $baseText);
}

/**
 * Получить оставшееся количество показов
 */
function getRemainingAdCount(int $userId, string $adType): int
{
    $manager = new AdLimitsSQLite();
    return $manager->getRemainingCount($userId, $adType);
}
?>
