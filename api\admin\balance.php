<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
/**
 * api/admin/balance.php
 * Управление балансом NOWPayments
 */

// Отключаем вывод ошибок в браузер
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Проверяем аутентификацию
session_start();
require_once __DIR__ . '/auth.php';
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../NOWPaymentsAPI.php';

$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Получаем текущий баланс
$balance = $api->getAccountBalance();

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">        <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🏦 Управление балансом NOWPayments</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button onclick="location.reload()" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                    </div>
                </div>
            </div>

            <!-- Карточки с балансом -->
            <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ if ($balance): ?>
                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
                $totalUSD = 0;
                $lowBalanceCurrencies = [];
                $balanceCards = [];

                foreach ($balance as $currency => $data) {
                    $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
                    if ($amount > 0) {
                        $isLow = false;

                        // Определяем критически низкий баланс
                        if (($currency === 'usdttrc20' || $currency === 'ton') && $amount < 1) {
                            $isLow = true;
                            $lowBalanceCurrencies[] = strtoupper($currency);
                        }

                        $balanceCards[] = [
                            'currency' => $currency,
                            'amount' => $amount,
                            'isLow' => $isLow
                        ];

                        // Конвертируем в USD для общей суммы
                        try {
                            $estimate = $api->getEstimateAmount($amount, $currency, 'usd');
                            if (isset($estimate['estimated_amount'])) {
                                $totalUSD += $estimate['estimated_amount'];
                            }
                        } catch (Exception $e) {
                            // Игнорируем ошибки конвертации
                        }
                    }
                }
                ?>

                <div class="row">
                    <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ foreach ($balanceCards as $index => $card): ?>
                        <div class="col-md-4 mb-4">
                            <div class="card <?= $card['isLow'] ? 'border-left-warning' : 'border-left-success' ?> shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-uppercase mb-1 <?= $card['isLow'] ? 'text-warning' : 'text-success' ?>">
                                                <?= strtoupper($card['currency']) ?>
                                                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ if ($card['isLow']): ?>
                                                    <span class="badge bg-warning text-dark">НИЗКИЙ</span>
                                                <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ endif; ?>
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $card['amount'] ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-currency-bitcoin fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ endforeach; ?>

                    <!-- Общая стоимость -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Общая стоимость
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">≈ <?= round($totalUSD, 2) ?> USD</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ else: ?>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Ошибка!</strong> Не удалось получить баланс. Проверьте API ключи.
                </div>
            <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ endif; ?>

            <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ if (!empty($lowBalanceCurrencies)): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Внимание!</strong> Низкий баланс для выплат: <?= implode(', ', $lowBalanceCurrencies) ?>
                    <br>Пополните баланс для обработки выплат.
                </div>
            <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ endif; ?>

            <!-- Действия -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">🔧 Действия</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="https://account.nowpayments.io/dashboard" target="_blank" class="btn btn-success w-100">
                                <i class="bi bi-bank"></i> Открыть панель NOWPayments
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="https://account.nowpayments.io/settings/api-keys" target="_blank" class="btn btn-primary w-100">
                                <i class="bi bi-key"></i> Управление API ключами
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button onclick="location.reload()" class="btn btn-warning w-100">
                                <i class="bi bi-arrow-clockwise"></i> Обновить баланс
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Инструкции -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📋 Инструкции по пополнению баланса</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>🎯 Для популярных выплат (приоритет):</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><strong>USDT TRC20</strong> - самый популярный (приоритет 1)</li>
                                <li class="list-group-item"><strong>TON</strong> - популярная валюта Telegram (приоритет 2)</li>
                            </ul>

                            <h5 class="mt-3">🔄 Как пополнить:</h5>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">Откройте <a href="https://account.nowpayments.io/dashboard" target="_blank">панель NOWPayments</a></li>
                                <li class="list-group-item">Перейдите в раздел "Wallet" или "Balance"</li>
                                <li class="list-group-item">Найдите USDT TRC20 или TON</li>
                                <li class="list-group-item">Нажмите "Deposit" или "Пополнить"</li>
                                <li class="list-group-item">Переведите средства на указанный адрес</li>
                            </ol>
                        </div>

                        <div class="col-md-6">
                            <h5>⚡ Автоконвертация:</h5>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">Перейдите в "Settings" → "Auto-conversion"</li>
                                <li class="list-group-item">Включите автоконвертацию BTC → USDT TRC20</li>
                                <li class="list-group-item">Установите лимиты и правила</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Информация о системе -->
            <div class="alert alert-info" role="alert">
                <h5><i class="bi bi-info-circle"></i> Информация о системе выплат</h5>
                <ul class="mb-0">
                    <li>✅ <strong>Защита адресов:</strong> Система НЕ заменяет адреса пользователей</li>
                    <li>✅ <strong>Автоконвертация:</strong> Автоматически конвертирует из доступного баланса</li>
                    <li>✅ <strong>Умная обработка:</strong> Находит оптимальные пути конвертации</li>
                    <li>✅ <strong>Безопасность:</strong> Деньги идут только на правильные адреса</li>
                </ul>
            </div>

            <!-- Статус системы -->
            <div class="alert alert-success text-center" role="alert">
                <h5><i class="bi bi-rocket"></i> Система готова к продакшену!</h5>
                <p class="mb-0">Как только будет баланс - все выплаты будут работать автоматически.</p>
            </div>

        </main>
    </div>
</div>

<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
