<?php
/**
 * api/safe_json_logger_unified.php
 * Унифицированная версия логгера с использованием единой JSON структуры
 */

declare(strict_types=1);

require_once __DIR__ . "/../database/unified_json_manager.php";

class SafeJsonLoggerUnified 
{
    private UnifiedJsonManager $manager;

    public function __construct()
    {
        $this->manager = UnifiedJsonManager::getInstance();
    }

    /**
     * Безопасно добавляет запись о просмотре рекламы
     */
    public function appendToJsonFile(string $filePath, array $newEntry): bool
    {
        // Определяем тип данных по пути файла
        if (strpos($filePath, "ad_views") !== false) {
            return $this->manager->logAdView($newEntry);
        } elseif (strpos($filePath, "ad_clicks") !== false) {
            return $this->manager->logAdClick($newEntry);
        } elseif (strpos($filePath, "support_messages") !== false) {
            return $this->manager->saveSupportMessage($newEntry);
        }
        
        // Для других типов файлов возвращаем true (совместимость)
        return true;
    }

    /**
     * Получение статистики
     */
    public function getStats(): array
    {
        return $this->manager->getStatistics();
    }
}

// Глобальные функции для совместимости
function logAdViewSafe(array $viewData): bool
{
    static $logger = null;
    if ($logger === null) {
        $logger = new SafeJsonLoggerUnified();
    }
    return $logger->appendToJsonFile("ad_views.json", $viewData);
}

function logAdClickSafe(array $clickData): bool
{
    static $logger = null;
    if ($logger === null) {
        $logger = new SafeJsonLoggerUnified();
    }
    return $logger->appendToJsonFile("ad_clicks.json", $clickData);
}
?>