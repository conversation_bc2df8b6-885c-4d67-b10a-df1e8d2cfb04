<?php
/**
 * migrate_correct_data.php
 * Корректная миграция всех данных с сервера в правильном порядке
 */

declare(strict_types=1);

echo "🚀 КОРРЕКТНАЯ МИГРАЦИЯ ДАННЫХ С СЕРВЕРА\n";
echo "=======================================\n\n";

try {
    $dbPath = __DIR__ . '/database/app.sqlite';
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Подключение к базе данных установлено\n\n";
    
    // Создаем недостающие таблицы (без ошибок синтаксиса)
    echo "📋 СОЗДАНИЕ НЕДОСТАЮЩИХ ТАБЛИЦ\n";
    echo "===============================\n";
    
    // Таблица для продвинутых отпечатков устройств
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS advanced_fingerprints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id BIGINT NOT NULL,
            fingerprint_hash TEXT NOT NULL UNIQUE,
            components TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_advanced_fingerprints_user_id ON advanced_fingerprints(user_id)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_advanced_fingerprints_hash ON advanced_fingerprints(fingerprint_hash)");
    echo "   ✅ Таблица advanced_fingerprints создана\n";
    
    // Таблица для VPN блокировок
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS vpn_blocks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address TEXT NOT NULL UNIQUE,
            provider TEXT,
            blocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            reason TEXT
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_vpn_blocks_ip ON vpn_blocks(ip_address)");
    echo "   ✅ Таблица vpn_blocks создана\n";
    
    // Таблица для использованных токенов
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS used_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token TEXT NOT NULL UNIQUE,
            user_id BIGINT NOT NULL,
            used_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_used_tokens_token ON used_tokens(token)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_used_tokens_user_id ON used_tokens(user_id)");
    echo "   ✅ Таблица used_tokens создана\n\n";
    
    // ШАГ 1: МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ (БЕЗ ВЫПЛАТ)
    echo "1. 👥 МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ\n";
    echo "============================\n";
    
    $userDataFile = 'database/user_data.json';
    if (!file_exists($userDataFile)) {
        throw new Exception("❌ Файл user_data.json не найден");
    }
    
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (!$userData) {
        throw new Exception("❌ Не удалось прочитать user_data.json");
    }
    
    echo "📁 Найдено " . count($userData) . " пользователей в JSON файле\n";
    
    $migratedUsers = 0;
    $skippedUsers = 0;
    
    foreach ($userData as $telegramId => $user) {
        try {
            // Проверяем, существует ли пользователь
            $existing = $pdo->prepare("SELECT id FROM users WHERE telegram_id = ?");
            $existing->execute([$telegramId]);
            
            if ($existing->fetch()) {
                $skippedUsers++;
                continue; // Пропускаем существующих пользователей
            }
            
            // Создаем нового пользователя
            $stmt = $pdo->prepare("
                INSERT INTO users (
                    telegram_id, username, first_name, last_name,
                    language, balance, total_earned, referrer_id,
                    referral_earnings, withdrawals_count, 
                    suspicious_activity_count, blocked,
                    registered_at, last_activity, joined,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $telegramId,
                $user['username'] ?? null,
                $user['first_name'] ?? '',
                $user['last_name'] ?? '',
                $user['language'] ?? 'ru',
                $user['balance'] ?? 0,
                $user['total_earned'] ?? 0,
                $user['referrer_id'] ?? null,
                $user['referral_earnings'] ?? 0,
                $user['withdrawals_count'] ?? 0,
                $user['suspicious_activity_count'] ?? 0,
                $user['blocked'] ?? false ? 1 : 0,
                $user['registered_at'] ?? time(),
                $user['last_activity'] ?? time(),
                $user['joined'] ?? time(),
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s')
            ]);
            
            $migratedUsers++;
            
            if ($migratedUsers % 100 == 0) {
                echo "   📊 Обработано {$migratedUsers} пользователей...\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Ошибка с пользователем {$telegramId}: " . $e->getMessage() . "\n";
            $skippedUsers++;
        }
    }
    
    echo "   ✅ Создано пользователей: {$migratedUsers}\n";
    echo "   ⏭️ Пропущено: {$skippedUsers}\n\n";
    
    // ШАГ 2: МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ (ТРАНЗАКЦИИ)
    echo "2. 📺 МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ\n";
    echo "=================================\n";
    
    $adViewsFile = 'database/ad_views.json';
    if (file_exists($adViewsFile)) {
        $adViews = json_decode(file_get_contents($adViewsFile), true);
        echo "📁 Найдено " . count($adViews) . " просмотров\n";
        
        $migratedViews = 0;
        $skippedViews = 0;
        
        foreach ($adViews as $view) {
            try {
                // Проверяем дубликаты по времени и пользователю
                $existing = $pdo->prepare("
                    SELECT id FROM coin_transactions 
                    WHERE user_id = ? AND created_at = ? AND amount = ?
                ");
                $existing->execute([
                    $view['user_id'],
                    $view['timestamp'],
                    $view['reward']
                ]);
                
                if ($existing->fetch()) {
                    $skippedViews++;
                    continue;
                }
                
                // Получаем текущий баланс пользователя
                $balanceStmt = $pdo->prepare("SELECT balance FROM users WHERE telegram_id = ?");
                $balanceStmt->execute([$view['user_id']]);
                $currentBalance = $balanceStmt->fetchColumn() ?: 0;
                
                // Создаем транзакцию
                $stmt = $pdo->prepare("
                    INSERT INTO coin_transactions (
                        user_id, amount, operation, transaction_type,
                        balance_before, balance_after, source_type, 
                        description, metadata, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $view['user_id'],
                    $view['reward'],
                    'credit',
                    'earn',
                    $currentBalance,
                    $currentBalance + $view['reward'],
                    'ad_view',
                    "Ad reward for " . ($view['ad_type'] ?? 'unknown'),
                    json_encode([
                        'ad_type' => $view['ad_type'] ?? 'unknown',
                        'ip' => $view['ip'] ?? 'unknown',
                        'user_agent' => substr($view['user_agent'] ?? 'unknown', 0, 500),
                        'migrated' => true
                    ]),
                    $view['timestamp']
                ]);
                
                $migratedViews++;
                
                if ($migratedViews % 100 == 0) {
                    echo "   📊 Обработано {$migratedViews} просмотров...\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Ошибка просмотра: " . $e->getMessage() . "\n";
                $skippedViews++;
            }
        }
        
        echo "   ✅ Мигрировано просмотров: {$migratedViews}\n";
        echo "   ⏭️ Пропущено: {$skippedViews}\n\n";
    } else {
        echo "   ⚠️ Файл ad_views.json не найден\n\n";
    }

    // ШАГ 3: МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ
    echo "3. 🖱️ МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ\n";
    echo "=================================\n";

    $adClicksFile = 'database/ad_clicks.json';
    if (file_exists($adClicksFile)) {
        $adClicks = json_decode(file_get_contents($adClicksFile), true);
        echo "📁 Найдено " . count($adClicks) . " кликов\n";

        $migratedClicks = 0;
        $skippedClicks = 0;

        foreach ($adClicks as $click) {
            try {
                // Проверяем дубликаты
                $existing = $pdo->prepare("
                    SELECT id FROM ad_clicks
                    WHERE user_id = ? AND timestamp = ? AND click_type = ?
                ");
                $existing->execute([
                    $click['user_id'],
                    $click['timestamp'],
                    $click['click_type']
                ]);

                if ($existing->fetch()) {
                    $skippedClicks++;
                    continue;
                }

                $stmt = $pdo->prepare("
                    INSERT INTO ad_clicks (
                        user_id, ad_type, click_type, reason,
                        timestamp, ip_address, user_agent
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $click['user_id'],
                    $click['ad_type'] ?? 'unknown',
                    $click['click_type'],
                    $click['reason'] ?? null,
                    $click['timestamp'],
                    $click['ip'] ?? 'unknown',
                    substr($click['user_agent'] ?? 'unknown', 0, 500)
                ]);

                $migratedClicks++;

                if ($migratedClicks % 500 == 0) {
                    echo "   📊 Обработано {$migratedClicks} кликов...\n";
                }

            } catch (Exception $e) {
                echo "   ❌ Ошибка клика: " . $e->getMessage() . "\n";
                $skippedClicks++;
            }
        }

        echo "   ✅ Мигрировано кликов: {$migratedClicks}\n";
        echo "   ⏭️ Пропущено: {$skippedClicks}\n\n";
    } else {
        echo "   ⚠️ Файл ad_clicks.json не найден\n\n";
    }

    // ШАГ 4: МИГРАЦИЯ ВЫПЛАТ ИЗ USER_DATA
    echo "4. 💰 МИГРАЦИЯ ВЫПЛАТ ИЗ USER_DATA\n";
    echo "==================================\n";

    $migratedWithdrawals = 0;
    $skippedWithdrawals = 0;

    foreach ($userData as $telegramId => $user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }

        foreach ($user['withdrawals'] as $withdrawal) {
            try {
                // Проверяем, существует ли уже эта выплата
                $existing = $pdo->prepare("
                    SELECT id FROM user_withdrawals
                    WHERE user_id = ? AND amount = ? AND wallet_address = ?
                ");
                $existing->execute([
                    $telegramId,
                    $withdrawal['coins_amount'] ?? $withdrawal['amount'] ?? 0,
                    $withdrawal['wallet_address'] ?? $withdrawal['address'] ?? ''
                ]);

                if ($existing->fetch()) {
                    $skippedWithdrawals++;
                    continue;
                }

                // Конвертируем статус
                $status = 'pending';
                if (isset($withdrawal['status'])) {
                    switch (strtolower($withdrawal['status'])) {
                        case 'finished':
                        case 'completed':
                        case 'success':
                            $status = 'completed';
                            break;
                        case 'failed':
                        case 'error':
                            $status = 'failed';
                            break;
                        case 'processing':
                        case 'in_progress':
                            $status = 'processing';
                            break;
                        case 'cancelled':
                        case 'canceled':
                            $status = 'cancelled';
                            break;
                    }
                }

                $stmt = $pdo->prepare("
                    INSERT INTO user_withdrawals (
                        user_id, amount, currency, wallet_address, status,
                        transaction_hash, requested_at, processed_at, error_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $requestedAt = isset($withdrawal['timestamp'])
                    ? date('Y-m-d H:i:s', $withdrawal['timestamp'])
                    : ($withdrawal['created_at'] ?? date('Y-m-d H:i:s'));

                $processedAt = null;
                if (in_array($status, ['completed', 'failed']) && isset($withdrawal['updated_at'])) {
                    $processedAt = is_numeric($withdrawal['updated_at'])
                        ? date('Y-m-d H:i:s', $withdrawal['updated_at'])
                        : $withdrawal['updated_at'];
                }

                $stmt->execute([
                    $telegramId,
                    $withdrawal['coins_amount'] ?? $withdrawal['amount'] ?? 0,
                    strtoupper($withdrawal['currency'] ?? 'TON'),
                    $withdrawal['wallet_address'] ?? $withdrawal['address'] ?? '',
                    $status,
                    $withdrawal['hash'] ?? $withdrawal['transaction_hash'] ?? null,
                    $requestedAt,
                    $processedAt,
                    $withdrawal['error_message'] ?? ($status === 'failed' ? 'Migration error' : null)
                ]);

                $migratedWithdrawals++;

            } catch (Exception $e) {
                echo "   ❌ Ошибка выплаты для {$telegramId}: " . $e->getMessage() . "\n";
                $skippedWithdrawals++;
            }
        }
    }

    echo "   ✅ Мигрировано выплат: {$migratedWithdrawals}\n";
    echo "   ⏭️ Пропущено: {$skippedWithdrawals}\n\n";

    // ШАГ 5: МИГРАЦИЯ FRAUD LOG
    echo "5. 🚨 МИГРАЦИЯ FRAUD LOG\n";
    echo "========================\n";

    $fraudLogFile = 'database/fraud_log.json';
    if (file_exists($fraudLogFile)) {
        $fraudLogs = json_decode(file_get_contents($fraudLogFile), true);
        echo "📁 Найдено " . count($fraudLogs) . " записей fraud log\n";

        $migratedFraud = 0;
        foreach ($fraudLogs as $log) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO fraud_logs (
                        timestamp, user_id, violation_type, risk_level,
                        risk_score, action, details, fingerprint, ip_address
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $log['timestamp'],
                    $log['user_id'],
                    $log['violation_type'],
                    $log['risk_level'],
                    $log['risk_score'],
                    $log['action'],
                    $log['details'],
                    $log['fingerprint'] ?? null,
                    $log['ip_address'] ?? null
                ]);
                $migratedFraud++;
            } catch (Exception $e) {
                // Игнорируем дубликаты
            }
        }

        echo "   ✅ Мигрировано: {$migratedFraud}\n\n";
    }

    echo "🎉 МИГРАЦИЯ ЗАВЕРШЕНА!\n";
    echo "======================\n";

    // Финальная статистика
    $finalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $finalWithdrawals = $pdo->query("SELECT COUNT(*) FROM user_withdrawals")->fetchColumn();
    $finalTransactions = $pdo->query("SELECT COUNT(*) FROM coin_transactions")->fetchColumn();
    $finalAdClicks = $pdo->query("SELECT COUNT(*) FROM ad_clicks")->fetchColumn();
    $finalFraud = $pdo->query("SELECT COUNT(*) FROM fraud_logs")->fetchColumn();
    $totalBalance = $pdo->query("SELECT SUM(balance) FROM users")->fetchColumn();

    echo "📊 ИТОГОВАЯ СТАТИСТИКА:\n";
    echo "   👥 Пользователей: {$finalUsers}\n";
    echo "   💰 Выплат: {$finalWithdrawals}\n";
    echo "   💎 Транзакций (просмотры): {$finalTransactions}\n";
    echo "   🖱️ Кликов: {$finalAdClicks}\n";
    echo "   🚨 Fraud записей: {$finalFraud}\n";
    echo "   💵 Общий баланс: " . number_format($totalBalance, 2) . " монет\n\n";

    // Статистика выплат по статусам
    if ($finalWithdrawals > 0) {
        echo "📋 Статистика выплат по статусам:\n";
        $withdrawalStats = $pdo->query("
            SELECT status, COUNT(*) as count, SUM(amount) as total_amount
            FROM user_withdrawals
            GROUP BY status
            ORDER BY count DESC
        ")->fetchAll(PDO::FETCH_ASSOC);

        foreach ($withdrawalStats as $stat) {
            echo "   - {$stat['status']}: {$stat['count']} шт. ({$stat['total_amount']} TON)\n";
        }
        echo "\n";
    }

    echo "✅ Все данные с сервера успешно мигрированы в SQLite!\n";
    echo "🚀 Теперь можно тестировать систему с реальными данными.\n";

} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}
?>
