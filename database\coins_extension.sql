-- coins_extension.sql
-- Дополнительные таблицы для системы монет (добавляются к существующей схеме)

-- Добавляем недостающие поля в таблицу users
ALTER TABLE users ADD COLUMN reserved_balance DECIMAL(10,2) DEFAULT 0;

-- Таблица транзакций монет (детальная история всех операций)
CREATE TABLE IF NOT EXISTS coin_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    transaction_type TEXT NOT NULL, -- 'earn', 'withdraw', 'reserve', 'unreserve', 'bonus', 'penalty', 'refund'
    amount DECIMAL(10,2) NOT NULL,  -- Сумма операции (всегда положительная)
    operation TEXT NOT NULL,       -- 'credit' (начисление) или 'debit' (списание)
    balance_before DECIMAL(10,2) NOT NULL, -- Баланс до операции
    balance_after DECIMAL(10,2) NOT NULL,  -- Баланс после операции
    source_type TEXT,               -- Источник: 'ad_view', 'referral', 'withdrawal', 'admin', 'bonus'
    source_id INTEGER,              -- ID источника (например, ID просмотра рекламы)
    description TEXT,               -- Описание операции
    metadata TEXT,                  -- JSON с дополнительными данными
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT check_amount_positive CHECK (amount > 0),
    CONSTRAINT check_operation_valid CHECK (operation IN ('credit', 'debit')),
    CONSTRAINT check_transaction_type_valid CHECK (
        transaction_type IN ('earn', 'withdraw', 'reserve', 'unreserve', 'bonus', 'penalty', 'refund')
    ),
    
    FOREIGN KEY (user_id) REFERENCES users(telegram_id)
);

-- Индексы для таблицы транзакций
CREATE INDEX IF NOT EXISTS idx_coin_transactions_user_id ON coin_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_type ON coin_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_created_at ON coin_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_coin_transactions_source ON coin_transactions(source_type, source_id);

-- Обновляем таблицу user_withdrawals (добавляем недостающие поля)
ALTER TABLE user_withdrawals ADD COLUMN crypto_amount TEXT;     -- Сумма в криптовалюте
ALTER TABLE user_withdrawals ADD COLUMN network_fee TEXT;       -- Сетевая комиссия
ALTER TABLE user_withdrawals ADD COLUMN final_amount TEXT;      -- Итоговая сумма к получению
ALTER TABLE user_withdrawals ADD COLUMN admin_notes TEXT;       -- Заметки администратора
ALTER TABLE user_withdrawals ADD COLUMN completed_at DATETIME;  -- Время завершения

-- Таблица настроек системы монет
CREATE TABLE IF NOT EXISTS coin_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Вставляем базовые настройки для системы монет
INSERT OR IGNORE INTO coin_settings (setting_key, setting_value, description) VALUES
('coin_rate_usd', '0.001', 'Курс монеты к доллару США'),
('min_withdrawal_coins', '1000', 'Минимальная сумма для вывода в монетах'),
('ad_reward_native_banner', '10', 'Награда за просмотр нативного баннера'),
('ad_reward_rewarded_video', '1', 'Награда за просмотр видео'),
('ad_reward_interstitial', '10', 'Награда за просмотр интерстициала'),
('referral_bonus_percent', '10', 'Процент бонуса с рефералов'),
('withdrawal_fee_percent', '0', 'Процент комиссии за вывод'),
('daily_earn_limit', '200', 'Дневной лимит заработка монет');

-- Индекс для настроек
CREATE INDEX IF NOT EXISTS idx_coin_settings_key ON coin_settings(setting_key);

-- Таблица дневной статистики пользователей (для быстрого доступа к лимитам)
CREATE TABLE IF NOT EXISTS user_daily_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    date DATE NOT NULL,
    coins_earned_today INTEGER DEFAULT 0,
    ads_viewed_today INTEGER DEFAULT 0,
    referral_earnings_today DECIMAL(10,2) DEFAULT 0,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(telegram_id),
    UNIQUE(user_id, date)
);

-- Индексы для дневной статистики
CREATE INDEX IF NOT EXISTS idx_user_daily_stats_user_date ON user_daily_stats(user_id, date);
CREATE INDEX IF NOT EXISTS idx_user_daily_stats_date ON user_daily_stats(date);

-- Триггер для автоматического обновления updated_at в users
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Триггер для автоматического обновления дневной статистики при начислении монет
CREATE TRIGGER IF NOT EXISTS update_daily_stats_on_earn
    AFTER INSERT ON coin_transactions
    FOR EACH ROW
    WHEN NEW.operation = 'credit' AND NEW.transaction_type = 'earn'
BEGIN
    INSERT OR REPLACE INTO user_daily_stats (
        user_id, 
        date,
        coins_earned_today,
        ads_viewed_today,
        referral_earnings_today,
        last_activity
    ) VALUES (
        NEW.user_id,
        date('now'),
        COALESCE((SELECT coins_earned_today FROM user_daily_stats WHERE user_id = NEW.user_id AND date = date('now')), 0) + NEW.amount,
        COALESCE((SELECT ads_viewed_today FROM user_daily_stats WHERE user_id = NEW.user_id AND date = date('now')), 0) + 
            CASE WHEN NEW.source_type = 'ad_view' THEN 1 ELSE 0 END,
        COALESCE((SELECT referral_earnings_today FROM user_daily_stats WHERE user_id = NEW.user_id AND date = date('now')), 0) + 
            CASE WHEN NEW.source_type = 'referral' THEN NEW.amount ELSE 0 END,
        CURRENT_TIMESTAMP
    );
END;

-- Триггер для обновления баланса пользователя при транзакциях
CREATE TRIGGER IF NOT EXISTS update_user_balance_on_transaction
    AFTER INSERT ON coin_transactions
    FOR EACH ROW
BEGIN
    UPDATE users 
    SET 
        balance = NEW.balance_after,
        total_earned = CASE 
            WHEN NEW.operation = 'credit' AND NEW.transaction_type = 'earn' 
            THEN total_earned + NEW.amount 
            ELSE total_earned 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE telegram_id = NEW.user_id;
END;

-- Функция очистки старой статистики (можно вызывать периодически)
-- DELETE FROM user_daily_stats WHERE date < date('now', '-30 days');

-- Представление для быстрого доступа к балансам пользователей
CREATE VIEW IF NOT EXISTS user_balance_view AS
SELECT 
    u.telegram_id,
    u.first_name,
    u.last_name,
    u.username,
    u.balance,
    COALESCE(u.reserved_balance, 0) as reserved_balance,
    (u.balance - COALESCE(u.reserved_balance, 0)) as available_balance,
    u.total_earned,
    u.referral_earnings,
    u.withdrawals_count,
    (SELECT COUNT(*) FROM coin_transactions ct WHERE ct.user_id = u.telegram_id AND ct.transaction_type = 'earn' AND date(ct.created_at) = date('now')) as earnings_today,
    (SELECT COALESCE(SUM(amount), 0) FROM user_withdrawals uw WHERE uw.user_id = u.telegram_id AND uw.status = 'completed') as total_withdrawn,
    u.created_at,
    u.updated_at
FROM users u;

-- Представление для статистики транзакций
CREATE VIEW IF NOT EXISTS transaction_stats_view AS
SELECT 
    transaction_type,
    operation,
    source_type,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount,
    date(created_at) as transaction_date
FROM coin_transactions 
GROUP BY transaction_type, operation, source_type, date(created_at)
ORDER BY transaction_date DESC;
