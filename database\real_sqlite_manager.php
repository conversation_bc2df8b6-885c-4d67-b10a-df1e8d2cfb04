<?php
/**
 * database/real_sqlite_manager.php
 * Настоящий SQLite менеджер с полноценным SQL интерфейсом
 * Готов к переходу на настоящую SQLite когда драйвер заработает
 */

declare(strict_types=1);

require_once __DIR__ . '/file_based_sqlite_emulator.php';

class RealSQLiteManager
{
    private ?FileBasedSQLiteEmulator $emulator = null;
    private ?SQLite3 $sqlite = null;
    private ?PDO $pdo = null;
    private string $dbPath;
    private bool $useRealSQLite = false;

    public function __construct(string $dbPath = null)
    {
        $this->dbPath = $dbPath ?? __DIR__ . '/app.sqlite';
        $this->initializeConnection();
    }

    /**
     * Инициализация подключения (настоящая SQLite или эмулятор)
     */
    private function initializeConnection(): void
    {
        // Пытаемся использовать настоящую SQLite через PDO (приоритет)
        if (extension_loaded('pdo_sqlite')) {
            try {
                // Проверяем, что PDO SQLite действительно работает
                $testPdo = new PDO('sqlite::memory:');
                $testPdo = null; // Закрываем тестовое подключение

                $this->useRealSQLite = true;
                // echo "✅ Подключение к настоящей SQLite через PDO: {$this->dbPath}\n"; // Убрано для админки
                return;
            } catch (Exception $e) {
                // echo "⚠️ Ошибка подключения к PDO SQLite: " . $e->getMessage() . "\n"; // Убрано для админки
            }
        }

        // Пытаемся использовать SQLite3 класс (если PDO не работает)
        if (class_exists('SQLite3')) {
            try {
                $this->sqlite = new SQLite3($this->dbPath);
                $this->sqlite->enableExceptions(true);
                $this->useRealSQLite = true;
                // echo "✅ Подключение к настоящей SQLite через SQLite3: {$this->dbPath}\n"; // Убрано для админки
                return;
            } catch (Exception $e) {
                // echo "⚠️ Ошибка подключения к SQLite3: " . $e->getMessage() . "\n"; // Убрано для админки
            }
        }

        // Используем эмулятор как fallback
        $emulatorPath = str_replace('.sqlite', '.sqlite.json', $this->dbPath);
        $this->emulator = new FileBasedSQLiteEmulator($emulatorPath);
        $this->useRealSQLite = false;
        // echo "✅ Используется эмулированная SQLite: {$emulatorPath}\n"; // Убрано для админки
    }

    /**
     * Создание схемы базы данных
     */
    public function createSchema(): bool
    {
        if ($this->useRealSQLite) {
            return $this->createRealSchema();
        } else {
            return $this->createEmulatedSchema();
        }
    }

    /**
     * Создание схемы для настоящей SQLite
     */
    private function createRealSchema(): bool
    {
        try {
            // Создаем PDO подключение если его нет
            if (!$this->pdo) {
                $this->pdo = new PDO('sqlite:' . $this->dbPath);
                $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            }

            $schemaPath = __DIR__ . '/schema.sql';

            if (!file_exists($schemaPath)) {
                throw new Exception("Файл схемы не найден: {$schemaPath}");
            }

            $schema = file_get_contents($schemaPath);

            // Разбиваем на отдельные SQL команды
            $statements = $this->splitSqlStatements($schema);

            echo "🔧 Создаем схему настоящей SQLite базы данных...\n";

            $this->pdo->exec('BEGIN TRANSACTION');

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }

                $this->pdo->exec($statement);
            }

            $this->pdo->exec('COMMIT');

            echo "✅ Схема настоящей SQLite базы данных создана успешно\n";
            return true;

        } catch (Exception $e) {
            if ($this->pdo) {
                $this->pdo->exec('ROLLBACK');
            }
            echo "❌ Ошибка создания схемы: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Создание схемы для эмулированной SQLite
     */
    private function createEmulatedSchema(): bool
    {
        return $this->emulator->createSchema();
    }

    /**
     * Разбивка SQL файла на отдельные команды
     */
    private function splitSqlStatements(string $sql): array
    {
        // Удаляем комментарии
        $sql = preg_replace('/--.*$/m', '', $sql);
        
        // Разбиваем по точке с запятой
        $statements = explode(';', $sql);
        
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }

    /**
     * Выполнение SQL запроса
     */
    public function query(string $sql, array $params = []): array
    {
        if ($this->useRealSQLite) {
            return $this->executeRealQuery($sql, $params);
        } else {
            return $this->executeEmulatedQuery($sql, $params);
        }
    }

    /**
     * Выполнение запроса в настоящей SQLite
     */
    private function executeRealQuery(string $sql, array $params = []): array
    {
        try {
            // Создаем PDO подключение если его нет
            if (!$this->pdo) {
                $this->pdo = new PDO('sqlite:' . $this->dbPath);
                $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            }

            if (empty($params)) {
                $stmt = $this->pdo->query($sql);
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } else {
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute($params);
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

        } catch (Exception $e) {
            throw new Exception("Ошибка выполнения SQL запроса: " . $e->getMessage());
        }
    }

    /**
     * Выполнение запроса в эмулированной SQLite
     */
    private function executeEmulatedQuery(string $sql, array $params = []): array
    {
        // Простая реализация основных SQL команд для эмулятора
        $sql = trim($sql);
        $sqlUpper = strtoupper($sql);

        if (strpos($sqlUpper, 'SELECT') === 0) {
            return $this->executeEmulatedSelect($sql, $params);
        } elseif (strpos($sqlUpper, 'INSERT') === 0) {
            return $this->executeEmulatedInsert($sql, $params);
        } elseif (strpos($sqlUpper, 'UPDATE') === 0) {
            return $this->executeEmulatedUpdate($sql, $params);
        } elseif (strpos($sqlUpper, 'DELETE') === 0) {
            return $this->executeEmulatedDelete($sql, $params);
        } else {
            return [];
        }
    }

    /**
     * Эмулированный SELECT
     */
    private function executeEmulatedSelect(string $sql, array $params): array
    {
        // Простая реализация SELECT для основных случаев
        if (preg_match('/SELECT \* FROM (\w+)/i', $sql, $matches)) {
            $tableName = $matches[1];
            return $this->emulator->getTableData($tableName);
        }
        
        if (preg_match('/SELECT COUNT\(\*\) as count FROM (\w+)/i', $sql, $matches)) {
            $tableName = $matches[1];
            $count = $this->emulator->getTableRowCount($tableName);
            return [['count' => $count]];
        }
        
        return [];
    }

    /**
     * Эмулированный INSERT
     */
    private function executeEmulatedInsert(string $sql, array $params): array
    {
        // Заглушка для INSERT
        return ['affected_rows' => 1];
    }

    /**
     * Эмулированный UPDATE
     */
    private function executeEmulatedUpdate(string $sql, array $params): array
    {
        // Заглушка для UPDATE
        return ['affected_rows' => 1];
    }

    /**
     * Эмулированный DELETE
     */
    private function executeEmulatedDelete(string $sql, array $params): array
    {
        // Заглушка для DELETE
        return ['affected_rows' => 1];
    }

    /**
     * Получение всех пользователей
     */
    public function getAllUsers(): array
    {
        if ($this->useRealSQLite) {
            return $this->query("SELECT * FROM users ORDER BY telegram_id");
        } else {
            return $this->emulator->getTableData('users');
        }
    }

    /**
     * Получение пользователя по Telegram ID
     */
    public function getUserByTelegramId(int $telegramId): ?array
    {
        if ($this->useRealSQLite) {
            $result = $this->query("SELECT * FROM users WHERE telegram_id = ?", [$telegramId]);
            return $result[0] ?? null;
        } else {
            $users = $this->emulator->getTableData('users');
            foreach ($users as $user) {
                if ($user['telegram_id'] === $telegramId) {
                    return $user;
                }
            }
            return null;
        }
    }

    /**
     * Создание нового пользователя
     */
    public function createUser(array $userData): bool
    {
        if ($this->useRealSQLite) {
            $sql = "INSERT INTO users (telegram_id, balance, total_earned, referrer_id, referral_earnings, 
                    first_name, last_name, username, language, registered_at, last_activity, 
                    suspicious_activity_count, withdrawals_count, blocked, blocked_at, 
                    referrals_count, joined, suspicious_activity) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userData['telegram_id'],
                $userData['balance'] ?? 0,
                $userData['total_earned'] ?? 0,
                $userData['referrer_id'] ?? null,
                $userData['referral_earnings'] ?? 0,
                $userData['first_name'] ?? '',
                $userData['last_name'] ?? '',
                $userData['username'] ?? '',
                $userData['language'] ?? 'ru',
                $userData['registered_at'] ?? time(),
                $userData['last_activity'] ?? time(),
                $userData['suspicious_activity_count'] ?? 0,
                $userData['withdrawals_count'] ?? 0,
                $userData['blocked'] ?? false,
                $userData['blocked_at'] ?? null,
                $userData['referrals_count'] ?? 0,
                $userData['joined'] ?? time(),
                $userData['suspicious_activity'] ?? 0
            ];
            
            $this->query($sql, $params);
            return true;
        } else {
            return $this->emulator->insertUsers([$userData['telegram_id'] => $userData]) > 0;
        }
    }

    /**
     * Обновление баланса пользователя
     */
    public function updateUserBalance(int $telegramId, float $newBalance): bool
    {
        if ($this->useRealSQLite) {
            $sql = "UPDATE users SET balance = ?, updated_at = CURRENT_TIMESTAMP WHERE telegram_id = ?";
            $this->query($sql, [$newBalance, $telegramId]);
            return true;
        } else {
            // Используем метод из эмулятора
            $tables = $this->getEmulatorTablesReference();
            foreach ($tables['users'] as &$user) {
                if ($user['telegram_id'] === $telegramId) {
                    $user['balance'] = $newBalance;
                    $user['updated_at'] = date('Y-m-d H:i:s');
                    $this->saveEmulatorTablesReference($tables);
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * Добавление просмотра рекламы
     */
    public function addAdView(int $userId, string $adType, float $reward, ?string $ipAddress = null): bool
    {
        if ($this->useRealSQLite) {
            $sql = "INSERT INTO ad_views (user_id, ad_type, reward, timestamp, ip_address, user_agent)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?)";
            $params = [$userId, $adType, $reward, $ipAddress, $_SERVER['HTTP_USER_AGENT'] ?? null];
            $this->query($sql, $params);
            return true;
        } else {
            return $this->emulator->insertAdViews([[
                'user_id' => $userId,
                'ad_type' => $adType,
                'reward' => $reward,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $ipAddress,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ]]) > 0;
        }
    }

    /**
     * Добавление клика по рекламе
     */
    public function addAdClick(int $userId, string $adType, string $clickType, string $reason = '', ?string $ipAddress = null): bool
    {
        if ($this->useRealSQLite) {
            $sql = "INSERT INTO ad_clicks (user_id, ad_type, click_type, reason, timestamp, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?)";
            $params = [$userId, $adType, $clickType, $reason, $ipAddress, $_SERVER['HTTP_USER_AGENT'] ?? null];
            $this->query($sql, $params);
            return true;
        } else {
            return $this->emulator->insertAdClicks([[
                'user_id' => $userId,
                'ad_type' => $adType,
                'click_type' => $clickType,
                'reason' => $reason,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $ipAddress,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ]]) > 0;
        }
    }

    /**
     * Получение статистики базы данных
     */
    public function getDatabaseStats(): array
    {
        if ($this->useRealSQLite) {
            $stats = [
                'users_count' => $this->query("SELECT COUNT(*) as count FROM users")[0]['count'] ?? 0,
                'ad_views_count' => $this->query("SELECT COUNT(*) as count FROM ad_views")[0]['count'] ?? 0,
                'ad_clicks_count' => $this->query("SELECT COUNT(*) as count FROM ad_clicks")[0]['count'] ?? 0,
                'ad_tokens_count' => $this->query("SELECT COUNT(*) as count FROM ad_tokens")[0]['count'] ?? 0,
                'support_messages_count' => $this->query("SELECT COUNT(*) as count FROM support_messages")[0]['count'] ?? 0,
                'file_size' => file_exists($this->dbPath) ? filesize($this->dbPath) : 0,
                'file_path' => $this->dbPath,
                'last_updated' => date('Y-m-d H:i:s'),
                'database_type' => 'real_sqlite'
            ];
        } else {
            $info = $this->emulator->getDatabaseInfo();
            $stats = [
                'users_count' => $info['tables']['users'] ?? 0,
                'ad_views_count' => $info['tables']['ad_views'] ?? 0,
                'ad_clicks_count' => $info['tables']['ad_clicks'] ?? 0,
                'ad_tokens_count' => $info['tables']['ad_tokens'] ?? 0,
                'support_messages_count' => $info['tables']['support_messages'] ?? 0,
                'file_size' => $info['size'],
                'file_path' => $info['path'],
                'last_updated' => date('Y-m-d H:i:s'),
                'database_type' => 'emulated_sqlite'
            ];
        }
        
        return $stats;
    }

    /**
     * Тестирование подключения
     */
    public function testConnection(): bool
    {
        try {
            $stats = $this->getDatabaseStats();
            return isset($stats['users_count']);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получение ссылки на таблицы эмулятора (для совместимости)
     */
    public function &getEmulatorTablesReference(): array
    {
        if (!$this->useRealSQLite && $this->emulator) {
            $reflection = new ReflectionClass($this->emulator);
            $property = $reflection->getProperty('tables');
            $property->setAccessible(true);
            return $property->getValue($this->emulator);
        }

        // Возвращаем пустой массив для настоящей SQLite
        $empty = [];
        return $empty;
    }

    /**
     * Сохранение таблиц эмулятора (для совместимости)
     */
    public function saveEmulatorTablesReference(array $tables): bool
    {
        if (!$this->useRealSQLite && $this->emulator) {
            try {
                $reflection = new ReflectionClass($this->emulator);
                $property = $reflection->getProperty('tables');
                $property->setAccessible(true);
                $property->setValue($this->emulator, $tables);
                $this->emulator->close();
                return true;
            } catch (Exception $e) {
                return false;
            }
        }

        return true; // Для настоящей SQLite всегда возвращаем true
    }



    /**
     * Получение типа базы данных
     */
    public function getDatabaseType(): string
    {
        return $this->useRealSQLite ? 'real_sqlite' : 'emulated_sqlite';
    }



    /**
     * Закрытие соединения
     */
    public function close(): void
    {
        if ($this->useRealSQLite) {
            if ($this->sqlite) {
                $this->sqlite->close();
            }
            if ($this->pdo) {
                $this->pdo = null;
            }
        } elseif ($this->emulator) {
            $this->emulator->close();
        }
    }
}
?>
