<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// Подключаем SQLite логгер
require_once __DIR__ . '/db_mock_final_sqlite.php';

$input = json_decode(file_get_contents('php://input'), true);

if (empty($input) || !isset($input['user_id']) || !isset($input['ad_type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid input']);
    exit;
}

// Логируем клик в SQLite
$success = logAdClick(
    intval($input['user_id']),
    $input['ad_type'],
    $input['click_type'] ?? 'button_click',
    $input['reason'] ?? ''
);

if ($success) {
    echo json_encode(['success' => true]);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to log click']);
}
