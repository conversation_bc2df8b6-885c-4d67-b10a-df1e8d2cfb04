<?php
/**
 * api/get_transaction_stats.php
 * API для получения детальной статистики транзакций
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $userId = null;
    $period = 'month'; // day, week, month, year, all
    
    // Поддерживаем как GET, так и POST запросы
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Если есть initData, парсим его
        if (isset($input['initData'])) {
            require_once __DIR__ . '/../api/telegram_auth.php';
            $telegramAuth = new TelegramAuth();
            $userData = $telegramAuth->validateInitData($input['initData']);
            
            if (!$userData || !isset($userData['user']['id'])) {
                throw new Exception('Invalid Telegram data');
            }
            
            $userId = (string)$userData['user']['id'];
        } elseif (isset($input['user_id'])) {
            $userId = (string)$input['user_id'];
        }
        
        $period = $input['period'] ?? 'month';
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $period = $_GET['period'] ?? 'month';
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Валидация периода
    $validPeriods = ['day', 'week', 'month', 'year', 'all'];
    if (!in_array($period, $validPeriods)) {
        $period = 'month';
    }
    
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    // Определяем условие для периода
    $periodCondition = '';
    $periodLabel = '';
    
    switch ($period) {
        case 'day':
            $periodCondition = "AND date(created_at) = date('now')";
            $periodLabel = 'Сегодня';
            break;
        case 'week':
            $periodCondition = "AND date(created_at) >= date('now', '-7 days')";
            $periodLabel = 'Последние 7 дней';
            break;
        case 'month':
            $periodCondition = "AND date(created_at) >= date('now', '-30 days')";
            $periodLabel = 'Последние 30 дней';
            break;
        case 'year':
            $periodCondition = "AND date(created_at) >= date('now', '-365 days')";
            $periodLabel = 'Последний год';
            break;
        case 'all':
            $periodCondition = '';
            $periodLabel = 'За все время';
            break;
    }
    
    // 1. Общая статистика
    $generalStatsQuery = "
        SELECT 
            COUNT(*) as total_transactions,
            SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as total_earned,
            SUM(CASE WHEN operation = 'debit' THEN amount ELSE 0 END) as total_spent,
            COUNT(CASE WHEN operation = 'credit' THEN 1 END) as credit_count,
            COUNT(CASE WHEN operation = 'debit' THEN 1 END) as debit_count,
            AVG(CASE WHEN operation = 'credit' THEN amount END) as avg_credit,
            AVG(CASE WHEN operation = 'debit' THEN amount END) as avg_debit,
            MIN(created_at) as first_transaction,
            MAX(created_at) as last_transaction
        FROM coin_transactions 
        WHERE user_id = ? {$periodCondition}
    ";
    
    $generalStats = $db->query($generalStatsQuery, [$userId])[0];
    
    // 2. Статистика по типам транзакций
    $typeStatsQuery = "
        SELECT 
            transaction_type,
            operation,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount,
            MIN(amount) as min_amount,
            MAX(amount) as max_amount
        FROM coin_transactions 
        WHERE user_id = ? {$periodCondition}
        GROUP BY transaction_type, operation
        ORDER BY total_amount DESC
    ";
    
    $typeStats = $db->query($typeStatsQuery, [$userId]);
    
    // 3. Статистика по источникам
    $sourceStatsQuery = "
        SELECT 
            source_type,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        FROM coin_transactions 
        WHERE user_id = ? AND source_type IS NOT NULL {$periodCondition}
        GROUP BY source_type
        ORDER BY total_amount DESC
    ";
    
    $sourceStats = $db->query($sourceStatsQuery, [$userId]);
    
    // 4. Дневная статистика (для графиков)
    $dailyStatsQuery = "
        SELECT 
            date(created_at) as date,
            COUNT(*) as transactions_count,
            SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as earned,
            SUM(CASE WHEN operation = 'debit' THEN amount ELSE 0 END) as spent
        FROM coin_transactions 
        WHERE user_id = ? {$periodCondition}
        GROUP BY date(created_at)
        ORDER BY date(created_at) ASC
    ";
    
    $dailyStats = $db->query($dailyStatsQuery, [$userId]);
    
    // 5. Почасовая статистика (для текущего дня)
    $hourlyStatsQuery = "
        SELECT 
            strftime('%H', created_at) as hour,
            COUNT(*) as transactions_count,
            SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as earned
        FROM coin_transactions 
        WHERE user_id = ? AND date(created_at) = date('now')
        GROUP BY strftime('%H', created_at)
        ORDER BY hour ASC
    ";
    
    $hourlyStats = $db->query($hourlyStatsQuery, [$userId]);
    
    // 6. Топ дней по заработку
    $topDaysQuery = "
        SELECT 
            date(created_at) as date,
            SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as earned,
            COUNT(*) as transactions_count
        FROM coin_transactions 
        WHERE user_id = ? AND operation = 'credit' {$periodCondition}
        GROUP BY date(created_at)
        ORDER BY earned DESC
        LIMIT 10
    ";
    
    $topDays = $db->query($topDaysQuery, [$userId]);
    
    // Форматируем результаты
    $formattedGeneralStats = [
        'total_transactions' => (int)$generalStats['total_transactions'],
        'total_earned' => (float)$generalStats['total_earned'],
        'total_spent' => (float)$generalStats['total_spent'],
        'net_balance' => (float)$generalStats['total_earned'] - (float)$generalStats['total_spent'],
        'credit_count' => (int)$generalStats['credit_count'],
        'debit_count' => (int)$generalStats['debit_count'],
        'avg_credit' => $generalStats['avg_credit'] ? round((float)$generalStats['avg_credit'], 2) : 0,
        'avg_debit' => $generalStats['avg_debit'] ? round((float)$generalStats['avg_debit'], 2) : 0,
        'first_transaction' => $generalStats['first_transaction'],
        'last_transaction' => $generalStats['last_transaction']
    ];
    
    $formattedTypeStats = [];
    foreach ($typeStats as $stat) {
        $formattedTypeStats[] = [
            'type' => $stat['transaction_type'],
            'operation' => $stat['operation'],
            'count' => (int)$stat['count'],
            'total_amount' => (float)$stat['total_amount'],
            'avg_amount' => round((float)$stat['avg_amount'], 2),
            'min_amount' => (float)$stat['min_amount'],
            'max_amount' => (float)$stat['max_amount']
        ];
    }
    
    $formattedSourceStats = [];
    foreach ($sourceStats as $stat) {
        $formattedSourceStats[] = [
            'source' => $stat['source_type'],
            'count' => (int)$stat['count'],
            'total_amount' => (float)$stat['total_amount'],
            'avg_amount' => round((float)$stat['avg_amount'], 2)
        ];
    }
    
    $formattedDailyStats = [];
    foreach ($dailyStats as $stat) {
        $formattedDailyStats[] = [
            'date' => $stat['date'],
            'formatted_date' => date('d.m.Y', strtotime($stat['date'])),
            'transactions_count' => (int)$stat['transactions_count'],
            'earned' => (float)$stat['earned'],
            'spent' => (float)$stat['spent'],
            'net' => (float)$stat['earned'] - (float)$stat['spent']
        ];
    }
    
    $formattedHourlyStats = [];
    for ($hour = 0; $hour < 24; $hour++) {
        $hourData = array_filter($hourlyStats, function($stat) use ($hour) {
            return (int)$stat['hour'] === $hour;
        });
        
        if (!empty($hourData)) {
            $hourData = array_values($hourData)[0];
            $formattedHourlyStats[] = [
                'hour' => $hour,
                'hour_formatted' => sprintf('%02d:00', $hour),
                'transactions_count' => (int)$hourData['transactions_count'],
                'earned' => (float)$hourData['earned']
            ];
        } else {
            $formattedHourlyStats[] = [
                'hour' => $hour,
                'hour_formatted' => sprintf('%02d:00', $hour),
                'transactions_count' => 0,
                'earned' => 0
            ];
        }
    }
    
    $formattedTopDays = [];
    foreach ($topDays as $day) {
        $formattedTopDays[] = [
            'date' => $day['date'],
            'formatted_date' => date('d.m.Y', strtotime($day['date'])),
            'earned' => (float)$day['earned'],
            'transactions_count' => (int)$day['transactions_count']
        ];
    }
    
    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'period' => $period,
        'period_label' => $periodLabel,
        'statistics' => [
            'general' => $formattedGeneralStats,
            'by_type' => $formattedTypeStats,
            'by_source' => $formattedSourceStats,
            'daily' => $formattedDailyStats,
            'hourly' => $formattedHourlyStats,
            'top_days' => $formattedTopDays
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'GET_STATS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
