<?php
/**
 * add_missing_translations.php
 * Добавление отсутствующих переводов
 */

declare(strict_types=1);

echo "➕ ДОБАВЛЕНИЕ ОТСУТСТВУЮЩИХ ПЕРЕВОДОВ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    // Переводы для добавления
    $translations = [
        'currency.coins' => ['ru' => 'монет', 'en' => 'coins'],
        'earnings.amount_to_withdraw' => ['ru' => 'Сумма для вывода:', 'en' => 'Amount to withdraw:'],
        'earnings.minimum_withdrawal' => ['ru' => 'Минимум к выводу:', 'en' => 'Minimum withdrawal:'],
        'earnings.withdrawal_amount' => ['ru' => 'Сумма к выводу:', 'en' => 'Withdrawal amount:'],
        'earnings.network_fee' => ['ru' => 'Сетевая комиссия:', 'en' => 'Network fee:'],
        'earnings.final_amount' => ['ru' => 'Вы получите:', 'en' => 'You will receive:'],
        'earnings.efficiency' => ['ru' => 'Эффективность:', 'en' => 'Efficiency:'],
        'earnings.amount_missing' => ['ru' => 'Не хватает:', 'en' => 'Missing:'],
        'earnings.enter_amount_for_calculation' => ['ru' => 'Введите сумму для расчета', 'en' => 'Enter amount for calculation'],
        'earnings.withdrawal_recommendations' => ['ru' => 'Рекомендации к выводу', 'en' => 'Withdrawal recommendations'],
        'earnings.selected_crypto' => ['ru' => 'Выбранная криптовалюта:', 'en' => 'Selected cryptocurrency:'],
        'earnings.amount_to_withdraw_coins' => ['ru' => 'Сумма для вывода (монеты):', 'en' => 'Amount to withdraw (coins):'],
        'earnings.amount_to_receive' => ['ru' => 'Сумма к получению:', 'en' => 'Amount to receive:'],
        'earnings.wallet_address' => ['ru' => 'Адрес кошелька:', 'en' => 'Wallet address:'],
        'earnings.wallet_warning' => ['ru' => 'Важно: Убедитесь, что адрес кошелька указан корректно.', 'en' => 'Important: Make sure the wallet address is correct.'],
        'earnings.withdrawal_history_title' => ['ru' => 'История выплат', 'en' => 'Withdrawal history'],
        'earnings.withdrawal_history_description' => ['ru' => 'Здесь отображается история всех ваших выплат и их статусы.', 'en' => 'Here is the history of all your withdrawals and their statuses.'],
        'earnings.refresh_history' => ['ru' => 'Обновить историю', 'en' => 'Refresh history'],
        'friends.share_title' => ['ru' => 'Поделиться', 'en' => 'Share'],
        'friends.share_description' => ['ru' => 'Расскажите друзьям об этом приложении!', 'en' => 'Tell your friends about this app!'],
        'friends.referral_link_title' => ['ru' => 'Реферальная ссылка', 'en' => 'Referral link'],
        'friends.referral_link_description' => ['ru' => 'Поделитесь своей уникальной ссылкой и получайте 10% от заработка каждого приглашенного друга!', 'en' => 'Share your unique link and get 10% of the earnings of each invited friend!'],
        'friends.total_referrals' => ['ru' => 'Всего рефералов:', 'en' => 'Total referrals:'],
        'friends.earned_from_referrals' => ['ru' => 'Заработано на рефералах:', 'en' => 'Earned from referrals:'],
        'friends.no_referrals' => ['ru' => 'У вас пока нет рефералов', 'en' => 'You have no referrals yet'],
        'friends.invite_friends_hint' => ['ru' => 'Пригласите друзей и начните зарабатывать!', 'en' => 'Invite friends and start earning!'],
        'friends.my_referrer_title' => ['ru' => 'Мой реферер', 'en' => 'My referrer'],
        'friends.referrer_description' => ['ru' => 'Информация о пользователе, который пригласил вас в приложение.', 'en' => 'Information about the user who invited you to the app.'],
        'buttons.refresh_stats' => ['ru' => 'Обновить статистику', 'en' => 'Refresh stats']
    ];
    
    echo "📝 Добавляем переводы...\n";
    
    $added = 0;
    foreach ($translations as $key => $langs) {
        foreach ($langs as $lang => $text) {
            // Проверяем, есть ли уже такой ключ
            $existing = $sqlite->query("SELECT id FROM bot_texts WHERE text_key = ? AND language_code = ?", [$key, $lang]);
            
            if (empty($existing)) {
                // Определяем категорию
                $category = explode('.', $key)[0];
                
                // Добавляем перевод
                $sqlite->query("
                    INSERT INTO bot_texts (text_key, text_value, language_code, category)
                    VALUES (?, ?, ?, ?)
                ", [$key, $text, $lang, $category]);
                
                echo "✅ [{$lang}] {$key}\n";
                $added++;
            } else {
                echo "⏭️ [{$lang}] {$key} (уже существует)\n";
            }
        }
    }
    
    echo "\n📊 РЕЗУЛЬТАТ:\n";
    echo "✅ Добавлено переводов: {$added}\n";
    
    // Проверяем общее количество
    $total = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts')[0]['count'];
    echo "📝 Всего переводов в базе: {$total}\n";
    
    echo "\n🌐 Тестируем API...\n";
    $response = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=ru');
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['app'])) {
            echo "✅ API работает\n";
            echo "📊 Категорий: " . count($data['app']) . "\n";
        }
    }
    
    echo "\n✅ ГОТОВО!\n";
    echo "🔄 Обновите страницу в браузере\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
