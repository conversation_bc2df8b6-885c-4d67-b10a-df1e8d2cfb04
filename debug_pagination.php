<?php
/**
 * debug_pagination.php
 * Отладка пагинации выводов
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ОТЛАДКА ПАГИНАЦИИ ВЫВОДОВ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Симулируем параметры как в withdrawals.php
    $page = 1;
    $per_page = 15;
    
    echo "📊 ПАРАМЕТРЫ ПАГИНАЦИИ:\n";
    echo "  Страница: {$page}\n";
    echo "  На странице: {$per_page}\n\n";
    
    // Загружаем данные
    $sql = "SELECT w.*, u.first_name, u.last_name, u.username 
            FROM user_withdrawals w 
            LEFT JOIN users u ON w.user_id = u.telegram_id 
            ORDER BY w.requested_at DESC";
    
    $allWithdrawals = $sqlite->query($sql);
    
    // Преобразуем данные
    foreach ($allWithdrawals as &$withdrawal) {
        $withdrawal['user_name'] = $withdrawal['first_name'] ?? 'Неизвестно';
        $withdrawal['user_lastname'] = $withdrawal['last_name'] ?? '';
        $withdrawal['timestamp'] = strtotime($withdrawal['requested_at']);
        $withdrawal['coins_amount'] = $withdrawal['amount'];
        $withdrawal['crypto_address'] = $withdrawal['wallet_address'];
        $withdrawal['payout_id'] = $withdrawal['transaction_hash'];
    }
    
    // Применяем фильтрацию (без фильтров = все данные)
    $filteredWithdrawals = $allWithdrawals;
    
    echo "📊 ДАННЫЕ:\n";
    echo "  Всего выводов: " . count($allWithdrawals) . "\n";
    echo "  После фильтрации: " . count($filteredWithdrawals) . "\n\n";
    
    // Пагинация
    $total_withdrawals = count($filteredWithdrawals);
    $total_pages = ceil($total_withdrawals / $per_page);
    $offset = ($page - 1) * $per_page;
    $withdrawals_page = array_slice($filteredWithdrawals, $offset, $per_page);
    
    echo "📄 ПАГИНАЦИЯ:\n";
    echo "  Всего записей: {$total_withdrawals}\n";
    echo "  Всего страниц: {$total_pages}\n";
    echo "  Смещение: {$offset}\n";
    echo "  На текущей странице: " . count($withdrawals_page) . "\n\n";
    
    if (!empty($withdrawals_page)) {
        echo "📋 ДАННЫЕ НА ТЕКУЩЕЙ СТРАНИЦЕ:\n";
        foreach ($withdrawals_page as $i => $w) {
            echo "  " . ($i + 1) . ". User: {$w['user_name']} ({$w['user_id']})\n";
            echo "     Amount: {$w['amount']} {$w['currency']}\n";
            echo "     Status: {$w['status']}\n";
            echo "     Date: {$w['requested_at']}\n";
            echo "     ---\n";
        }
    } else {
        echo "⚠️ НЕТ ДАННЫХ НА ТЕКУЩЕЙ СТРАНИЦЕ!\n";
        echo "Возможные причины:\n";
        echo "  - Неправильное смещение: {$offset}\n";
        echo "  - Пустой массив после фильтрации\n";
        echo "  - Ошибка в array_slice\n";
    }
    
    // Проверяем первые элементы исходного массива
    echo "\n🔍 ПРОВЕРКА ИСХОДНЫХ ДАННЫХ:\n";
    if (!empty($filteredWithdrawals)) {
        echo "Первый элемент массива:\n";
        $first = $filteredWithdrawals[0];
        echo "  ID: {$first['id']}\n";
        echo "  User: {$first['user_name']}\n";
        echo "  Amount: {$first['amount']}\n";
        echo "  Status: {$first['status']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ОТЛАДКА ЗАВЕРШЕНА!\n";
?>
