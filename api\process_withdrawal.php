<?php
/**
 * api/process_withdrawal.php
 * API для обработки и списания баланса при выводе средств
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    $requiredFields = ['withdrawal_id', 'action'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $withdrawalId = (int)$input['withdrawal_id'];
    $action = strtolower(trim($input['action'])); // complete, cancel, fail
    $transactionHash = $input['transaction_hash'] ?? null;
    $adminNotes = $input['admin_notes'] ?? '';
    $errorMessage = $input['error_message'] ?? '';
    
    // Валидируем действие
    $validActions = ['complete', 'cancel', 'fail'];
    if (!in_array($action, $validActions)) {
        throw new Exception('Invalid action. Allowed: ' . implode(', ', $validActions));
    }
    
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Получаем информацию о выводе
    $withdrawalInfo = $db->query(
        "SELECT * FROM user_withdrawals WHERE id = ?",
        [$withdrawalId]
    );
    
    if (empty($withdrawalInfo)) {
        throw new Exception('Withdrawal not found');
    }
    
    $withdrawal = $withdrawalInfo[0];
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    $currentStatus = $withdrawal['status'];
    
    // Проверяем, можно ли обработать этот вывод
    if (!in_array($currentStatus, ['pending', 'processing', 'waiting', 'confirming'])) {
        throw new Exception("Cannot process withdrawal with status: {$currentStatus}");
    }
    
    // Получаем текущий баланс пользователя
    $balance = $coinsManager->getUserBalance($userId);
    
    error_log("process_withdrawal INFO: Processing withdrawal #{$withdrawalId}, action: {$action}, user: {$userId}, amount: {$amount}");
    
    try {
        $db->beginTransaction();
        
        switch ($action) {
            case 'complete':
                // Завершение вывода - списываем зарезервированные средства
                $success = processWithdrawalComplete($withdrawalId, $withdrawal, $coinsManager, $db, $transactionHash, $adminNotes);
                break;
                
            case 'cancel':
                // Отмена вывода - возвращаем зарезервированные средства
                $success = processWithdrawalCancel($withdrawalId, $withdrawal, $coinsManager, $db, $adminNotes);
                break;
                
            case 'fail':
                // Ошибка вывода - возвращаем зарезервированные средства
                $success = processWithdrawalFail($withdrawalId, $withdrawal, $coinsManager, $db, $errorMessage, $adminNotes);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
        if (!$success) {
            throw new Exception('Failed to process withdrawal');
        }
        
        $db->commit();
        
        // Получаем обновленную информацию о выводе
        $updatedWithdrawal = $db->query("SELECT * FROM user_withdrawals WHERE id = ?", [$withdrawalId])[0];
        
        // Получаем обновленный баланс
        $newBalance = $coinsManager->getUserBalance($userId);
        
        // Логируем событие
        logAuditEvent('withdrawal_processed', $userId, [
            'withdrawal_id' => $withdrawalId,
            'action' => $action,
            'old_status' => $currentStatus,
            'new_status' => $updatedWithdrawal['status'],
            'amount' => $amount,
            'transaction_hash' => $transactionHash,
            'admin_notes' => $adminNotes
        ], $db);
        
        error_log("process_withdrawal INFO: Successfully processed withdrawal #{$withdrawalId} with action {$action}");
        
        // Возвращаем успешный ответ
        echo json_encode([
            'success' => true,
            'message' => "Withdrawal {$action}d successfully",
            'data' => [
                'withdrawal_id' => $withdrawalId,
                'action' => $action,
                'old_status' => $currentStatus,
                'new_status' => $updatedWithdrawal['status'],
                'amount' => $amount,
                'transaction_hash' => $transactionHash,
                'processed_at' => $updatedWithdrawal['processed_at'],
                'completed_at' => $updatedWithdrawal['completed_at'],
                'balance' => [
                    'current' => $newBalance['balance'],
                    'available' => $newBalance['available_balance'],
                    'reserved' => $newBalance['reserved_balance']
                ]
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("process_withdrawal ERROR: Transaction failed: " . $e->getMessage());
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("process_withdrawal ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'PROCESS_WITHDRAWAL_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Завершение вывода - списание зарезервированных средств
 */
function processWithdrawalComplete(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, ?string $transactionHash, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Получаем текущий баланс
    $balance = $coinsManager->getUserBalance($userId);
    
    // Проверяем, что средства зарезервированы
    if ($balance['reserved_balance'] < $amount) {
        throw new Exception('Insufficient reserved balance');
    }
    
    // Списываем зарезервированные средства (снимаем резерв и списываем с основного баланса)
    $newBalance = $balance['balance'] - $amount;
    $newReservedBalance = $balance['reserved_balance'] - $amount;
    
    // Обновляем баланс пользователя
    $db->query(
        "UPDATE users SET balance = ?, reserved_balance = ? WHERE telegram_id = ?",
        [$newBalance, $newReservedBalance, $userId]
    );
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'completed', 
         transaction_hash = ?, 
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP,
         completed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$transactionHash, $adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию списания
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_complete',
        $amount,
        'debit',
        $balance['balance'],
        $newBalance,
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} completed",
        [
            'withdrawal_id' => $withdrawalId,
            'transaction_hash' => $transactionHash,
            'currency' => $withdrawal['currency'],
            'wallet_address' => $withdrawal['wallet_address'],
            'crypto_amount' => $withdrawal['crypto_amount'],
            'final_amount' => $withdrawal['final_amount']
        ]
    );
    
    return true;
}

/**
 * Отмена вывода - возврат зарезервированных средств
 */
function processWithdrawalCancel(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Снимаем резерв (возвращаем средства в доступный баланс)
    $unreserveSuccess = $coinsManager->unreserveCoins($userId, $amount, $withdrawalId);
    
    if (!$unreserveSuccess) {
        throw new Exception('Failed to unreserve coins');
    }
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'cancelled', 
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию отмены
    $balance = $coinsManager->getUserBalance($userId);
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_cancel',
        $amount,
        'credit',
        $balance['balance'],
        $balance['balance'], // Баланс не меняется, только резерв
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} cancelled - funds returned",
        [
            'withdrawal_id' => $withdrawalId,
            'reason' => 'cancelled',
            'admin_notes' => $adminNotes
        ]
    );
    
    return true;
}

/**
 * Ошибка вывода - возврат зарезервированных средств
 */
function processWithdrawalFail(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, string $errorMessage, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Снимаем резерв (возвращаем средства в доступный баланс)
    $unreserveSuccess = $coinsManager->unreserveCoins($userId, $amount, $withdrawalId);
    
    if (!$unreserveSuccess) {
        throw new Exception('Failed to unreserve coins');
    }
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'failed', 
         error_message = ?,
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$errorMessage, $adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию ошибки
    $balance = $coinsManager->getUserBalance($userId);
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_fail',
        $amount,
        'credit',
        $balance['balance'],
        $balance['balance'], // Баланс не меняется, только резерв
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} failed - funds returned",
        [
            'withdrawal_id' => $withdrawalId,
            'reason' => 'failed',
            'error_message' => $errorMessage,
            'admin_notes' => $adminNotes
        ]
    );
    
    return true;
}

/**
 * Логирование событий аудита
 */
function logAuditEvent(string $event, string $userId, array $data, RealSQLiteManager $db): void {
    try {
        $db->query(
            "INSERT INTO audit_logs (event_type, user_id, event_data, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
            [$event, $userId, json_encode($data)]
        );
    } catch (Exception $e) {
        error_log("logAuditEvent ERROR: " . $e->getMessage());
    }
}
?>
