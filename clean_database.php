<?php
/**
 * clean_database.php
 * Полная очистка SQLite базы данных перед миграцией
 */

declare(strict_types=1);

echo "🗑️ ПОЛНАЯ ОЧИСТКА БАЗЫ ДАННЫХ\n";
echo "==============================\n\n";

try {
    $dbPath = __DIR__ . '/database/app.sqlite';
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Подключение к базе данных установлено\n\n";
    
    // Получаем список всех таблиц
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Найдено таблиц для очистки: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "   - {$table}\n";
    }
    echo "\n";
    
    // Отключаем проверку внешних ключей
    $pdo->exec("PRAGMA foreign_keys = OFF");
    
    // Очищаем все таблицы
    echo "🗑️ Очистка таблиц:\n";
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
            echo "   - {$table}: {$count} записей → ";
            
            $pdo->exec("DELETE FROM {$table}");
            echo "✅ очищено\n";
            
        } catch (Exception $e) {
            echo "❌ ошибка: " . $e->getMessage() . "\n";
        }
    }
    
    // Сбрасываем все автоинкременты
    echo "\n🔄 Сброс автоинкрементов:\n";
    $pdo->exec("DELETE FROM sqlite_sequence");
    echo "   ✅ Все счетчики ID сброшены\n";
    
    // Включаем обратно проверку внешних ключей
    $pdo->exec("PRAGMA foreign_keys = ON");
    
    // Проверяем результат
    echo "\n📊 Проверка результата:\n";
    $totalRecords = 0;
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
        echo "   - {$table}: {$count} записей\n";
        $totalRecords += $count;
    }
    
    echo "\n🎉 ОЧИСТКА ЗАВЕРШЕНА!\n";
    echo "===================\n";
    echo "✅ Всего записей в базе: {$totalRecords}\n";
    echo "✅ База данных готова для чистой миграции\n\n";
    
    echo "🚀 СЛЕДУЮЩИЕ ШАГИ:\n";
    echo "1. Запустите полную миграцию данных\n";
    echo "2. Проверьте корректность мигрированных данных\n";
    echo "3. Протестируйте систему выплат\n";
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}
?>
