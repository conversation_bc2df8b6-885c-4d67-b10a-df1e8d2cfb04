<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо: Награды из админки</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        .demo-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-title {
            color: var(--cyber-accent-neon);
            font-size: 18px;
            margin-bottom: 15px;
        }
        .ad-button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            font-size: 16px;
            position: relative;
            min-width: 200px;
        }
        .ad-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        .ad-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .reward-circle {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #fff;
            color: #ff6b35;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid #ff6b35;
        }
        .settings-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .settings-table th,
        .settings-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--cyber-border);
        }
        .settings-table th {
            background: rgba(0, 255, 255, 0.1);
            color: var(--cyber-accent-neon);
        }
        .settings-table td {
            color: var(--cyber-text-primary);
        }
        .status-message {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .status-info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        .status-warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--cyber-border);
            border-radius: 10px;
            padding: 15px;
        }
        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--cyber-text-primary);
        }
        .refresh-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .refresh-button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); margin-bottom: 30px;">
            ⚙️ Демо: Награды из админки
        </h1>

        <!-- Статус загрузки -->
        <div class="demo-section">
            <div class="demo-title">📡 Статус загрузки настроек</div>
            <div id="loading-status" class="status-message status-info">
                Загрузка настроек из базы данных...
            </div>
            <div style="text-align: center;">
                <button class="refresh-button" onclick="refreshSettings()">
                    🔄 Обновить настройки
                </button>
            </div>
        </div>

        <!-- Кнопки рекламы с наградами -->
        <div class="demo-section">
            <div class="demo-title">📺 Кнопки рекламы (как в приложении)</div>
            <div style="text-align: center;">
                <button class="ad-button" data-ad-type="native_banner" onclick="showRewardInfo('native_banner')">
                    🔗 Open Link
                    <div class="reward-circle" id="reward-native_banner">10</div>
                </button>
                <br>
                <button class="ad-button" data-ad-type="rewarded_video" onclick="showRewardInfo('rewarded_video')">
                    🎥 Watch Video
                    <div class="reward-circle" id="reward-rewarded_video">1</div>
                </button>
                <br>
                <button class="ad-button" data-ad-type="interstitial" onclick="showRewardInfo('interstitial')">
                    📱 Tap and Earn
                    <div class="reward-circle" id="reward-interstitial">10</div>
                </button>
            </div>
            <div style="text-align: center; margin-top: 20px; font-size: 14px; color: var(--cyber-text-secondary);">
                💡 Числа в кружочках берутся из настроек админки
            </div>
        </div>

        <!-- Таблица настроек -->
        <div class="demo-section">
            <div class="demo-title">⚙️ Настройки из базы данных</div>
            <table class="settings-table">
                <thead>
                    <tr>
                        <th>Тип рекламы</th>
                        <th>Ключ в базе</th>
                        <th>Награда (монет)</th>
                        <th>Статус</th>
                    </tr>
                </thead>
                <tbody id="settings-table-body">
                    <tr>
                        <td colspan="4" style="text-align: center; color: var(--cyber-text-secondary);">
                            Загрузка настроек...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Сравнение -->
        <div class="demo-section">
            <div class="demo-title">🔄 Сравнение: до и после</div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <div class="comparison-title">❌ Раньше (захардкожено)</div>
                    <div style="font-size: 12px; color: var(--cyber-text-secondary);">
                        <div>📱 Баннер: 5 монет (фиксированно)</div>
                        <div>🎥 Видео: 10 монет (фиксированно)</div>
                        <div>📺 Полноэкранная: 8 монет (фиксированно)</div>
                        <div style="margin-top: 10px; color: #ff6b6b;">
                            ⚠️ Нельзя изменить без правки кода
                        </div>
                    </div>
                </div>
                <div class="comparison-card">
                    <div class="comparison-title">✅ Теперь (из админки)</div>
                    <div style="font-size: 12px; color: var(--cyber-text-secondary);">
                        <div>📱 Нативный баннер: <span id="current-native_banner">10</span> монет</div>
                        <div>🎥 Видео с наградой: <span id="current-rewarded_video">1</span> монет</div>
                        <div>📺 Полноэкранная: <span id="current-interstitial">10</span> монет</div>
                        <div style="margin-top: 10px; color: #4caf50;">
                            ✅ Настраивается через админку
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Информация о награде -->
        <div class="demo-section">
            <div class="demo-title">ℹ️ Информация о выбранной награде</div>
            <div id="reward-info" class="status-message status-info">
                Нажмите на кнопку рекламы, чтобы увидеть информацию о награде
            </div>
        </div>
    </div>

    <!-- Подключение скриптов -->
    <script src="js/ad-reward-integration.js"></script>

    <script>
        let currentSettings = null;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('loading-status');
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type}`;
        }

        function showRewardInfo(adType) {
            const infoEl = document.getElementById('reward-info');
            
            if (currentSettings && currentSettings.rewards) {
                const reward = currentSettings.rewards[adType] || 0;
                const description = getAdTypeDescription(adType);
                
                infoEl.innerHTML = `
                    <strong>${description}</strong><br>
                    Награда: <strong>${reward} монет</strong><br>
                    Тип: <code>${adType}</code><br>
                    Источник: База данных (coin_settings)
                `;
                infoEl.className = 'status-message status-success';
            } else {
                infoEl.textContent = 'Настройки еще не загружены';
                infoEl.className = 'status-message status-warning';
            }
        }

        function getAdTypeDescription(adType) {
            const descriptions = {
                'native_banner': 'Нативный баннер (Open Link)',
                'rewarded_video': 'Видео с наградой (Watch Video)',
                'interstitial': 'Полноэкранная реклама (Tap and Earn)'
            };
            return descriptions[adType] || adType;
        }

        async function loadAdSettings() {
            try {
                showStatus('Загрузка настроек из базы данных...', 'info');

                const response = await fetch('/api/getAdSettings.php');
                const data = await response.json();

                if (data.success) {
                    currentSettings = data;
                    updateDisplay(data);
                    showStatus('Настройки успешно загружены из базы данных', 'success');
                } else {
                    currentSettings = { rewards: data.fallback_rewards || {} };
                    updateDisplay(currentSettings);
                    showStatus('Используются fallback настройки', 'warning');
                }

            } catch (error) {
                console.error('Ошибка загрузки настроек:', error);
                showStatus(`Ошибка загрузки: ${error.message}`, 'warning');
            }
        }

        function updateDisplay(data) {
            const rewards = data.ad_rewards_extended || data.ad_rewards || data.rewards || {};

            // Обновляем кружочки в кнопках
            for (const [adType, reward] of Object.entries(rewards)) {
                const circleEl = document.getElementById(`reward-${adType}`);
                if (circleEl) {
                    circleEl.textContent = reward;
                }

                const currentEl = document.getElementById(`current-${adType}`);
                if (currentEl) {
                    currentEl.textContent = reward;
                }
            }

            // Обновляем таблицу настроек
            updateSettingsTable(rewards);
        }

        function updateSettingsTable(rewards) {
            const tbody = document.getElementById('settings-table-body');
            tbody.innerHTML = '';

            const mainTypes = ['native_banner', 'rewarded_video', 'interstitial'];

            mainTypes.forEach(adType => {
                const reward = rewards[adType] || 0;
                const row = document.createElement('tr');
                
                row.innerHTML = `
                    <td>${getAdTypeDescription(adType)}</td>
                    <td><code>ad_reward_${adType}</code></td>
                    <td><strong>${reward}</strong></td>
                    <td><span style="color: ${reward > 0 ? '#4caf50' : '#ff6b6b'}">
                        ${reward > 0 ? '✅ Загружено' : '❌ Не найдено'}
                    </span></td>
                `;
                
                tbody.appendChild(row);
            });
        }

        async function refreshSettings() {
            showStatus('Обновление настроек...', 'info');
            await loadAdSettings();
        }

        // Автоматическая загрузка при открытии страницы
        window.addEventListener('load', () => {
            console.log('Демо страница наград загружена');
            
            setTimeout(() => {
                loadAdSettings();
            }, 1000);
        });
    </script>
</body>
</html>
