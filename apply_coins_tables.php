<?php
/**
 * apply_coins_tables.php
 * Применение таблиц для системы монет по частям
 */

declare(strict_types=1);

echo "🔨 ПРИМЕНЕНИЕ ТАБЛИЦ ДЛЯ СИСТЕМЫ МОНЕТ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    echo "1. 🔧 Добавление колонки reserved_balance в users:\n";
    
    try {
        $db->query("ALTER TABLE users ADD COLUMN reserved_balance DECIMAL(10,2) DEFAULT 0");
        echo "   ✅ Колонка reserved_balance добавлена\n";
    } catch (Exception $e) {
        if (str_contains($e->getMessage(), 'duplicate column name')) {
            echo "   ✅ Колонка reserved_balance уже существует\n";
        } else {
            echo "   ❌ Ошибка: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n2. 🏗️ Создание таблицы coin_transactions:\n";
    
    $coinTransactionsSQL = "
    CREATE TABLE IF NOT EXISTS coin_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id BIGINT NOT NULL,
        transaction_type TEXT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        operation TEXT NOT NULL,
        balance_before DECIMAL(10,2) NOT NULL,
        balance_after DECIMAL(10,2) NOT NULL,
        source_type TEXT,
        source_id INTEGER,
        description TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT check_amount_positive CHECK (amount > 0),
        CONSTRAINT check_operation_valid CHECK (operation IN ('credit', 'debit')),
        CONSTRAINT check_transaction_type_valid CHECK (
            transaction_type IN ('earn', 'withdraw', 'reserve', 'unreserve', 'bonus', 'penalty', 'refund')
        )
    )";
    
    try {
        $db->query($coinTransactionsSQL);
        echo "   ✅ Таблица coin_transactions создана\n";
    } catch (Exception $e) {
        echo "   ❌ Ошибка создания coin_transactions: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. 🏗️ Создание индексов для coin_transactions:\n";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_coin_transactions_user_id ON coin_transactions(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_coin_transactions_type ON coin_transactions(transaction_type)",
        "CREATE INDEX IF NOT EXISTS idx_coin_transactions_created_at ON coin_transactions(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_coin_transactions_source ON coin_transactions(source_type, source_id)"
    ];
    
    foreach ($indexes as $indexSQL) {
        try {
            $db->query($indexSQL);
            echo "   ✅ Индекс создан\n";
        } catch (Exception $e) {
            echo "   ❌ Ошибка создания индекса: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. 🏗️ Создание таблицы coin_settings:\n";
    
    $coinSettingsSQL = "
    CREATE TABLE IF NOT EXISTS coin_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $db->query($coinSettingsSQL);
        echo "   ✅ Таблица coin_settings создана\n";
        
        // Создаем индекс
        $db->query("CREATE INDEX IF NOT EXISTS idx_coin_settings_key ON coin_settings(setting_key)");
        echo "   ✅ Индекс для coin_settings создан\n";
    } catch (Exception $e) {
        echo "   ❌ Ошибка создания coin_settings: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. 📝 Вставка базовых настроек:\n";
    
    $settings = [
        ['coin_rate_usd', '0.001', 'Курс монеты к доллару США'],
        ['min_withdrawal_coins', '1000', 'Минимальная сумма для вывода в монетах'],
        ['ad_reward_native_banner', '10', 'Награда за просмотр нативного баннера'],
        ['ad_reward_rewarded_video', '1', 'Награда за просмотр видео'],
        ['ad_reward_interstitial', '10', 'Награда за просмотр интерстициала'],
        ['referral_bonus_percent', '10', 'Процент бонуса с рефералов'],
        ['withdrawal_fee_percent', '0', 'Процент комиссии за вывод'],
        ['daily_earn_limit', '200', 'Дневной лимит заработка монет']
    ];
    
    $inserted = 0;
    foreach ($settings as $setting) {
        try {
            $db->query(
                "INSERT OR IGNORE INTO coin_settings (setting_key, setting_value, description) VALUES (?, ?, ?)",
                $setting
            );
            $inserted++;
            echo "   ✅ {$setting[0]}: {$setting[1]}\n";
        } catch (Exception $e) {
            echo "   ❌ Ошибка вставки {$setting[0]}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "   📊 Вставлено настроек: {$inserted}\n";
    
    echo "\n6. 🏗️ Создание таблицы user_daily_stats:\n";
    
    $userDailyStatsSQL = "
    CREATE TABLE IF NOT EXISTS user_daily_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id BIGINT NOT NULL,
        date DATE NOT NULL,
        coins_earned_today INTEGER DEFAULT 0,
        ads_viewed_today INTEGER DEFAULT 0,
        referral_earnings_today DECIMAL(10,2) DEFAULT 0,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE(user_id, date)
    )";
    
    try {
        $db->query($userDailyStatsSQL);
        echo "   ✅ Таблица user_daily_stats создана\n";
        
        // Создаем индексы
        $db->query("CREATE INDEX IF NOT EXISTS idx_user_daily_stats_user_date ON user_daily_stats(user_id, date)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_user_daily_stats_date ON user_daily_stats(date)");
        echo "   ✅ Индексы для user_daily_stats созданы\n";
    } catch (Exception $e) {
        echo "   ❌ Ошибка создания user_daily_stats: " . $e->getMessage() . "\n";
    }
    
    echo "\n7. 🔧 Добавление колонок в user_withdrawals:\n";
    
    $withdrawalColumns = [
        'crypto_amount TEXT',
        'network_fee TEXT',
        'final_amount TEXT',
        'admin_notes TEXT',
        'completed_at DATETIME'
    ];
    
    foreach ($withdrawalColumns as $column) {
        try {
            $db->query("ALTER TABLE user_withdrawals ADD COLUMN {$column}");
            echo "   ✅ Колонка добавлена: {$column}\n";
        } catch (Exception $e) {
            if (str_contains($e->getMessage(), 'duplicate column name')) {
                echo "   ✅ Колонка уже существует: {$column}\n";
            } else {
                echo "   ❌ Ошибка добавления {$column}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n8. 📊 Финальная проверка:\n";
    
    // Проверяем созданные таблицы
    $tables = ['coin_transactions', 'coin_settings', 'user_daily_stats'];
    
    foreach ($tables as $table) {
        try {
            $count = $db->query("SELECT COUNT(*) as count FROM {$table}")[0]['count'];
            echo "   ✅ {$table}: {$count} записей\n";
        } catch (Exception $e) {
            echo "   ❌ {$table}: НЕ НАЙДЕНА\n";
        }
    }
    
    // Проверяем настройки
    try {
        $settingsCount = $db->query("SELECT COUNT(*) as count FROM coin_settings")[0]['count'];
        echo "   ✅ Настроек в базе: {$settingsCount}\n";
    } catch (Exception $e) {
        echo "   ❌ Настройки недоступны\n";
    }
    
    // Проверяем колонку reserved_balance
    try {
        $testUser = $db->query("SELECT telegram_id, balance, reserved_balance FROM users LIMIT 1");
        if (!empty($testUser)) {
            echo "   ✅ Колонка reserved_balance работает\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Колонка reserved_balance недоступна\n";
    }
    
    echo "\n✅ СИСТЕМА МОНЕТ УСПЕШНО НАСТРОЕНА!\n";
    
    echo "\n🎯 ГОТОВЫЕ КОМПОНЕНТЫ:\n";
    echo "   ✅ Таблица транзакций (coin_transactions)\n";
    echo "   ✅ Таблица настроек (coin_settings)\n";
    echo "   ✅ Таблица дневной статистики (user_daily_stats)\n";
    echo "   ✅ Расширенная таблица пользователей (reserved_balance)\n";
    echo "   ✅ Расширенная таблица выводов (дополнительные поля)\n";
    echo "   ✅ Базовые настройки системы\n";
    
} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
