# 🔧 Устранение проблем с Webhook

## 🚨 Если получаете ошибку "❌ Ошибка при обновлении webhook"

### 🎯 Быстрое решение (рекомендуется)

1. **Зайдите в админку** → Настройки бота
2. **Прокрутите вниз** до блока "🔧 Инструменты отладки"
3. **Нажмите "⚡ Быстрое исправление"** - система автоматически найдет и исправит проблемы

### 🔍 Пошаговая диагностика

#### Шаг 1: Проверка токенов и URL
1. **Нажмите "🧪 Прямой тест webhook"**
2. Проверьте результаты для обоих ботов:
   - ✅ Токен валиден
   - ✅ URL доступен
   - ✅ Webhook установлен

#### Шаг 2: Просмотр логов ошибок
1. **Нажмите "📋 Просмотр логов"**
2. Найдите записи с ошибками (красным цветом)
3. Обратите внимание на:
   - `CURL ошибка` - проблемы с сетью
   - `HTTP ошибка` - проблемы с сервером
   - `Telegram API ошибка` - проблемы с настройками

#### Шаг 3: Полная диагностика
1. **Нажмите "🔍 Полная диагностика"**
2. Проверьте детальную информацию о каждом боте

## 🛠 Типичные проблемы и решения

### 1. **Неверный токен бота**
**Симптомы:** `❌ Проблема с токеном`
**Решение:** 
- Проверьте токены в настройках бота
- Убедитесь, что токены скопированы полностью
- Проверьте, что боты активны в @BotFather

### 2. **URL недоступен**
**Симптомы:** `❌ URL недоступен`
**Решение:**
- Убедитесь, что сайт доступен из интернета
- Проверьте SSL сертификат
- Убедитесь, что файлы webhook существуют

### 3. **Неправильная папка проекта**
**Симптомы:** URL содержит старую папку (например, test3 вместо test4)
**Решение:**
1. Используйте **"🔍 Определить текущую папку"**
2. Нажмите **"🚀 Обновить все URL автоматически"**

### 4. **Проблемы с SSL**
**Симптомы:** `SSL certificate problem`
**Решение:**
- Обновите SSL сертификат
- Проверьте, что домен правильно настроен

### 5. **Файл webhook не найден**
**Симптомы:** `HTTP 404` при проверке URL
**Решение:**
- Убедитесь, что файлы существуют:
  - `/bot/webhook.php` (основной бот)
  - `/api/admin/support_webhook.php` (бот поддержки)

## 📋 Инструменты диагностики

### ⚡ Быстрое исправление
- **Что делает:** Автоматически находит и исправляет проблемы
- **Когда использовать:** Первым делом при любых проблемах
- **Время выполнения:** 10-30 секунд

### 🧪 Прямой тест webhook
- **Что делает:** Проверяет токены, URL и устанавливает webhook
- **Когда использовать:** Для детальной проверки каждого бота
- **Показывает:** Пошаговый процесс установки webhook

### 🔍 Полная диагностика
- **Что делает:** Интерактивная проверка с детальными результатами
- **Когда использовать:** Для глубокого анализа проблем
- **Особенности:** Красивый интерфейс с подробными результатами

### 📋 Просмотр логов
- **Что делает:** Показывает последние ошибки из логов
- **Когда использовать:** Для понимания причин ошибок
- **Обновление:** Автоматически каждые 30 секунд

## 🎯 Алгоритм устранения неполадок

```
1. ⚡ Быстрое исправление
   ↓
2. Проблема решена? → ✅ Готово!
   ↓
3. 📋 Просмотр логов → Анализ ошибок
   ↓
4. 🧪 Прямой тест → Детальная проверка
   ↓
5. Исправление конкретной проблемы
   ↓
6. Повторить шаг 1
```

## 🆘 Если ничего не помогает

1. **Проверьте интернет-соединение**
2. **Убедитесь, что сервер работает**
3. **Проверьте, что домен не заблокирован**
4. **Обратитесь к хостинг-провайдеру**

## 📞 Контакты для поддержки

- **Логи ошибок:** Всегда сохраняйте логи для анализа
- **Скриншоты:** Делайте скриншоты ошибок
- **Детали:** Указывайте, какие действия привели к ошибке

---

**💡 Совет:** Используйте "⚡ Быстрое исправление" регулярно для профилактики проблем!
