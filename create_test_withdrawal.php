<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Создание тестовой выплаты</title>
    <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #000; padding: 20px; border-radius: 10px; border: 2px solid #00ff00; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; color: #00ffff; }
        .form-group input, .form-group select { width: 100%; padding: 10px; background: #111; color: #00ff00; border: 1px solid #333; border-radius: 5px; }
        .button { background: #00ff00; color: #000; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 10px; }
        .output { background: #111; padding: 15px; border-radius: 5px; white-space: pre-wrap; font-size: 14px; max-height: 400px; overflow-y: auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #004400; border: 1px solid #00ff00; }
        .error { background: #440000; border: 1px solid #ff0000; color: #ff0000; }
        .info { background: #004444; border: 1px solid #00ffff; color: #00ffff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 СОЗДАНИЕ ТЕСТОВОЙ ВЫПЛАТЫ</h1>
        
        <?php if (!isset($_POST['create_withdrawal'])): ?>
            <div class="status info">
                <h3>📋 Создание одной тестовой выплаты для демонстрации</h3>
                <p>Эта форма создаст одну выплату для тестирования автообновляющейся истории.</p>
            </div>

            <form method="POST">
                <div class="form-group">
                    <label>👤 Пользователь:</label>
                    <select name="user_id" required>
                        <option value="">Выберите пользователя...</option>
                        <?php
                        try {
                            $dbPath = __DIR__ . '/database/app.sqlite';
                            $pdo = new PDO('sqlite:' . $dbPath);
                            $users = $pdo->query("
                                SELECT telegram_id, first_name, username, balance 
                                FROM users 
                                WHERE balance > 10 
                                ORDER BY balance DESC 
                                LIMIT 20
                            ")->fetchAll(PDO::FETCH_ASSOC);
                            
                            foreach ($users as $user) {
                                $name = $user['first_name'] . ($user['username'] ? ' (@' . $user['username'] . ')' : '');
                                echo "<option value='{$user['telegram_id']}'>{$name} - {$user['balance']} монет</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>Ошибка загрузки пользователей</option>";
                        }
                        ?>
                    </select>
                </div>

                <div class="form-group">
                    <label>💰 Сумма (TON):</label>
                    <input type="number" name="amount" step="0.01" min="0.01" max="1000" value="25.00" required>
                </div>

                <div class="form-group">
                    <label>💳 Адрес кошелька:</label>
                    <input type="text" name="wallet_address" value="EQD<?= bin2hex(random_bytes(24)) ?>" required>
                </div>

                <div class="form-group">
                    <label>📊 Статус:</label>
                    <select name="status" required>
                        <option value="pending">⏳ Pending - Ожидает обработки</option>
                        <option value="processing">🔄 Processing - В обработке</option>
                        <option value="completed">✅ Completed - Завершена</option>
                        <option value="failed">❌ Failed - Ошибка</option>
                        <option value="cancelled">🚫 Cancelled - Отменена</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>🔗 Хеш транзакции (для completed):</label>
                    <input type="text" name="transaction_hash" placeholder="Оставьте пустым для автогенерации">
                </div>

                <div class="form-group">
                    <label>📝 Сообщение об ошибке (для failed):</label>
                    <input type="text" name="error_message" placeholder="Только для статуса failed">
                </div>

                <div style="text-align: center;">
                    <button type="submit" name="create_withdrawal" class="button">
                        💰 СОЗДАТЬ ТЕСТОВУЮ ВЫПЛАТУ
                    </button>
                    <a href="test_migration_web.php" class="button" style="background: #666; text-decoration: none;">
                        🔙 НАЗАД
                    </a>
                </div>
            </form>

        <?php else: ?>
            <div class="output">
<?php
            try {
                $dbPath = __DIR__ . '/database/app.sqlite';
                $pdo = new PDO('sqlite:' . $dbPath);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "💰 СОЗДАНИЕ ТЕСТОВОЙ ВЫПЛАТЫ\n";
                echo "============================\n\n";
                
                $userId = $_POST['user_id'];
                $amount = (float)$_POST['amount'];
                $walletAddress = $_POST['wallet_address'];
                $status = $_POST['status'];
                $transactionHash = $_POST['transaction_hash'] ?: null;
                $errorMessage = $_POST['error_message'] ?: null;
                
                // Автогенерация хеша для completed
                if ($status === 'completed' && !$transactionHash) {
                    $transactionHash = hash('sha256', $userId . $amount . time());
                }
                
                // Получаем информацию о пользователе
                $userInfo = $pdo->prepare("SELECT first_name, username, balance FROM users WHERE telegram_id = ?");
                $userInfo->execute([$userId]);
                $user = $userInfo->fetch(PDO::FETCH_ASSOC);
                
                if (!$user) {
                    throw new Exception("Пользователь не найден");
                }
                
                echo "👤 Пользователь: {$user['first_name']} (ID: {$userId})\n";
                echo "💎 Текущий баланс: {$user['balance']} монет\n";
                echo "💰 Сумма выплаты: {$amount} TON\n";
                echo "📊 Статус: {$status}\n";
                echo "💳 Кошелек: {$walletAddress}\n\n";
                
                // Создаем выплату
                $stmt = $pdo->prepare("
                    INSERT INTO user_withdrawals (
                        user_id, amount, currency, wallet_address, status,
                        transaction_hash, requested_at, processed_at, error_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $requestedAt = date('Y-m-d H:i:s');
                $processedAt = in_array($status, ['completed', 'failed']) ? date('Y-m-d H:i:s') : null;
                
                $stmt->execute([
                    $userId,
                    $amount,
                    'TON',
                    $walletAddress,
                    $status,
                    $transactionHash,
                    $requestedAt,
                    $processedAt,
                    $errorMessage
                ]);
                
                $withdrawalId = $pdo->lastInsertId();
                
                echo "✅ ВЫПЛАТА СОЗДАНА УСПЕШНО!\n";
                echo "🆔 ID выплаты: {$withdrawalId}\n";
                echo "📅 Дата создания: {$requestedAt}\n";
                
                if ($processedAt) {
                    echo "✅ Дата обработки: {$processedAt}\n";
                }
                
                if ($transactionHash) {
                    echo "🔗 Хеш транзакции: {$transactionHash}\n";
                }
                
                if ($errorMessage) {
                    echo "❌ Ошибка: {$errorMessage}\n";
                }
                
                echo "\n🎯 СЛЕДУЮЩИЕ ШАГИ:\n";
                echo "1. Откройте demo_withdrawal_history.html\n";
                echo "2. Проверьте автообновление каждые 15 секунд\n";
                echo "3. Убедитесь, что новая выплата отображается\n";
                
            } catch (Exception $e) {
                echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
                echo "Стек: " . $e->getTraceAsString() . "\n";
            }
?>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="?" class="button">🔄 СОЗДАТЬ ЕЩЕ ОДНУ</a>
                <a href="demo_withdrawal_history.html" class="button">📋 ТЕСТ ИСТОРИИ ВЫПЛАТ</a>
                <a href="test_migration_web.php" class="button">🔙 НАЗАД</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
