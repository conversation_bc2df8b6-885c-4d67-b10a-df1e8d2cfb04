<?php
/**
 * api/recordAdView_sqlite.php
 * API для начисления монет за просмотр рекламы (SQLite версия)
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    if (!isset($input['initData']) || !isset($input['adType'])) {
        throw new Exception('Missing required fields: initData, adType');
    }
    
    $initData = $input['initData'];
    $adType = $input['adType'];
    
    // Получаем ID пользователя из initData
    $userId = null;
    
    if (strpos($initData, 'user=') !== false) {
        // Парсим initData из Telegram
        parse_str($initData, $initDataParsed);
        if (isset($initDataParsed['user'])) {
            $userData = json_decode($initDataParsed['user'], true);
            if ($userData && isset($userData['id'])) {
                $userId = (string)$userData['id'];
            }
        }
    } else {
        // Для тестирования - извлекаем ID из тестовых данных
        if (preg_match('/user_(\d+)/', $initData, $matches)) {
            $userId = $matches[1];
        }
    }
    
    if (!$userId) {
        throw new Exception('Cannot extract user ID from initData');
    }
    
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Определяем награду за просмотр рекламы
    $adRewards = [
        'banner' => 5,
        'video' => 10,
        'interstitial' => 8,
        'rewarded' => 15,
        'default' => 10
    ];
    
    $reward = $adRewards[$adType] ?? $adRewards['default'];
    
    // Проверяем дневные лимиты
    $dailyStats = $coinsManager->getDailyStats($userId);
    $dailyLimit = $dailyStats['limit'] ?? 200;
    $earnedToday = $dailyStats['earned_today'] ?? 0;
    
    if ($earnedToday >= $dailyLimit) {
        throw new Exception('Daily earning limit reached');
    }
    
    // Проверяем, не превысит ли награда дневной лимит
    if (($earnedToday + $reward) > $dailyLimit) {
        $reward = $dailyLimit - $earnedToday;
        if ($reward <= 0) {
            throw new Exception('Daily earning limit reached');
        }
    }
    
    // Проверяем лимиты по типу рекламы (простая проверка)
    $lastAdTime = $db->query(
        "SELECT MAX(created_at) as last_time 
         FROM coin_transactions 
         WHERE user_id = ? AND transaction_type = 'ad_reward' AND description LIKE ?",
        [$userId, "%{$adType}%"]
    );
    
    if (!empty($lastAdTime) && $lastAdTime[0]['last_time']) {
        $lastTime = strtotime($lastAdTime[0]['last_time']);
        $cooldownSeconds = 30; // 30 секунд между просмотрами
        
        if ((time() - $lastTime) < $cooldownSeconds) {
            throw new Exception('Please wait before watching another ad');
        }
    }
    
    // Начисляем монеты
    $success = $coinsManager->creditCoins(
        $userId,
        $reward,
        'ad_reward',
        'system',
        null,
        "Ad reward for {$adType} ad",
        [
            'ad_type' => $adType,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => time()
        ]
    );
    
    if (!$success) {
        throw new Exception('Failed to credit coins');
    }
    
    // Получаем обновленный баланс
    $balance = $coinsManager->getUserBalance($userId);
    $newBalance = $balance['balance'];
    
    // Обновляем дневную статистику
    $updatedDailyStats = $coinsManager->getDailyStats($userId);
    
    // Логируем успешное начисление
    error_log("recordAdView_sqlite SUCCESS: User {$userId} earned {$reward} coins for {$adType} ad. New balance: {$newBalance}");
    
    // Формируем ответ
    $response = [
        'success' => true,
        'reward' => (float)$reward,
        'newBalance' => (float)$newBalance,
        'adType' => $adType,
        'daily_stats' => [
            'earned_today' => (float)$updatedDailyStats['earned_today'],
            'limit' => (float)$updatedDailyStats['limit'],
            'remaining' => (float)($updatedDailyStats['limit'] - $updatedDailyStats['earned_today'])
        ],
        'message' => "Reward credited successfully",
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("recordAdView_sqlite ERROR: " . $e->getMessage());
    
    // Определяем код ошибки
    $errorCode = 400;
    if (strpos($e->getMessage(), 'limit') !== false) {
        $errorCode = 429; // Too Many Requests
    } elseif (strpos($e->getMessage(), 'wait') !== false) {
        $errorCode = 429; // Too Many Requests
    }
    
    http_response_code($errorCode);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'AD_REWARD_ERROR',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
