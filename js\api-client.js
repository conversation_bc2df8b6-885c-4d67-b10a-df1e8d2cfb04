// === api-client.js ===
// Файл: js/api-client.js
// Описание: Централизованный API клиент для всех запросов к серверу (полная интеграция из оригинала)

class ApiClient {
  constructor() {
    this.baseUrl = window.API_BASE_URL || 'https://app.uniqpaid.com/test4/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };

    // Счетчики для мониторинга
    this.requestCount = 0;
    this.errorCount = 0;
  }

  /**
   * Базовый метод для выполнения HTTP запросов (из оригинала)
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}/${endpoint}`;
    const config = {
      method: 'POST',
      headers: { ...this.defaultHeaders, ...options.headers },
      body: options.body ? JSON.stringify(options.body) : undefined,
      ...options
    };

    this.requestCount++;
    console.log(`[ApiClient] Запрос ${this.requestCount}: ${config.method} ${url}`);

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      console.log(`[ApiClient] Успешный ответ от ${endpoint}`);
      return data;

    } catch (error) {
      this.errorCount++;
      console.error(`[ApiClient] Ошибка запроса к ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Получает данные пользователя для API (из оригинала)
   */
  getUserDataForAPI() {
    const initData = window.Telegram?.WebApp?.initData;
    if (initData) {
      return { initData };
    }
    return null;
  }

  // === МЕТОДЫ ДЛЯ НАСТРОЕК ===

  /**
   * Загружает настройки приложения с сервера (из оригинала)
   */
  async loadAppSettings() {
    try {
      console.log('[ApiClient] Загрузка настроек приложения...');

      const data = await this.request('getAppSettings.php', { method: 'GET' });

      if (data.success && data.settings) {
        if (window.appSettings && window.appSettings.update) {
          window.appSettings.update(data.settings);
        }
        console.log('[ApiClient] Настройки загружены:', data.settings);

        // Загружаем актуальные данные о валютах
        await this.loadActualCurrencyData();

        // Обновляем отображение комиссий и минимумов
        if (window.updateFeeDisplay) window.updateFeeDisplay();
        if (window.updateMinimumDisplay) window.updateMinimumDisplay();

        // Принудительно обновляем калькулятор с новыми минимумами
        if (window.updateCalculatorDisplay) {
          const calcAmountInput = document.getElementById('calc-amount');
          if (calcAmountInput) {
            const currentAmount = parseInt(calcAmountInput.value) || 0;
            window.updateCalculatorDisplay(currentAmount);
          }
        }

        return true;
      }

      console.warn('[ApiClient] Не удалось загрузить настройки, используем значения по умолчанию');
      return false;

    } catch (error) {
      console.warn('[ApiClient] Ошибка загрузки настроек:', error);
      return false;
    }
  }

  /**
   * Загружает актуальные данные о валютах с сервера (из оригинала)
   */
  async loadActualCurrencyData() {
    try {
      console.log('[ApiClient] Загрузка актуальных данных о валютах...');

      // ИСПРАВЛЕНИЕ: Используем кэшированные данные для быстрой загрузки
      const data = await this.request('getCachedCurrencyData.php', { method: 'GET' });

      if (data.success && data.currencies) {
        // Обновляем данные о валютах
        if (window.currencyData) {
          Object.assign(window.currencyData, data.currencies);
        }

        // Сохраняем полные данные включая курсы для использования в расчетах
        window.loadedCurrencyData = data;

        console.log('[ApiClient] Актуальные данные о валютах загружены:', window.currencyData);
        console.log('[ApiClient] Курсы валют:', data.exchange_rates);

        // Принудительно обновляем отображение минимумов и комиссий
        if (window.updateMinimumDisplay) window.updateMinimumDisplay();
        if (window.updateFeeDisplay) window.updateFeeDisplay();

        // Обновляем калькулятор если есть введенная сумма
        if (window.updateCalculatorDisplay) {
          const calcAmountInput = document.getElementById('calc-amount');
          if (calcAmountInput && calcAmountInput.value) {
            const currentAmount = parseInt(calcAmountInput.value) || 0;
            if (currentAmount > 0) {
              window.updateCalculatorDisplay(currentAmount);
            }
          }
        }

        return true;
      }

      console.warn('[ApiClient] Не удалось загрузить данные о валютах, используем значения по умолчанию');
      return false;

    } catch (error) {
      console.warn('[ApiClient] Ошибка загрузки данных о валютах:', error);
      return false;
    }
  }

  // === МЕТОДЫ ДЛЯ ПОЛЬЗОВАТЕЛЕЙ ===

  /**
   * Загружает данные пользователя (обновленная версия с SQLite)
   */
  async loadUserData(userId) {
    try {
      console.log('[ApiClient] 📡 Загрузка данных пользователя...');

      // Сначала пробуем новый SQLite API через BalanceApiClient
      if (window.balanceApiClient) {
        try {
          const balanceData = await window.balanceApiClient.getUserBalance();

          if (balanceData.success) {
            console.log('[ApiClient] ✅ Данные загружены через SQLite API');

            // Обновляем баланс через BalanceManager
            if (window.balanceManager) {
              await window.balanceManager.loadBalanceFromServer();
            } else {
              // Fallback для старого кода
              if (window.updateBalanceDisplay) {
                window.updateBalanceDisplay(balanceData.balance);
              }
            }

            return {
              balance: balanceData.balance,
              available_balance: balanceData.available_balance,
              reserved_balance: balanceData.reserved_balance,
              total_earned: balanceData.total_earned,
              daily_stats: balanceData.daily_stats,
              withdrawal_settings: balanceData.withdrawal_settings,
              user_info: balanceData.user_info
            };
          }
        } catch (sqliteError) {
          console.warn('[ApiClient] SQLite API недоступен:', sqliteError);
        }
      }

      // Fallback на старый API
      console.log('[ApiClient] 🔄 Используем старый API...');
      const data = await this.request('getUserData.php', {
        body: { user_id: userId }
      });

      if (data.success) {
        console.log('[ApiClient] ✅ Данные загружены через старый API');

        // Обновляем баланс
        if (window.balanceManager) {
          window.balanceManager.updateBalance(data.user?.balance || 0, 'old_api');
        } else if (window.updateBalanceDisplay) {
          window.updateBalanceDisplay(data.user?.balance || 0);
        }

        return data.user;
      }

      return null;
    } catch (error) {
      console.error('[ApiClient] ❌ Ошибка загрузки данных пользователя:', error);
      return null;
    }
  }

  // === МЕТОДЫ ДЛЯ ВЫПЛАТ ===

  /**
   * Запрашивает вывод средств (из оригинала)
   */
  async requestWithdrawal(amount, address, currency, cryptoAmount) {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('requestWithdrawal.php', {
      body: {
        ...userData,
        amount,
        address,
        currency,
        crypto_amount: cryptoAmount
      }
    });
  }

  /**
   * Отменяет выплату (из оригинала)
   */
  async cancelWithdrawal(withdrawalId) {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('cancelWithdrawal.php', {
      body: {
        ...userData,
        withdrawal_id: withdrawalId
      }
    });
  }

  /**
   * Получает историю выплат (из оригинала)
   */
  async getWithdrawalHistory() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('getWithdrawalHistory.php', {
      body: userData
    });
  }

  /**
   * Проверяет статусы выплат пользователя (из оригинала)
   */
  async checkUserWithdrawals() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('checkUserWithdrawals.php', {
      body: userData
    });
  }

  /**
   * Принудительно обновляет статусы выплат (из оригинала)
   */
  async forceUpdateWithdrawalStatuses() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('forceUpdateWithdrawalStatuses.php', {
      body: userData
    });
  }

  // === МЕТОДЫ ДЛЯ РЕФЕРАЛОВ ===

  /**
   * Получает статистику рефералов (из оригинала)
   */
  async getReferralStats() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('getReferralStats.php', {
      body: userData
    });
  }

  /**
   * Регистрирует реферала (из оригинала)
   */
  async registerReferral(referrerId) {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('registerReferral.php', {
      body: {
        ...userData,
        referrer_id: referrerId
      }
    });
  }

  // === МЕТОДЫ ДЛЯ РЕКЛАМЫ ===

  /**
   * Записывает просмотр рекламы (обновленная SQLite версия)
   */
  async recordAdView(adType) {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    try {
      // Сначала пробуем новый SQLite API
      console.log('[ApiClient] 📡 Используем SQLite API для начисления за рекламу');

      const data = await this.request('recordAdView_sqlite.php', {
        body: { ...userData, adType }
      });

      if (data.success) {
        console.log('[ApiClient] ✅ Награда начислена через SQLite API:', data);

        // Обновляем баланс через BalanceManager
        if (window.balanceManager) {
          window.balanceManager.updateBalance(data.newBalance, `ad_reward_${adType}`);
        }

        return data;
      } else {
        throw new Error(data.error || 'SQLite API returned error');
      }

    } catch (error) {
      console.warn('[ApiClient] SQLite API недоступен, пробуем старый API:', error);

      // Fallback на старый API
      try {
        const fallbackData = await this.request('recordAdView.php', {
          body: { ...userData, adType }
        });

        console.log('[ApiClient] ✅ Награда начислена через старый API:', fallbackData);

        // Обновляем баланс
        if (window.balanceManager && fallbackData.newBalance !== undefined) {
          window.balanceManager.updateBalance(fallbackData.newBalance, `ad_reward_${adType}`);
        }

        return fallbackData;

      } catch (fallbackError) {
        console.error('[ApiClient] Оба API недоступны:', fallbackError);
        throw new Error('Не удалось начислить награду за рекламу');
      }
    }
  }

  /**
   * Отправляет данные о просмотре рекламы (из оригинала)
   */
  async submitAdView(adData) {
    try {
      // ИСПРАВЛЕНИЕ: Используем правильный API endpoint и структуру данных
      const requestData = {
        initData: adData.initData,
        adType: adData.ad_type,  // Преобразуем ad_type в adType
        reward: adData.reward,
        timestamp: adData.timestamp
      };

      console.log('[ApiClient] Отправляем данные на recordAdView.php:', requestData);

      const data = await this.request('recordAdView.php', {
        body: requestData
      });

      console.log('[ApiClient] Получен ответ от recordAdView.php:', data);

      // ИСПРАВЛЕНИЕ: Преобразуем ответ в ожидаемый формат
      if (data && typeof data.newBalance !== 'undefined') {
        return {
          success: true,
          new_balance: data.newBalance,
          message: 'Награда успешно начислена'
        };
      } else if (data && data.error) {
        return {
          success: false,
          message: data.error
        };
      } else {
        return {
          success: false,
          message: 'Неожиданный ответ сервера'
        };
      }
    } catch (error) {
      console.error('[ApiClient] Ошибка отправки данных о рекламе:', error);

      // Возвращаем структурированную ошибку
      return {
        success: false,
        message: error.message || 'Ошибка сети'
      };
    }
  }

  // === МЕТОДЫ ДЛЯ КАЛЬКУЛЯТОРА ===

  /**
   * Рассчитывает сумму криптовалюты (из оригинала)
   */
  async calculateCrypto(coinAmount, currency, checkBalance = true) {
    return this.request('calculateCrypto.php', {
      body: {
        coin_amount: coinAmount,
        currency: currency,
        check_balance: checkBalance
      }
    });
  }

  // === МЕТОДЫ ДЛЯ БАЛАНСА ===

  /**
   * Получает текущий баланс пользователя (из оригинала)
   */
  async getUserBalance() {
    const userData = this.getUserDataForAPI();
    if (!userData) {
      throw new Error('Нет данных пользователя');
    }

    return this.request('getUserBalance.php', {
      body: userData
    });
  }

  // === УТИЛИТЫ ===

  /**
   * Проверяет доступность API (из оригинала)
   */
  async checkApiHealth() {
    try {
      const data = await this.request('health.php', { method: 'GET' });
      return data.status === 'ok';
    } catch (error) {
      console.error('[ApiClient] API недоступно:', error);
      return false;
    }
  }

  /**
   * Получает статистику API клиента
   */
  getStats() {
    return {
      totalRequests: this.requestCount,
      totalErrors: this.errorCount,
      successRate: this.requestCount > 0 ?
        ((this.requestCount - this.errorCount) / this.requestCount * 100).toFixed(2) + '%' :
        '0%'
    };
  }

  /**
   * Сбрасывает статистику
   */
  resetStats() {
    this.requestCount = 0;
    this.errorCount = 0;
  }
}

// Создаем глобальный экземпляр
window.apiClient = new ApiClient();

// Экспорт функций для обратной совместимости (из оригинала)
window.apiRequest = (endpoint, options) => window.apiClient.request(endpoint, options);
window.loadAppSettings = () => window.apiClient.loadAppSettings();
window.loadActualCurrencyData = () => window.apiClient.loadActualCurrencyData();
window.loadUserData = (userId) => window.apiClient.loadUserData(userId);
window.requestWithdrawal = (amount, address, currency, cryptoAmount) =>
  window.apiClient.requestWithdrawal(amount, address, currency, cryptoAmount);
window.loadWithdrawalHistory = () => window.apiClient.getWithdrawalHistory();
window.checkWithdrawalStatuses = () => window.apiClient.checkUserWithdrawals();
window.loadReferralStats = () => window.apiClient.getReferralStats();
window.submitAdView = (adData) => window.apiClient.submitAdView(adData);
window.checkApiHealth = () => window.apiClient.checkApiHealth();
window.getUserDataForAPI = () => window.apiClient.getUserDataForAPI();

console.log('🌐 [ApiClient] API клиент загружен с полной интеграцией.');
