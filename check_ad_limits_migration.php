<?php
/**
 * check_ad_limits_migration.php
 * Проверка миграции лимитов рекламы
 */

declare(strict_types=1);

echo "🔍 ПРОВЕРКА МИГРАЦИИ ЛИМИТОВ РЕКЛАМЫ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ПРОВЕРКА ТАБЛИЦЫ ad_limits:\n";
    
    // Проверяем, существует ли таблица
    $tableExists = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table' AND name='ad_limits'");
    
    if (empty($tableExists)) {
        echo "   ❌ Таблица ad_limits НЕ СУЩЕСТВУЕТ!\n";
        echo "   🔧 Создаем таблицу...\n";
        
        $createTable = "
            CREATE TABLE IF NOT EXISTS ad_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id BIGINT NOT NULL,
                ad_type TEXT NOT NULL,
                daily_count INTEGER DEFAULT 0,
                last_reset_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(telegram_id),
                UNIQUE(user_id, ad_type, last_reset_date)
            )
        ";
        
        $sqlite->query($createTable);
        echo "   ✅ Таблица ad_limits создана\n";
    } else {
        echo "   ✅ Таблица ad_limits существует\n";
    }
    
    // Проверяем данные в таблице
    $limitsCount = $sqlite->query("SELECT COUNT(*) as count FROM ad_limits")[0]['count'];
    echo "   📊 Записей в ad_limits: {$limitsCount}\n";
    
    if ($limitsCount > 0) {
        echo "   📋 Примеры записей:\n";
        $sampleLimits = $sqlite->query("SELECT * FROM ad_limits LIMIT 5");
        foreach ($sampleLimits as $limit) {
            echo "     - User {$limit['user_id']}: {$limit['ad_type']} = {$limit['daily_count']}/{20} (дата: {$limit['last_reset_date']})\n";
        }
    }
    
    echo "\n2. 📄 ПРОВЕРКА JSON ФАЙЛА:\n";
    
    $jsonFile = 'database/ad_limits.json';
    if (file_exists($jsonFile)) {
        echo "   ✅ Файл ad_limits.json существует\n";
        
        $jsonData = json_decode(file_get_contents($jsonFile), true);
        if ($jsonData) {
            echo "   📊 Данные из JSON:\n";
            echo "     - Дневные лимиты:\n";
            foreach ($jsonData['daily_limits'] as $type => $limit) {
                echo "       * {$type}: {$limit}\n";
            }
            
            echo "     - Счетчики пользователей:\n";
            if (isset($jsonData['user_counts'])) {
                foreach ($jsonData['user_counts'] as $userId => $counts) {
                    echo "       * {$userId}:\n";
                    foreach ($counts as $adType => $count) {
                        echo "         - {$adType}: {$count}\n";
                    }
                }
            }
            
            echo "     - Последний сброс: {$jsonData['last_reset_date']}\n";
        } else {
            echo "   ❌ Ошибка декодирования JSON\n";
        }
    } else {
        echo "   ❌ Файл ad_limits.json НЕ СУЩЕСТВУЕТ!\n";
    }
    
    echo "\n3. 🔄 МИГРАЦИЯ ДАННЫХ ИЗ JSON:\n";
    
    if (file_exists($jsonFile) && $jsonData) {
        echo "   🔧 Мигрируем данные из JSON в SQLite...\n";
        
        $migratedCount = 0;
        $today = date('Y-m-d');
        
        // Мигрируем счетчики пользователей
        if (isset($jsonData['user_counts'])) {
            foreach ($jsonData['user_counts'] as $userIdStr => $counts) {
                // Извлекаем ID пользователя из строки "user_7176766994"
                $userId = (int)str_replace('user_', '', $userIdStr);
                
                foreach ($counts as $adType => $count) {
                    // Проверяем, есть ли уже запись
                    $existing = $sqlite->query("
                        SELECT id FROM ad_limits 
                        WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
                    ", [$userId, $adType, $today]);
                    
                    if (empty($existing)) {
                        // Добавляем новую запись
                        $sqlite->query("
                            INSERT INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                            VALUES (?, ?, ?, ?)
                        ", [$userId, $adType, $count, $today]);
                        
                        $migratedCount++;
                        echo "     ✅ Мигрировано: User {$userId}, {$adType} = {$count}\n";
                    } else {
                        echo "     ℹ️ Уже существует: User {$userId}, {$adType}\n";
                    }
                }
            }
        }
        
        echo "   📊 Мигрировано записей: {$migratedCount}\n";
    }
    
    echo "\n4. 🎯 ПРОВЕРКА ЛИМИТОВ ДЛЯ КОНКРЕТНОГО ПОЛЬЗОВАТЕЛЯ:\n";
    
    // Проверяем лимиты для пользователя из JSON
    $testUserId = 7176766994;
    echo "   👤 Проверяем лимиты для пользователя {$testUserId}:\n";
    
    $userLimits = $sqlite->query("
        SELECT ad_type, daily_count, last_reset_date 
        FROM ad_limits 
        WHERE user_id = ? AND last_reset_date = ?
        ORDER BY ad_type
    ", [$testUserId, $today]);
    
    if (!empty($userLimits)) {
        foreach ($userLimits as $limit) {
            $remaining = 20 - $limit['daily_count'];
            echo "     - {$limit['ad_type']}: {$limit['daily_count']}/20 (осталось: {$remaining})\n";
        }
    } else {
        echo "     ℹ️ Нет записей лимитов для этого пользователя\n";
    }
    
    echo "\n5. 🔧 ТЕСТ ФУНКЦИЙ ЛИМИТОВ:\n";
    
    // Тестируем функцию проверки лимитов
    echo "   📊 Тестируем логику лимитов:\n";
    
    $adTypes = ['native_banner', 'rewarded_video', 'interstitial'];
    foreach ($adTypes as $adType) {
        // Получаем текущий счетчик
        $currentLimit = $sqlite->query("
            SELECT daily_count 
            FROM ad_limits 
            WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
        ", [$testUserId, $adType, $today]);
        
        $currentCount = $currentLimit[0]['daily_count'] ?? 0;
        $remaining = 20 - $currentCount;
        $canShow = $remaining > 0;
        
        echo "     - {$adType}: {$currentCount}/20";
        echo $canShow ? " ✅ Можно показать ({$remaining} осталось)" : " ❌ Лимит исчерпан";
        echo "\n";
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ПРОВЕРКИ:\n";
    
    $finalCount = $sqlite->query("SELECT COUNT(*) as count FROM ad_limits")[0]['count'];
    
    if ($finalCount > 0) {
        echo "   ✅ Лимиты мигрированы: {$finalCount} записей\n";
        echo "   ✅ Таблица ad_limits работает\n";
        echo "   ✅ Можно отслеживать лимиты показов\n";
    } else {
        echo "   ⚠️ Лимиты НЕ мигрированы\n";
        echo "   🔧 Нужно добавить миграцию лимитов\n";
    }
    
    echo "   📊 Структура готова для отображения счетчиков в кнопках\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
