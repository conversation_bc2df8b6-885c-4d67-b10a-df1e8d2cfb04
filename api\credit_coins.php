<?php
/**
 * api/credit_coins.php
 * API для начисления монет пользователю за просмотр рекламы
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    $requiredFields = ['initData', 'adType'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $initData = $input['initData'];
    $adType = $input['adType'];
    $adId = $input['adId'] ?? null;
    
    // Валидируем тип рекламы
    $validAdTypes = ['native_banner', 'rewarded_video', 'interstitial'];
    if (!in_array($adType, $validAdTypes)) {
        throw new Exception('Invalid ad type');
    }
    
    // Парсим initData для получения user_id
    require_once __DIR__ . '/../api/telegram_auth.php';
    $telegramAuth = new TelegramAuth();
    $userData = $telegramAuth->validateInitData($initData);
    
    if (!$userData || !isset($userData['user']['id'])) {
        throw new Exception('Invalid Telegram data');
    }
    
    $userId = (string)$userData['user']['id'];
    
    // Инициализируем менеджер монет
    require_once __DIR__ . '/coins_manager.php';
    $coinsManager = new CoinsManager();
    
    // Проверяем лимиты рекламы
    require_once __DIR__ . '/ad_limits_sqlite.php';
    $limitsManager = new AdLimitsSQLite();
    $userLimits = $limitsManager->getUserLimits($userId);
    
    if (!isset($userLimits[$adType]) || !$userLimits[$adType]['can_show']) {
        throw new Exception('Ad limit reached for this type');
    }
    
    // Проверяем дневной лимит заработка
    $dailyLimit = $coinsManager->checkDailyEarnLimit($userId);
    if (!$dailyLimit['can_earn']) {
        throw new Exception('Daily earning limit reached');
    }
    
    // Получаем награду за тип рекламы
    $reward = $coinsManager->getAdReward($adType);
    if ($reward <= 0) {
        throw new Exception('No reward configured for this ad type');
    }
    
    // Проверяем, не превышает ли награда дневной лимит
    if ($reward > $dailyLimit['remaining']) {
        $reward = $dailyLimit['remaining'];
    }
    
    // Записываем просмотр рекламы
    $adViewId = $limitsManager->recordAdView($userId, $adType, $adId);
    
    if (!$adViewId) {
        throw new Exception('Failed to record ad view');
    }
    
    // Начисляем монеты
    $success = $coinsManager->creditCoins(
        $userId,
        $reward,
        'earn',
        'ad_view',
        $adViewId,
        "Reward for viewing {$adType} ad"
    );
    
    if (!$success) {
        throw new Exception('Failed to credit coins');
    }
    
    // Получаем обновленный баланс
    $newBalance = $coinsManager->getUserBalance($userId);
    
    // Получаем обновленные лимиты
    $updatedLimits = $limitsManager->getUserLimits($userId);
    $updatedDailyLimit = $coinsManager->checkDailyEarnLimit($userId);
    
    // Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'message' => 'Coins credited successfully',
        'data' => [
            'reward' => $reward,
            'ad_type' => $adType,
            'ad_view_id' => $adViewId,
            'balance' => [
                'current' => $newBalance['balance'],
                'available' => $newBalance['available_balance'],
                'total_earned' => $newBalance['total_earned']
            ],
            'limits' => [
                'ad_limits' => $updatedLimits[$adType] ?? null,
                'daily_earning' => [
                    'limit' => $updatedDailyLimit['daily_limit'],
                    'earned_today' => $updatedDailyLimit['earned_today'],
                    'remaining' => $updatedDailyLimit['remaining'],
                    'can_earn' => $updatedDailyLimit['can_earn']
                ]
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'CREDIT_COINS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
