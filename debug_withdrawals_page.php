<?php
/**
 * debug_withdrawals_page.php
 * Отладка страницы выводов
 */

declare(strict_types=1);

// Отключаем вывод ошибок в браузер
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ОТЛАДКА СТРАНИЦЫ ВЫВОДОВ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Симулируем параметры фильтрации (пустые = показать все)
    $search = '';
    $status_filter = '';
    $currency_filter = '';
    $date_from = '';
    $date_to = '';
    $user_filter = '';
    
    echo "📊 ЗАГРУЖАЕМ ДАННЫЕ ИЗ SQLITE:\n";
    $sql = "SELECT w.*, u.first_name, u.last_name, u.username 
            FROM user_withdrawals w 
            LEFT JOIN users u ON w.user_id = u.telegram_id 
            ORDER BY w.requested_at DESC";
    
    $allWithdrawals = $sqlite->query($sql);
    echo "✅ Загружено: " . count($allWithdrawals) . " выводов\n\n";
    
    // Преобразуем данные как в withdrawals.php
    echo "🔄 ПРЕОБРАЗУЕМ ДАННЫЕ:\n";
    foreach ($allWithdrawals as &$withdrawal) {
        $withdrawal['user_name'] = $withdrawal['first_name'] ?? 'Неизвестно';
        $withdrawal['user_lastname'] = $withdrawal['last_name'] ?? '';
        $withdrawal['timestamp'] = strtotime($withdrawal['requested_at']);
        $withdrawal['coins_amount'] = $withdrawal['amount'];
        $withdrawal['crypto_address'] = $withdrawal['wallet_address'];
        $withdrawal['payout_id'] = $withdrawal['transaction_hash'];
    }
    echo "✅ Данные преобразованы\n\n";
    
    // Применяем фильтрацию (копируем логику из withdrawals.php)
    echo "🔍 ПРИМЕНЯЕМ ФИЛЬТРАЦИЮ:\n";
    $filteredWithdrawals = array_filter($allWithdrawals, function($withdrawal) use ($search, $status_filter, $currency_filter, $date_from, $date_to, $user_filter) {
        // Поиск по тексту
        if (!empty($search)) {
            $searchText = strtolower($search);
            $searchFields = [
                strtolower($withdrawal['user_name'] ?? ''),
                strtolower($withdrawal['user_lastname'] ?? ''),
                strtolower($withdrawal['username'] ?? ''),
                strtolower($withdrawal['user_id'] ?? ''),
                strtolower($withdrawal['wallet_address'] ?? ''),
                strtolower($withdrawal['currency'] ?? ''),
                strtolower($withdrawal['payout_id'] ?? ''),
                strtolower($withdrawal['transaction_hash'] ?? ''),
            ];
            
            $found = false;
            foreach ($searchFields as $field) {
                if (strpos($field, $searchText) !== false) {
                    $found = true;
                    break;
                }
            }
            if (!$found) return false;
        }
        
        // Фильтр по статусу
        if (!empty($status_filter) && ($withdrawal['status'] ?? '') !== $status_filter) {
            return false;
        }
        
        // Фильтр по валюте
        if (!empty($currency_filter) && ($withdrawal['currency'] ?? '') !== $currency_filter) {
            return false;
        }
        
        // Фильтр по пользователю
        if (!empty($user_filter) && ($withdrawal['user_id'] ?? '') !== $user_filter) {
            return false;
        }
        
        // Фильтр по дате
        if (!empty($date_from)) {
            $from_timestamp = strtotime($date_from);
            if (($withdrawal['timestamp'] ?? 0) < $from_timestamp) {
                return false;
            }
        }
        
        if (!empty($date_to)) {
            $to_timestamp = strtotime($date_to . ' 23:59:59');
            if (($withdrawal['timestamp'] ?? 0) > $to_timestamp) {
                return false;
            }
        }
        
        return true;
    });
    
    echo "✅ После фильтрации: " . count($filteredWithdrawals) . " выводов\n\n";
    
    if (!empty($filteredWithdrawals)) {
        echo "📋 ПЕРВЫЕ 3 ОТФИЛЬТРОВАННЫХ ВЫВОДА:\n";
        $count = 0;
        foreach ($filteredWithdrawals as $w) {
            if ($count >= 3) break;
            echo "  " . ($count + 1) . ". User: {$w['user_name']} ({$w['user_id']})\n";
            echo "     Amount: {$w['amount']} {$w['currency']}\n";
            echo "     Status: {$w['status']}\n";
            echo "     Date: {$w['requested_at']}\n";
            echo "     ---\n";
            $count++;
        }
    } else {
        echo "⚠️ НЕТ ДАННЫХ ПОСЛЕ ФИЛЬТРАЦИИ!\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ОТЛАДКА ЗАВЕРШЕНА!\n";
?>
