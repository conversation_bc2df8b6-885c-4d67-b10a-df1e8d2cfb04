<?php
/**
 * migrate_locales_simple.php
 * Простая миграция переводов из locales в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🌍 МИГРАЦИЯ ПЕРЕВОДОВ В SQLITE\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Очищаем старые тексты
    echo "🗑️ Очищаем старые тексты...\n";
    $sqlite->query("DELETE FROM bot_texts");
    echo "✅ Очищено\n\n";
    
    $totalTexts = 0;
    
    // Обрабатываем русский язык
    echo "📂 Обрабатываем русский язык...\n";
    $ruFile = 'locales/ru.json';
    if (file_exists($ruFile)) {
        $ruData = json_decode(file_get_contents($ruFile), true);
        if ($ruData) {
            $ruCount = 0;
            foreach ($ruData as $section => $texts) {
                if (is_array($texts)) {
                    foreach ($texts as $key => $value) {
                        if (is_string($value)) {
                            $fullKey = $section . '.' . $key;
                            $sql = "INSERT INTO bot_texts (language_code, text_key, text_value, category) VALUES (?, ?, ?, ?)";
                            $sqlite->query($sql, ['ru', $fullKey, $value, $section]);
                            $ruCount++;
                        }
                    }
                }
            }
            echo "✅ Русский: {$ruCount} текстов\n";
            $totalTexts += $ruCount;
        }
    }
    
    // Обрабатываем английский язык
    echo "📂 Обрабатываем английский язык...\n";
    $enFile = 'locales/en.json';
    if (file_exists($enFile)) {
        $enData = json_decode(file_get_contents($enFile), true);
        if ($enData) {
            $enCount = 0;
            foreach ($enData as $section => $texts) {
                if (is_array($texts)) {
                    foreach ($texts as $key => $value) {
                        if (is_string($value)) {
                            $fullKey = $section . '.' . $key;
                            $sql = "INSERT INTO bot_texts (language_code, text_key, text_value, category) VALUES (?, ?, ?, ?)";
                            $sqlite->query($sql, ['en', $fullKey, $value, $section]);
                            $enCount++;
                        }
                    }
                }
            }
            echo "✅ Английский: {$enCount} текстов\n";
            $totalTexts += $enCount;
        }
    }
    
    echo "\n📊 РЕЗУЛЬТАТ:\n";
    echo "  ✅ Всего мигрировано: {$totalTexts} текстов\n";
    
    // Проверяем результат
    $result = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts');
    echo "  📊 В базе данных: " . $result[0]['count'] . " записей\n";
    
    // Статистика по языкам
    $langStats = $sqlite->query('SELECT language_code, COUNT(*) as count FROM bot_texts GROUP BY language_code');
    echo "\n🌍 ПО ЯЗЫКАМ:\n";
    foreach ($langStats as $stat) {
        echo "  - {$stat['language_code']}: {$stat['count']} текстов\n";
    }
    
    // Примеры
    echo "\n📝 ПРИМЕРЫ:\n";
    $samples = $sqlite->query('SELECT language_code, text_key, text_value FROM bot_texts LIMIT 5');
    foreach ($samples as $sample) {
        $value = strlen($sample['text_value']) > 40 ? substr($sample['text_value'], 0, 40) . "..." : $sample['text_value'];
        echo "  [{$sample['language_code']}] {$sample['text_key']}: {$value}\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ГОТОВО!\n";
?>
