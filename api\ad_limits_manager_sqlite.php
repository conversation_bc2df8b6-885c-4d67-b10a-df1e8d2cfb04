<?php
/**
 * api/ad_limits_manager_sqlite.php
 * Менеджер лимитов показов рекламы с использованием SQLite вместо JSON файла
 */

declare(strict_types=1);

require_once __DIR__ . '/ads-config.php';
require_once __DIR__ . '/database_manager.php';

class AdLimitsManagerSqlite 
{
    private DatabaseManager $db;
    private array $configLimits;
    
    public function __construct() 
    {
        $this->db = DatabaseManager::getInstance();
        $this->configLimits = $this->getConfigLimits();
        $this->checkDailyReset();
    }
    
    /**
     * Получает лимиты из config.php
     */
    private function getConfigLimits(): array 
    {
        return [
            'native_banner' => defined('USER_AD_LIMIT_NATIVE_BANNER') ? USER_AD_LIMIT_NATIVE_BANNER : 20,
            'rewarded_video' => defined('USER_AD_LIMIT_REWARDED_VIDEO') ? USER_AD_LIMIT_REWARDED_VIDEO : 20,
            'interstitial' => defined('USER_AD_LIMIT_INTERSTITIAL') ? USER_AD_LIMIT_INTERSTITIAL : 20
        ];
    }
    
    /**
     * Проверяет и выполняет ежедневный сброс лимитов
     */
    private function checkDailyReset(): void 
    {
        try {
            $currentDate = gmdate('Y-m-d'); // UTC время
            
            // Проверяем есть ли записи за сегодня
            $pdo = $this->db->getPdo();
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM ad_limits 
                WHERE last_reset_date = ?
            ");
            $stmt->execute([$currentDate]);
            $result = $stmt->fetch();
            
            // Если нет записей за сегодня, значит нужен сброс
            if ($result['count'] == 0) {
                $this->resetDailyLimits();
                error_log("AdLimitsManagerSqlite: Выполнен ежедневный сброс лимитов для даты {$currentDate}");
            }
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка проверки ежедневного сброса: " . $e->getMessage());
        }
    }
    
    /**
     * Сбрасывает ежедневные лимиты
     */
    public function resetDailyLimits(): bool 
    {
        try {
            $pdo = $this->db->getPdo();
            $currentDate = gmdate('Y-m-d');
            
            // Удаляем старые записи (не за сегодня)
            $stmt = $pdo->prepare("DELETE FROM ad_limits WHERE last_reset_date != ?");
            $stmt->execute([$currentDate]);
            
            error_log("AdLimitsManagerSqlite: Ежедневные лимиты сброшены для даты {$currentDate}");
            return true;
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка сброса лимитов: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получает текущий счетчик пользователя для типа рекламы
     */
    public function getUserAdCount(int $userId, string $adType): int 
    {
        try {
            $pdo = $this->db->getPdo();
            $stmt = $pdo->prepare("
                SELECT daily_count 
                FROM ad_limits 
                WHERE user_id = ? AND ad_type = ? AND last_reset_date = ?
            ");
            $stmt->execute([$userId, $adType, gmdate('Y-m-d')]);
            
            $result = $stmt->fetch();
            return $result ? (int)$result['daily_count'] : 0;
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка получения счетчика пользователя: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Увеличивает счетчик пользователя для типа рекламы
     */
    public function incrementUserAdCount(int $userId, string $adType): int 
    {
        try {
            $currentCount = $this->getUserAdCount($userId, $adType);
            $newCount = $currentCount + 1;
            
            $pdo = $this->db->getPdo();
            $stmt = $pdo->prepare("
                INSERT OR REPLACE INTO ad_limits (user_id, ad_type, daily_count, last_reset_date)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $adType, $newCount, gmdate('Y-m-d')]);
            
            error_log("AdLimitsManagerSqlite: Счетчик пользователя {$userId} для {$adType} увеличен до {$newCount}");
            return $newCount;
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка увеличения счетчика: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Получает оставшееся количество показов для пользователя
     */
    public function getRemainingCount(int $userId, string $adType): int 
    {
        $currentCount = $this->getUserAdCount($userId, $adType);
        $limit = $this->configLimits[$adType] ?? 20;
        
        return max(0, $limit - $currentCount);
    }
    
    /**
     * Получает общее количество просмотров пользователя за день (все типы)
     */
    public function getTotalUserAdCount(int $userId): int 
    {
        $total = 0;
        foreach ($this->configLimits as $adType => $limit) {
            $total += $this->getUserAdCount($userId, $adType);
        }
        return $total;
    }
    
    /**
     * Проверяет, достигнут ли лимит для пользователя и типа рекламы
     */
    public function isLimitReached(int $userId, string $adType): bool 
    {
        $currentCount = $this->getUserAdCount($userId, $adType);
        $limit = $this->configLimits[$adType] ?? 20;
        
        return $currentCount >= $limit;
    }
    
    /**
     * Получает все лимиты пользователя
     */
    public function getUserLimits(int $userId): array 
    {
        $limits = [];
        
        foreach ($this->configLimits as $adType => $maxLimit) {
            $currentCount = $this->getUserAdCount($userId, $adType);
            $limits[$adType] = [
                'current' => $currentCount,
                'limit' => $maxLimit,
                'remaining' => max(0, $maxLimit - $currentCount),
                'reached' => $currentCount >= $maxLimit
            ];
        }
        
        return $limits;
    }
    
    /**
     * Получает статистику по всем пользователям
     */
    public function getAllUsersStats(): array 
    {
        try {
            $pdo = $this->db->getPdo();
            $stmt = $pdo->query("
                SELECT user_id, ad_type, daily_count, last_reset_date
                FROM ad_limits 
                WHERE last_reset_date = ?
                ORDER BY user_id, ad_type
            ");
            $stmt->execute([gmdate('Y-m-d')]);
            
            $stats = [];
            while ($row = $stmt->fetch()) {
                $userId = $row['user_id'];
                if (!isset($stats[$userId])) {
                    $stats[$userId] = [];
                }
                $stats[$userId][$row['ad_type']] = (int)$row['daily_count'];
            }
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка получения статистики: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получает топ пользователей по просмотрам
     */
    public function getTopUsers(int $limit = 10): array 
    {
        try {
            $pdo = $this->db->getPdo();
            $stmt = $pdo->prepare("
                SELECT user_id, SUM(daily_count) as total_views
                FROM ad_limits 
                WHERE last_reset_date = ?
                GROUP BY user_id
                ORDER BY total_views DESC
                LIMIT ?
            ");
            $stmt->execute([gmdate('Y-m-d'), $limit]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка получения топ пользователей: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получает общую статистику за день
     */
    public function getDailyStats(): array 
    {
        try {
            $pdo = $this->db->getPdo();
            
            // Общее количество просмотров по типам
            $stmt = $pdo->prepare("
                SELECT ad_type, SUM(daily_count) as total_count, COUNT(DISTINCT user_id) as unique_users
                FROM ad_limits 
                WHERE last_reset_date = ?
                GROUP BY ad_type
            ");
            $stmt->execute([gmdate('Y-m-d')]);
            
            $stats = [
                'date' => gmdate('Y-m-d'),
                'by_type' => [],
                'totals' => [
                    'total_views' => 0,
                    'unique_users' => 0
                ]
            ];
            
            $allUsers = [];
            
            while ($row = $stmt->fetch()) {
                $stats['by_type'][$row['ad_type']] = [
                    'total_count' => (int)$row['total_count'],
                    'unique_users' => (int)$row['unique_users']
                ];
                $stats['totals']['total_views'] += (int)$row['total_count'];
            }
            
            // Общее количество уникальных пользователей
            $stmt = $pdo->prepare("
                SELECT COUNT(DISTINCT user_id) as unique_users
                FROM ad_limits 
                WHERE last_reset_date = ?
            ");
            $stmt->execute([gmdate('Y-m-d')]);
            $result = $stmt->fetch();
            $stats['totals']['unique_users'] = (int)$result['unique_users'];
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("AdLimitsManagerSqlite: Ошибка получения дневной статистики: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Проверяет корректность данных и исправляет ошибки
     */
    public function validateAndFix(): array 
    {
        $issues = [];
        
        try {
            $pdo = $this->db->getPdo();
            
            // Проверяем отрицательные значения
            $stmt = $pdo->query("
                SELECT COUNT(*) as count 
                FROM ad_limits 
                WHERE daily_count < 0
            ");
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                $issues[] = "Найдены отрицательные значения счетчиков";
                
                // Исправляем
                $pdo->exec("UPDATE ad_limits SET daily_count = 0 WHERE daily_count < 0");
                $issues[] = "Отрицательные значения исправлены";
            }
            
            // Проверяем слишком большие значения
            $maxReasonableLimit = 1000;
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM ad_limits 
                WHERE daily_count > ?
            ");
            $stmt->execute([$maxReasonableLimit]);
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                $issues[] = "Найдены подозрительно большие значения счетчиков";
            }
            
            // Проверяем старые записи
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM ad_limits 
                WHERE last_reset_date != ?
            ");
            $stmt->execute([gmdate('Y-m-d')]);
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                $issues[] = "Найдены устаревшие записи";
                
                // Удаляем старые записи
                $pdo->prepare("DELETE FROM ad_limits WHERE last_reset_date != ?")->execute([gmdate('Y-m-d')]);
                $issues[] = "Устаревшие записи удалены";
            }
            
            if (empty($issues)) {
                $issues[] = "Данные корректны, исправлений не требуется";
            }
            
        } catch (Exception $e) {
            $issues[] = "Ошибка проверки данных: " . $e->getMessage();
        }
        
        return $issues;
    }
    
    /**
     * Получает конфигурационные лимиты
     */
    public function getConfigLimits(): array 
    {
        return $this->configLimits;
    }
    
    /**
     * Экспорт данных для совместимости со старым форматом
     */
    public function exportToLegacyFormat(): array 
    {
        $stats = $this->getAllUsersStats();
        
        $legacyFormat = [
            'daily_limits' => $this->configLimits,
            'user_counts' => [],
            'last_reset_date' => gmdate('Y-m-d'),
            'version' => '2.0-sqlite'
        ];
        
        foreach ($stats as $userId => $adCounts) {
            $legacyFormat['user_counts']["user_{$userId}"] = $adCounts;
        }
        
        return $legacyFormat;
    }
}
?>
