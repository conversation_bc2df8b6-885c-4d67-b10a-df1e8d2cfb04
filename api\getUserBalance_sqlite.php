<?php
/**
 * api/getUserBalance_sqlite.php
 * API для получения баланса пользователя из SQLite (совместимость с фронтендом)
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $userId = null;
    
    // Поддерживаем как GET, так и POST запросы
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Если есть initData, парсим его
        if (isset($input['initData'])) {
            require_once __DIR__ . '/validate_initdata.php';
            $userData = validateInitData($input['initData']);
            
            if (!$userData || !isset($userData['user']['id'])) {
                throw new Exception('Invalid Telegram data');
            }
            
            $userId = (string)$userData['user']['id'];
        } elseif (isset($input['user_id'])) {
            $userId = (string)$input['user_id'];
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        
        // Поддержка старого формата с initData в GET
        if (!$userId && isset($_GET['initData'])) {
            require_once __DIR__ . '/validate_initdata.php';
            $userData = validateInitData($_GET['initData']);
            
            if ($userData && isset($userData['user']['id'])) {
                $userId = (string)$userData['user']['id'];
            }
        }
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Инициализируем менеджер монет
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Получаем баланс пользователя из SQLite
    $balance = $coinsManager->getUserBalance($userId);
    
    // Получаем дополнительную информацию о пользователе
    $userInfo = $db->query(
        "SELECT first_name, last_name, username, withdrawals_count, referrals_count, 
                registered_at, last_activity, blocked, suspicious_activity_count
         FROM users WHERE telegram_id = ?",
        [$userId]
    );
    
    $user = !empty($userInfo) ? $userInfo[0] : null;
    
    // Получаем настройки системы
    $minWithdrawal = (float)$coinsManager->getSetting('min_withdrawal_coins', '1000');
    $coinRate = (float)$coinsManager->getSetting('coin_rate_usd', '0.001');
    $minBalanceForWithdrawal = (float)$coinsManager->getSetting('min_balance_for_withdrawal', '100');
    
    // Получаем статистику выводов
    $withdrawalStats = $db->query(
        "SELECT 
            COUNT(*) as total_withdrawals,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_withdrawals,
            MAX(requested_at) as last_withdrawal_date
         FROM user_withdrawals WHERE user_id = ?",
        [$userId]
    );
    
    $withdrawalData = !empty($withdrawalStats) ? $withdrawalStats[0] : [
        'total_withdrawals' => 0,
        'completed_amount' => 0,
        'pending_withdrawals' => 0,
        'last_withdrawal_date' => null
    ];
    
    // Получаем дневную статистику заработка
    $dailyStats = $coinsManager->checkDailyEarnLimit($userId);
    
    // Получаем последние транзакции
    $recentTransactions = $coinsManager->getUserTransactions($userId, 5);
    
    // Форматируем транзакции
    $formattedTransactions = [];
    foreach ($recentTransactions as $transaction) {
        $formattedTransactions[] = [
            'id' => (int)$transaction['id'],
            'type' => $transaction['transaction_type'],
            'amount' => (float)$transaction['amount'],
            'operation' => $transaction['operation'],
            'description' => $transaction['description'],
            'created_at' => $transaction['created_at'],
            'amount_display' => ($transaction['operation'] === 'credit' ? '+' : '-') . $transaction['amount']
        ];
    }
    
    // Проверяем возможность вывода
    $canWithdraw = $balance['available_balance'] >= $minWithdrawal && 
                   $balance['balance'] >= $minBalanceForWithdrawal &&
                   (!$user || !$user['blocked']);
    
    // Возвращаем данные в формате, совместимом с фронтендом
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'balance' => $balance['balance'], // Основной баланс для совместимости
        'available_balance' => $balance['available_balance'],
        'reserved_balance' => $balance['reserved_balance'],
        'total_earned' => $balance['total_earned'],
        'total_withdrawn' => $balance['total_withdrawn'],
        'balance_usd' => round($balance['available_balance'] * $coinRate, 4),
        
        // Информация о пользователе
        'user_info' => $user ? [
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'username' => $user['username'],
            'withdrawals_count' => (int)$user['withdrawals_count'],
            'referrals_count' => (int)$user['referrals_count'],
            'registered_at' => $user['registered_at'],
            'last_activity' => $user['last_activity'],
            'blocked' => (bool)$user['blocked'],
            'suspicious_activity_count' => (int)$user['suspicious_activity_count']
        ] : null,
        
        // Статистика выводов
        'withdrawal_stats' => [
            'total_withdrawals' => (int)$withdrawalData['total_withdrawals'],
            'completed_amount' => (float)$withdrawalData['completed_amount'],
            'pending_withdrawals' => (int)$withdrawalData['pending_withdrawals'],
            'last_withdrawal_date' => $withdrawalData['last_withdrawal_date']
        ],
        
        // Дневная статистика
        'daily_stats' => [
            'limit' => $dailyStats['daily_limit'],
            'earned_today' => $dailyStats['earned_today'],
            'remaining' => $dailyStats['remaining'],
            'can_earn' => $dailyStats['can_earn'],
            'progress_percent' => round(($dailyStats['earned_today'] / $dailyStats['daily_limit']) * 100, 1)
        ],
        
        // Настройки вывода
        'withdrawal_settings' => [
            'min_withdrawal' => $minWithdrawal,
            'min_balance_for_access' => $minBalanceForWithdrawal,
            'coin_rate_usd' => $coinRate,
            'can_withdraw' => $canWithdraw,
            'withdrawal_available_usd' => round($balance['available_balance'] * $coinRate, 4)
        ],
        
        // Последние транзакции
        'recent_transactions' => $formattedTransactions,
        
        // Системная информация
        'system_info' => [
            'timestamp' => time(),
            'server_time' => date('Y-m-d H:i:s'),
            'database_type' => 'sqlite'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'GET_BALANCE_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
