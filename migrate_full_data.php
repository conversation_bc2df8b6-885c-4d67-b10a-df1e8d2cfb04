<?php
/**
 * migrate_full_data.php
 * Полная миграция актуальных данных из JSON файлов в SQLite базу данных
 */

declare(strict_types=1);

echo "🚀 ПОЛНАЯ МИГРАЦИЯ АКТУАЛЬНЫХ ДАННЫХ В SQLITE\n";
echo "==============================================\n\n";

try {
    // Подключаем необходимые классы
    require_once 'database/sqlite_manager_clean.php';
    require_once 'api/coins_manager.php';

    $db = new SQLiteManagerClean();
    $coinsManager = new CoinsManager();
    
    echo "✅ Подключение к базе данных установлено\n";
    
    // Проверяем существующие данные
    $existingUsers = $db->query("SELECT COUNT(*) as count FROM users")[0]['count'];
    $existingTransactions = $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'];
    
    echo "📊 Текущее состояние базы:\n";
    echo "   - Пользователей: {$existingUsers}\n";
    echo "   - Транзакций: {$existingTransactions}\n\n";
    
    // 1. МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ
    echo "1. 👥 МИГРАЦИЯ ПОЛЬЗОВАТЕЛЕЙ\n";
    echo "============================\n";
    
    $userDataFile = 'database/user_data.json';
    if (!file_exists($userDataFile)) {
        throw new Exception("❌ Файл user_data.json не найден");
    }
    
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (!$userData) {
        throw new Exception("❌ Не удалось прочитать user_data.json");
    }
    
    echo "📁 Найдено " . count($userData) . " пользователей в JSON файле\n\n";
    
    $migratedUsers = 0;
    $updatedUsers = 0;
    $skippedUsers = 0;
    
    foreach ($userData as $telegramId => $user) {
        try {
            // Проверяем, существует ли пользователь
            $existingUser = $db->query(
                "SELECT id, balance, total_earned FROM users WHERE telegram_id = ?",
                [$telegramId]
            );
            
            if (!empty($existingUser)) {
                // Обновляем существующего пользователя
                $db->query(
                    "UPDATE users SET 
                        username = ?, first_name = ?, last_name = ?, 
                        language_code = ?, balance = ?, total_earned = ?,
                        referrer_id = ?, referral_earnings = ?, 
                        withdrawals_count = ?, suspicious_activity_count = ?,
                        is_blocked = ?, updated_at = ?
                     WHERE telegram_id = ?",
                    [
                        $user['username'] ?? null,
                        $user['first_name'] ?? '',
                        $user['last_name'] ?? '',
                        $user['language'] ?? 'ru',
                        $user['balance'] ?? 0,
                        $user['total_earned'] ?? 0,
                        $user['referrer_id'] ?? null,
                        $user['referral_earnings'] ?? 0,
                        $user['withdrawals_count'] ?? 0,
                        $user['suspicious_activity_count'] ?? 0,
                        $user['blocked'] ?? false ? 1 : 0,
                        date('Y-m-d H:i:s'),
                        $telegramId
                    ]
                );
                
                $updatedUsers++;
                echo "   🔄 Обновлен пользователь {$telegramId} ({$user['first_name']})\n";
            } else {
                // Создаем нового пользователя
                $db->query(
                    "INSERT INTO users (
                        telegram_id, username, first_name, last_name, 
                        language_code, is_bot, is_premium, 
                        balance, total_earned, referrer_id, 
                        referral_earnings, withdrawals_count, 
                        suspicious_activity_count, is_blocked, 
                        blocked_at, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $telegramId,
                        $user['username'] ?? null,
                        $user['first_name'] ?? '',
                        $user['last_name'] ?? '',
                        $user['language'] ?? 'ru',
                        0, // is_bot
                        0, // is_premium
                        $user['balance'] ?? 0,
                        $user['total_earned'] ?? 0,
                        $user['referrer_id'] ?? null,
                        $user['referral_earnings'] ?? 0,
                        $user['withdrawals_count'] ?? 0,
                        $user['suspicious_activity_count'] ?? 0,
                        $user['blocked'] ?? false ? 1 : 0,
                        isset($user['blocked_at']) ? date('Y-m-d H:i:s', $user['blocked_at']) : null,
                        isset($user['registered_at']) ? date('Y-m-d H:i:s', $user['registered_at']) : date('Y-m-d H:i:s'),
                        date('Y-m-d H:i:s')
                    ]
                );
                
                $migratedUsers++;
                echo "   ✅ Создан пользователь {$telegramId} ({$user['first_name']})\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ Ошибка с пользователем {$telegramId}: " . $e->getMessage() . "\n";
            $skippedUsers++;
        }
    }
    
    echo "\n📊 Результат миграции пользователей:\n";
    echo "   ✅ Создано новых: {$migratedUsers}\n";
    echo "   🔄 Обновлено: {$updatedUsers}\n";
    echo "   ❌ Пропущено: {$skippedUsers}\n\n";
    
    // 2. МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ
    echo "2. 📺 МИГРАЦИЯ ПРОСМОТРОВ РЕКЛАМЫ\n";
    echo "=================================\n";
    
    $adViewsFile = 'database/ad_views.json';
    if (file_exists($adViewsFile)) {
        $adViews = json_decode(file_get_contents($adViewsFile), true);
        
        if ($adViews) {
            echo "📁 Найдено " . count($adViews) . " просмотров в JSON файле\n\n";
            
            $migratedViews = 0;
            $skippedViews = 0;
            
            foreach ($adViews as $index => $view) {
                try {
                    $timestamp = $view['timestamp'];
                    $userId = (string)$view['user_id'];
                    $reward = (float)$view['reward'];
                    $adType = $view['ad_type'];
                    
                    // Проверяем дубликаты по времени и пользователю
                    $existing = $db->query(
                        "SELECT id FROM coin_transactions 
                         WHERE user_id = ? AND created_at = ? AND amount = ? AND transaction_type = 'ad_reward'",
                        [$userId, $timestamp, $reward]
                    );
                    
                    if (!empty($existing)) {
                        $skippedViews++;
                        continue;
                    }
                    
                    // Создаем транзакцию
                    $db->query(
                        "INSERT INTO coin_transactions (
                            user_id, amount, operation, transaction_type, 
                            from_user_id, to_user_id, description, metadata, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            $userId,
                            $reward,
                            'credit',
                            'ad_reward',
                            'system',
                            null,
                            "Ad reward for {$adType}",
                            json_encode([
                                'ad_type' => $adType,
                                'ip_address' => $view['ip'] ?? 'unknown',
                                'user_agent' => substr($view['user_agent'] ?? 'unknown', 0, 500),
                                'migrated_from' => 'ad_views.json'
                            ]),
                            $timestamp
                        ]
                    );
                    
                    $migratedViews++;
                    
                    if ($migratedViews % 100 == 0) {
                        echo "   📊 Обработано {$migratedViews} просмотров...\n";
                    }
                    
                } catch (Exception $e) {
                    echo "   ❌ Ошибка миграции просмотра #{$index}: " . $e->getMessage() . "\n";
                    $skippedViews++;
                }
            }
            
            echo "\n📊 Результат миграции просмотров:\n";
            echo "   ✅ Мигрировано: {$migratedViews}\n";
            echo "   ⏭️  Пропущено: {$skippedViews}\n\n";
        }
    } else {
        echo "   ⚠️  Файл ad_views.json не найден\n\n";
    }
    
    // 3. МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ
    echo "3. 🖱️  МИГРАЦИЯ КЛИКОВ ПО РЕКЛАМЕ\n";
    echo "=================================\n";
    
    $adClicksFile = 'database/ad_clicks.json';
    if (file_exists($adClicksFile)) {
        $adClicks = json_decode(file_get_contents($adClicksFile), true);
        
        if ($adClicks) {
            echo "📁 Найдено " . count($adClicks) . " кликов в JSON файле\n\n";
            
            // Создаем таблицу для кликов если не существует
            $db->query("
                CREATE TABLE IF NOT EXISTS ad_clicks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id BIGINT NOT NULL,
                    ad_type TEXT NOT NULL,
                    click_type TEXT NOT NULL,
                    reason TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ");
            
            // Создаем индексы
            $db->query("CREATE INDEX IF NOT EXISTS idx_ad_clicks_user_id ON ad_clicks(user_id)");
            $db->query("CREATE INDEX IF NOT EXISTS idx_ad_clicks_created_at ON ad_clicks(created_at)");
            
            $migratedClicks = 0;
            $skippedClicks = 0;
            
            foreach ($adClicks as $index => $click) {
                try {
                    $timestamp = $click['timestamp'];
                    $userId = (string)$click['user_id'];
                    
                    // Проверяем дубликаты
                    $existing = $db->query(
                        "SELECT id FROM ad_clicks 
                         WHERE user_id = ? AND created_at = ? AND click_type = ? AND ad_type = ?",
                        [$userId, $timestamp, $click['click_type'], $click['ad_type']]
                    );
                    
                    if (!empty($existing)) {
                        $skippedClicks++;
                        continue;
                    }
                    
                    $db->query(
                        "INSERT INTO ad_clicks (user_id, ad_type, click_type, reason, ip_address, user_agent, created_at)
                         VALUES (?, ?, ?, ?, ?, ?, ?)",
                        [
                            $userId,
                            $click['ad_type'],
                            $click['click_type'],
                            $click['reason'] ?? null,
                            $click['ip'] ?? 'unknown',
                            substr($click['user_agent'] ?? 'unknown', 0, 500),
                            $timestamp
                        ]
                    );
                    
                    $migratedClicks++;
                    
                    if ($migratedClicks % 500 == 0) {
                        echo "   📊 Обработано {$migratedClicks} кликов...\n";
                    }
                    
                } catch (Exception $e) {
                    echo "   ❌ Ошибка миграции клика #{$index}: " . $e->getMessage() . "\n";
                    $skippedClicks++;
                }
            }
            
            echo "\n📊 Результат миграции кликов:\n";
            echo "   ✅ Мигрировано: {$migratedClicks}\n";
            echo "   ⏭️  Пропущено: {$skippedClicks}\n\n";
        }
    } else {
        echo "   ⚠️  Файл ad_clicks.json не найден\n\n";
    }

    // 4. СОЗДАНИЕ ТЕСТОВЫХ ВЫПЛАТ
    echo "4. 💰 СОЗДАНИЕ ТЕСТОВЫХ ВЫПЛАТ\n";
    echo "==============================\n";

    // Находим пользователей с наибольшим балансом для создания тестовых выплат
    $topUsers = $db->query(
        "SELECT telegram_id, first_name, balance
         FROM users
         WHERE balance > 50
         ORDER BY balance DESC
         LIMIT 5"
    );

    if (!empty($topUsers)) {
        echo "👥 Найдено " . count($topUsers) . " пользователей с балансом > 50 монет\n";
        echo "💡 Создаем тестовые выплаты для демонстрации...\n\n";

        $testWithdrawals = 0;
        $statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];

        foreach ($topUsers as $index => $user) {
            try {
                $userId = $user['telegram_id'];
                $amount = min($user['balance'] * 0.3, 100); // 30% от баланса, но не больше 100
                $status = $statuses[$index % count($statuses)];

                // Создаем тестовую выплату
                $db->query(
                    "INSERT INTO user_withdrawals (
                        user_id, amount, currency, wallet_address, status,
                        payout_id, transaction_hash, network_fee, final_amount,
                        admin_notes, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $userId,
                        $amount,
                        'TON',
                        'EQD' . bin2hex(random_bytes(24)), // Генерируем тестовый адрес
                        $status,
                        $status === 'completed' ? 'payout_' . time() . '_' . $index : null,
                        $status === 'completed' ? hash('sha256', $userId . $amount . time()) : null,
                        $status === 'completed' ? round($amount * 0.01, 2) : null, // 1% комиссия
                        $status === 'completed' ? round($amount * 0.99, 2) : null,
                        "Тестовая выплата для демонстрации системы (статус: {$status})",
                        date('Y-m-d H:i:s', time() - rand(3600, 86400)), // От 1 часа до 1 дня назад
                        date('Y-m-d H:i:s')
                    ]
                );

                $testWithdrawals++;
                echo "   ✅ Создана тестовая выплата для {$user['first_name']} ({$amount} TON, статус: {$status})\n";

            } catch (Exception $e) {
                echo "   ❌ Ошибка создания выплаты для {$user['first_name']}: " . $e->getMessage() . "\n";
            }
        }

        echo "\n📊 Создано тестовых выплат: {$testWithdrawals}\n\n";
    } else {
        echo "   ⚠️  Нет пользователей с достаточным балансом для тестовых выплат\n\n";
    }

    // 5. ФИНАЛЬНАЯ СТАТИСТИКА
    echo "5. 📊 ФИНАЛЬНАЯ СТАТИСТИКА\n";
    echo "==========================\n";

    $finalUsers = $db->query("SELECT COUNT(*) as count FROM users")[0]['count'];
    $finalTransactions = $db->query("SELECT COUNT(*) as count FROM coin_transactions")[0]['count'];
    $finalClicks = $db->query("SELECT COUNT(*) as count FROM ad_clicks")[0]['count'] ?? 0;
    $finalWithdrawals = $db->query("SELECT COUNT(*) as count FROM user_withdrawals")[0]['count'] ?? 0;
    $totalBalance = $db->query("SELECT SUM(balance) as total FROM users")[0]['total'] ?? 0;
    $totalEarned = $db->query("SELECT SUM(total_earned) as total FROM users")[0]['total'] ?? 0;

    echo "   👥 Всего пользователей: {$finalUsers}\n";
    echo "   💰 Всего транзакций: {$finalTransactions}\n";
    echo "   🖱️  Всего кликов: {$finalClicks}\n";
    echo "   💸 Всего выплат: {$finalWithdrawals}\n";
    echo "   💎 Общий баланс: " . number_format($totalBalance, 2) . " монет\n";
    echo "   🏆 Всего заработано: " . number_format($totalEarned, 2) . " монет\n\n";

    // Статистика по статусам выплат
    if ($finalWithdrawals > 0) {
        echo "📋 Статистика выплат по статусам:\n";
        $withdrawalStats = $db->query(
            "SELECT status, COUNT(*) as count, SUM(amount) as total_amount
             FROM user_withdrawals
             GROUP BY status
             ORDER BY count DESC"
        );

        foreach ($withdrawalStats as $stat) {
            echo "   - {$stat['status']}: {$stat['count']} шт. ({$stat['total_amount']} TON)\n";
        }
        echo "\n";
    }

    echo "🎉 МИГРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!\n";
    echo "==============================\n";
    echo "✅ Все данные из JSON файлов успешно перенесены в SQLite базу данных\n";
    echo "✅ Созданы тестовые выплаты для демонстрации системы\n";
    echo "✅ База данных готова для тестирования системы выплат\n\n";

    echo "🚀 СЛЕДУЮЩИЕ ШАГИ:\n";
    echo "1. Откройте demo_withdrawal_history.html для тестирования истории выплат\n";
    echo "2. Проверьте автообновление каждые 15 секунд\n";
    echo "3. Протестируйте создание новых выплат через систему\n\n";

} catch (Exception $e) {
    echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
