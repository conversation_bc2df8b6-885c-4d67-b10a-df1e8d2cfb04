<?php
/**
 * update_admin_to_sqlite.php
 * Автоматическое обновление всех файлов админки для использования SQLite
 */

declare(strict_types=1);

echo "🔄 ОБНОВЛЕНИЕ АДМИНКИ ДЛЯ SQLITE\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Список файлов для обновления
$filesToUpdate = [
    'api/admin/users.php',
    'api/admin/stats.php',
    'api/admin/user_detail.php',
    'api/admin/balance.php',
    'api/admin/withdrawals.php',
    'api/admin/monitor.php',
    'api/admin/diagnose.php',
    'api/admin/ad_statistics.php'
];

$updatedCount = 0;
$errorCount = 0;

foreach ($filesToUpdate as $file) {
    $fullPath = __DIR__ . '/' . $file;
    
    if (!file_exists($fullPath)) {
        echo "  ⚠️ Файл не найден: {$file}\n";
        continue;
    }
    
    echo "📝 Обновляем: {$file}\n";
    
    try {
        $content = file_get_contents($fullPath);
        $originalContent = $content;
        
        // Заменяем подключения db_mock.php на db_mock_final_sqlite.php
        $content = str_replace(
            "require_once __DIR__ . '/../db_mock.php'",
            "require_once __DIR__ . '/../db_mock_final_sqlite.php'",
            $content
        );
        
        $content = str_replace(
            'require_once __DIR__ . "/../db_mock.php"',
            'require_once __DIR__ . "/../db_mock_final_sqlite.php"',
            $content
        );
        
        $content = str_replace(
            "include_once __DIR__ . '/../db_mock.php'",
            "include_once __DIR__ . '/../db_mock_final_sqlite.php'",
            $content
        );
        
        // Заменяем прямые обращения к JSON файлам
        $content = str_replace(
            '/database/ad_clicks.json',
            '/* УСТАРЕЛО: использует SQLite */',
            $content
        );
        
        $content = str_replace(
            '/database/ad_views.json',
            '/* УСТАРЕЛО: использует SQLite */',
            $content
        );
        
        $content = str_replace(
            '/database/ad_tokens.json',
            '/* УСТАРЕЛО: использует SQLite */',
            $content
        );
        
        $content = str_replace(
            '/database/ad_limits.json',
            '/* УСТАРЕЛО: использует SQLite */',
            $content
        );
        
        // Добавляем комментарий об обновлении
        if (strpos($content, 'ОБНОВЛЕНО: Использует SQLite') === false) {
            $content = str_replace(
                '<?php',
                "<?php\n/**\n * ОБНОВЛЕНО: Использует SQLite базу данных\n * Дата обновления: " . date('Y-m-d H:i:s') . "\n */",
                $content
            );
        }
        
        // Сохраняем файл только если были изменения
        if ($content !== $originalContent) {
            if (file_put_contents($fullPath, $content) !== false) {
                echo "  ✅ Обновлен: {$file}\n";
                $updatedCount++;
            } else {
                echo "  ❌ Ошибка сохранения: {$file}\n";
                $errorCount++;
            }
        } else {
            echo "  ℹ️ Без изменений: {$file}\n";
        }
        
    } catch (Exception $e) {
        echo "  ❌ Ошибка обработки {$file}: " . $e->getMessage() . "\n";
        $errorCount++;
    }
}

echo "\n📊 РЕЗУЛЬТАТЫ ОБНОВЛЕНИЯ:\n";
echo "  ✅ Обновлено файлов: {$updatedCount}\n";
echo "  ❌ Ошибок: {$errorCount}\n";

// Проверяем, что основные файлы используют SQLite
echo "\n🔍 ПРОВЕРКА КЛЮЧЕВЫХ ФАЙЛОВ:\n";

$keyFiles = [
    'api/db_mock_final_sqlite.php' => 'Основной SQLite API',
    'database/app.sqlite' => 'SQLite база данных',
    'database/real_sqlite_manager.php' => 'SQLite менеджер'
];

foreach ($keyFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "  ✅ {$description}: {$file} (" . number_format($size) . " bytes)\n";
    } else {
        echo "  ❌ ОТСУТСТВУЕТ: {$description}: {$file}\n";
    }
}

echo "\n🎯 РЕКОМЕНДАЦИИ:\n";
echo "  1. Проверьте админку в браузере\n";
echo "  2. Убедитесь, что статистика загружается из SQLite\n";
echo "  3. Проверьте, что пользователи отображаются корректно\n";
echo "  4. Удалите старые JSON файлы если они больше не нужны\n";

echo "\n✅ ОБНОВЛЕНИЕ ЗАВЕРШЕНО!\n";
echo "📝 Все файлы админки теперь используют SQLite базу данных\n";

echo "\nДата обновления: " . date('Y-m-d H:i:s') . "\n";
?>
