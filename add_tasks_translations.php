<?php
/**
 * add_tasks_translations.php
 * Добавление переводов для кнопок задач
 */

declare(strict_types=1);

echo "🎯 ДОБАВЛЕНИЕ ПЕРЕВОДОВ ДЛЯ КНОПОК ЗАДАЧ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    // Переводы для кнопок задач
    $taskTranslations = [
        'tasks.open_link' => ['ru' => 'Открыть ссылку', 'en' => 'Open Link'],
        'tasks.watch_video' => ['ru' => 'Смотреть видео', 'en' => 'Watch Video'],
        'tasks.watch_ad' => ['ru' => 'Смотреть рекламу', 'en' => 'Watch Ad'],
        'tasks.earn_coins' => ['ru' => 'Заработать монеты', 'en' => 'Earn <PERSON>ins'],
        'tasks.complete_task' => ['ru' => 'Выполнить задание', 'en' => 'Complete Task'],
        'tasks.get_reward' => ['ru' => 'Получить награду', 'en' => 'Get Reward'],
        'tasks.view_banner' => ['ru' => 'Просмотреть баннер', 'en' => 'View Banner'],
        'tasks.click_ad' => ['ru' => 'Кликнуть рекламу', 'en' => 'Click Ad']
    ];
    
    echo "📝 Добавляем переводы для кнопок задач...\n";
    
    $added = 0;
    foreach ($taskTranslations as $key => $langs) {
        foreach ($langs as $lang => $text) {
            // Проверяем, есть ли уже такой ключ
            $existing = $sqlite->query("SELECT id FROM bot_texts WHERE text_key = ? AND language_code = ?", [$key, $lang]);
            
            if (empty($existing)) {
                // Добавляем перевод
                $sqlite->query("
                    INSERT INTO bot_texts (text_key, text_value, language_code, category)
                    VALUES (?, ?, ?, 'tasks')
                ", [$key, $text, $lang]);
                
                echo "✅ [{$lang}] {$key}: {$text}\n";
                $added++;
            } else {
                echo "⏭️ [{$lang}] {$key} (уже существует)\n";
            }
        }
    }
    
    echo "\n📊 РЕЗУЛЬТАТ:\n";
    echo "✅ Добавлено переводов: {$added}\n";
    
    // Проверяем общее количество
    $total = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts')[0]['count'];
    echo "📝 Всего переводов в базе: {$total}\n";
    
    // Проверяем категорию tasks
    $tasksCount = $sqlite->query("SELECT COUNT(*) as count FROM bot_texts WHERE category = 'tasks'")[0]['count'];
    echo "🎯 Переводов в категории 'tasks': {$tasksCount}\n";
    
    echo "\n🌐 Тестируем API...\n";
    $response = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=ru');
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['app']['tasks'])) {
            echo "✅ API работает, категория 'tasks' найдена\n";
            echo "📊 Переводов в tasks: " . count($data['app']['tasks']) . "\n";
            
            // Показываем примеры
            foreach ($data['app']['tasks'] as $key => $value) {
                echo "   - {$key}: {$value}\n";
            }
        } else {
            echo "❌ Категория 'tasks' не найдена в API\n";
        }
    }
    
    echo "\n✅ ГОТОВО!\n";
    echo "🔄 Обновите страницу в браузере\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
