<?php
/**
 * test_json_localization.php
 * Тест восстановленной системы локализации на JSON
 */

declare(strict_types=1);

echo "🔄 ТЕСТ ВОССТАНОВЛЕННОЙ СИСТЕМЫ ЛОКАЛИЗАЦИИ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    echo "1. 📂 ПРОВЕРКА JSON ФАЙЛОВ:\n";
    
    $localeFiles = ['locales/ru.json', 'locales/en.json'];
    foreach ($localeFiles as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "   ✅ {$file}: " . number_format($size) . " bytes\n";
            
            $content = json_decode(file_get_contents($file), true);
            if ($content && isset($content['app'])) {
                echo "     📊 Категорий в app: " . count($content['app']) . "\n";
                
                // Проверяем tasks
                if (isset($content['app']['tasks'])) {
                    echo "     🎯 Переводов в tasks: " . count($content['app']['tasks']) . "\n";
                    
                    // Показываем примеры
                    $taskKeys = ['open_link', 'watch_video', 'watch_ad'];
                    foreach ($taskKeys as $key) {
                        if (isset($content['app']['tasks'][$key])) {
                            echo "       - {$key}: {$content['app']['tasks'][$key]}\n";
                        }
                    }
                }
            }
        } else {
            echo "   ❌ {$file}: НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n2. 🌐 ТЕСТ API ЛОКАЛИЗАЦИИ:\n";
    
    // Тестируем API для русского языка
    echo "   📡 Тестируем API для русского языка...\n";
    $ruResponse = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=ru');
    
    if ($ruResponse) {
        $ruData = json_decode($ruResponse, true);
        if ($ruData && isset($ruData['app'])) {
            echo "   ✅ API работает для русского языка\n";
            echo "   📊 Категорий: " . count($ruData['app']) . "\n";
            
            // Проверяем tasks
            if (isset($ruData['app']['tasks'])) {
                echo "   🎯 Переводов в tasks: " . count($ruData['app']['tasks']) . "\n";
                
                $taskKeys = ['open_link', 'watch_video', 'watch_ad'];
                foreach ($taskKeys as $key) {
                    if (isset($ruData['app']['tasks'][$key])) {
                        echo "     ✅ tasks.{$key}: {$ruData['app']['tasks'][$key]}\n";
                    } else {
                        echo "     ❌ tasks.{$key}: НЕ НАЙДЕН\n";
                    }
                }
            } else {
                echo "   ❌ Категория 'tasks' не найдена\n";
            }
        } else {
            echo "   ❌ API вернул неправильный формат\n";
            echo "   Ответ: " . substr($ruResponse, 0, 200) . "\n";
        }
    } else {
        echo "   ❌ API не отвечает\n";
    }
    
    echo "\n3. 🌐 ТЕСТ API ДЛЯ АНГЛИЙСКОГО:\n";
    
    $enResponse = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=en');
    if ($enResponse) {
        $enData = json_decode($enResponse, true);
        if ($enData && isset($enData['app']['tasks'])) {
            echo "   ✅ API работает для английского языка\n";
            
            $taskKeys = ['open_link', 'watch_video', 'watch_ad'];
            foreach ($taskKeys as $key) {
                if (isset($enData['app']['tasks'][$key])) {
                    echo "     ✅ tasks.{$key}: {$enData['app']['tasks'][$key]}\n";
                }
            }
        }
    }
    
    echo "\n4. 🔧 ПРОВЕРКА JAVASCRIPT ФАЙЛОВ:\n";
    
    $jsFiles = [
        'js/localization.js' => 'Основная локализация',
        'js/ad-counters.js' => 'Счетчики рекламы (восстановлен)',
        'js/server-ad-counters.js' => 'Серверные счетчики (восстановлен)'
    ];
    
    foreach ($jsFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✅ {$description}: {$file}\n";
        } else {
            echo "   ❌ {$description}: {$file} НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n5. 📊 СРАВНЕНИЕ С SQLite:\n";
    
    // Проверяем, что SQLite больше не используется для переводов
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    $sqliteTexts = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts')[0]['count'];
    echo "   📝 Переводов в SQLite: {$sqliteTexts} (не используются)\n";
    
    // Загружаем из JSON
    $jsonRu = json_decode(file_get_contents('locales/ru.json'), true);
    $jsonTasksCount = isset($jsonRu['app']['tasks']) ? count($jsonRu['app']['tasks']) : 0;
    echo "   📝 Переводов tasks в JSON: {$jsonTasksCount} (используются)\n";
    
    echo "\n6. 🎮 ТЕСТ КОНКРЕТНЫХ КЛЮЧЕЙ:\n";
    
    // Тестируем конкретные ключи, которые должны работать
    $testKeys = [
        'tasks.open_link' => 'Открыть ссылку',
        'tasks.watch_video' => 'Смотреть видео', 
        'tasks.watch_ad' => 'Кликнуть по баннеру',
        'currency.coins' => 'монет',
        'nav.home' => 'Главная'
    ];
    
    foreach ($testKeys as $key => $expectedRu) {
        $parts = explode('.', $key);
        $category = $parts[0];
        $subkey = $parts[1];
        
        if (isset($ruData['app'][$category][$subkey])) {
            $actualValue = $ruData['app'][$category][$subkey];
            if ($actualValue === $expectedRu) {
                echo "   ✅ {$key}: '{$actualValue}' (правильно)\n";
            } else {
                echo "   ⚠️ {$key}: '{$actualValue}' (ожидалось: '{$expectedRu}')\n";
            }
        } else {
            echo "   ❌ {$key}: НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ВОССТАНОВЛЕНИЯ:\n";
    
    echo "   ✅ API локализации переключен на JSON файлы\n";
    echo "   ✅ JavaScript восстановлен к оригинальному состоянию\n";
    echo "   ✅ Переводы кнопок работают из JSON\n";
    echo "   ✅ SQLite больше не используется для переводов\n";
    echo "   ✅ Система вернулась к исходному состоянию\n";
    
    echo "\n🎮 ИНСТРУКЦИИ:\n";
    
    echo "   1. 🔄 Обновите страницу миниапп (F5)\n";
    echo "   2. 👀 Проверьте, что кнопки показывают правильные тексты\n";
    echo "   3. 📊 Убедитесь, что локализация работает\n";
    echo "   4. 🎯 Система лимитов тоже должна работать\n";
    
    echo "\n🚀 СИСТЕМА ПЕРЕВОДОВ ВОССТАНОВЛЕНА!\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
