<?php
/**
 * api/get_user_language.php
 * Определение языка пользователя по IP адресу
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Получаем IP адрес пользователя
    $userIP = getUserIP();
    
    // Определяем язык по IP
    $language = getLanguageByIP($userIP);
    
    echo json_encode([
        'success' => true,
        'ip' => $userIP,
        'language' => $language,
        'is_russian' => $language === 'ru'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // В случае ошибки возвращаем русский язык по умолчанию
    echo json_encode([
        'success' => false,
        'language' => 'ru',
        'is_russian' => true,
        'error' => 'Ошибка определения языка'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Получает реальный IP адрес пользователя
 */
function getUserIP() {
    // Проверяем различные заголовки для получения реального IP
    $ipKeys = [
        'HTTP_CF_CONNECTING_IP',     // Cloudflare
        'HTTP_CLIENT_IP',            // Proxy
        'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
        'HTTP_X_FORWARDED',          // Proxy
        'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
        'HTTP_FORWARDED_FOR',        // Proxy
        'HTTP_FORWARDED',            // Proxy
        'REMOTE_ADDR'                // Standard
    ];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            // Проверяем, что IP валидный и не локальный
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    // Fallback для локальной разработки
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Определяет язык по IP адресу
 */
function getLanguageByIP($ip) {
    // Для локальных IP возвращаем русский
    if (isLocalIP($ip)) {
        return 'ru';
    }
    
    // Список русскоязычных стран (коды ISO)
    $russianCountries = [
        'RU', // Россия
        'BY', // Беларусь
        'KZ', // Казахстан
        'KG', // Киргизия
        'TJ', // Таджикистан
        'UZ', // Узбекистан
        'AM', // Армения
        'AZ', // Азербайджан
        'GE', // Грузия
        'MD', // Молдова
        'UA', // Украина
        'LV', // Латвия (частично русскоязычная)
        'LT', // Литва (частично русскоязычная)
        'EE'  // Эстония (частично русскоязычная)
    ];
    
    try {
        // Пробуем определить страну через простой API
        $country = getCountryByIP($ip);
        
        if ($country && in_array($country, $russianCountries)) {
            return 'ru';
        }
        
        return 'en';
        
    } catch (Exception $e) {
        // В случае ошибки возвращаем русский по умолчанию
        return 'ru';
    }
}

/**
 * Проверяет, является ли IP локальным
 */
function isLocalIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
}

/**
 * Получает код страны по IP
 */
function getCountryByIP($ip) {
    // Простая база данных IP диапазонов для основных стран
    // В реальном проекте лучше использовать GeoIP2 или подобные сервисы
    
    $ipLong = ip2long($ip);
    if ($ipLong === false) {
        return null;
    }
    
    // Основные диапазоны российских IP (упрощенно)
    $russianRanges = [
        ['start' => ip2long('*******'), 'end' => ip2long('***********')],
        ['start' => ip2long('********'), 'end' => ip2long('************')],
        ['start' => ip2long('********'), 'end' => ip2long('************')],
        ['start' => ip2long('********'), 'end' => ip2long('************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')],
        ['start' => ip2long('***********'), 'end' => ip2long('***************')],
        ['start' => ip2long('*********'), 'end' => ip2long('*************')],
        ['start' => ip2long('***********'), 'end' => ip2long('***************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')],
        ['start' => ip2long('**********'), 'end' => ip2long('**************')]
    ];
    
    // Проверяем российские диапазоны
    foreach ($russianRanges as $range) {
        if ($ipLong >= $range['start'] && $ipLong <= $range['end']) {
            return 'RU';
        }
    }
    
    // Пробуем использовать внешний API (если доступен)
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 2,
                'user_agent' => 'Mozilla/5.0 (compatible; UniQPaid/1.0)'
            ]
        ]);
        
        $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['countryCode'])) {
                return $data['countryCode'];
            }
        }
    } catch (Exception $e) {
        // Игнорируем ошибки внешнего API
    }
    
    // По умолчанию возвращаем null (неизвестно)
    return null;
}
?>
