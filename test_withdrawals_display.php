<?php
/**
 * test_withdrawals_display.php
 * Тест отображения данных выводов
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ТЕСТ ОТОБРАЖЕНИЯ ВЫВОДОВ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Тестируем SQL запрос из withdrawals.php
    echo "📊 ТЕСТИРУЕМ SQL ЗАПРОС:\n";
    $sql = "SELECT w.*, u.first_name, u.last_name, u.username 
            FROM user_withdrawals w 
            LEFT JOIN users u ON w.user_id = u.telegram_id 
            ORDER BY w.requested_at DESC";
    
    $withdrawals = $sqlite->query($sql);
    
    echo "✅ Найдено выводов: " . count($withdrawals) . "\n\n";
    
    if (!empty($withdrawals)) {
        echo "📋 ПЕРВЫЕ 5 ВЫВОДОВ:\n";
        foreach (array_slice($withdrawals, 0, 5) as $i => $w) {
            echo "  " . ($i + 1) . ". ID: {$w['id']}\n";
            echo "     User: {$w['user_id']} ({$w['first_name']} {$w['last_name']})\n";
            echo "     Amount: {$w['amount']} {$w['currency']}\n";
            echo "     Status: {$w['status']}\n";
            echo "     Date: {$w['requested_at']}\n";
            echo "     Wallet: " . substr($w['wallet_address'], 0, 20) . "...\n";
            echo "     ---\n";
        }
        
        // Проверяем уникальные валюты и статусы
        echo "\n💰 ВАЛЮТЫ:\n";
        $currencies = $sqlite->query("SELECT DISTINCT currency FROM user_withdrawals WHERE currency IS NOT NULL");
        foreach ($currencies as $curr) {
            echo "  - {$curr['currency']}\n";
        }
        
        echo "\n📊 СТАТУСЫ:\n";
        $statuses = $sqlite->query("SELECT DISTINCT status FROM user_withdrawals WHERE status IS NOT NULL");
        foreach ($statuses as $stat) {
            echo "  - {$stat['status']}\n";
        }
        
        // Статистика по статусам
        echo "\n📈 СТАТИСТИКА ПО СТАТУСАМ:\n";
        $statusStats = $sqlite->query("SELECT status, COUNT(*) as count FROM user_withdrawals GROUP BY status ORDER BY count DESC");
        foreach ($statusStats as $stat) {
            echo "  - {$stat['status']}: {$stat['count']} выводов\n";
        }
        
    } else {
        echo "⚠️ НЕТ ДАННЫХ О ВЫВОДАХ!\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ТЕСТ ЗАВЕРШЕН!\n";
?>
