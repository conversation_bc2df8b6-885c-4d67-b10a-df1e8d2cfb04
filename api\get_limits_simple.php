<?php
/**
 * api/get_limits_simple.php
 * Простой API для получения лимитов пользователя
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    require_once __DIR__ . '/../api/ad_limits_sqlite.php';
    
    // Получаем user_id из параметров
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId) {
        // Для тестирования используем фиксированный ID
        $userId = 5880288830;
    }
    
    $limitsManager = new AdLimitsSQLite();
    $userLimits = $limitsManager->getUserLimits($userId);
    
    // Преобразуем в формат, который ожидает JavaScript
    $data = [];
    foreach ($userLimits as $adType => $limitInfo) {
        $data[$adType] = [
            'current' => $limitInfo['current'],
            'limit' => $limitInfo['limit'],
            'remaining' => $limitInfo['remaining'],
            'can_show' => $limitInfo['can_show']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения лимитов'
    ], JSON_UNESCAPED_UNICODE);
}
?>
