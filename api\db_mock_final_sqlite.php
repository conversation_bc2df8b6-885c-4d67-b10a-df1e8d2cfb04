<?php
/**
 * api/db_mock_final_sqlite.php
 * Финальная версия db_mock.php с полноценной SQLite интеграцией
 * Готова к переходу на настоящую SQLite когда драйвер заработает
 */

declare(strict_types=1);

require_once __DIR__ . '/../database/real_sqlite_manager.php';

// Глобальный экземпляр SQLite менеджера
$GLOBALS['sqlite_manager'] = null;

/**
 * Получение экземпляра SQLite менеджера
 */
function getSQLiteManager(): RealSQLiteManager
{
    if ($GLOBALS['sqlite_manager'] === null) {
        $GLOBALS['sqlite_manager'] = new RealSQLiteManager();
    }
    return $GLOBALS['sqlite_manager'];
}

/**
 * Загрузка данных пользователей из SQLite
 */
function loadUserData(): array
{
    try {
        $sqlite = getSQLiteManager();
        $users = $sqlite->getAllUsers();
        
        // Преобразуем в формат, совместимый со старым API
        $userData = [];
        foreach ($users as $user) {
            $telegramId = (string)$user['telegram_id'];
            $userData[$telegramId] = [
                'balance' => (float)$user['balance'],
                'total_earned' => (float)$user['total_earned'],
                'withdrawals' => [], // Загружаем отдельно если нужно
                'withdrawal_log' => [], // Загружаем отдельно если нужно
                'referrer_id' => $user['referrer_id'],
                'referrals' => [], // Загружаем отдельно если нужно
                'referral_earnings' => (float)$user['referral_earnings'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'username' => $user['username'],
                'language' => $user['language'],
                'registered_at' => $user['registered_at'],
                'last_activity' => $user['last_activity'],
                'suspicious_activity_count' => $user['suspicious_activity_count'],
                'withdrawals_count' => $user['withdrawals_count'],
                'blocked' => (bool)$user['blocked'],
                'blocked_at' => $user['blocked_at'],
                'referrals_count' => $user['referrals_count'],
                'joined' => $user['joined'],
                'suspicious_activity' => $user['suspicious_activity']
            ];
        }
        
        return $userData;
        
    } catch (Exception $e) {
        error_log("Ошибка загрузки пользователей из SQLite: " . $e->getMessage());
        return [];
    }
}

/**
 * Сохранение данных пользователей в SQLite
 */
function saveUserData(array $userData): bool
{
    try {
        $sqlite = getSQLiteManager();
        
        // Обновляем каждого пользователя
        foreach ($userData as $telegramId => $user) {
            $existingUser = $sqlite->getUserByTelegramId((int)$telegramId);
            
            if ($existingUser) {
                // Обновляем существующего пользователя
                $sqlite->updateUserBalance((int)$telegramId, (float)$user['balance']);
                // Здесь можно добавить обновление других полей
            } else {
                // Создаем нового пользователя
                $user['telegram_id'] = (int)$telegramId;
                $sqlite->createUser($user);
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Ошибка сохранения пользователей в SQLite: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение деталей пользователя
 */
function getUserDetails(int $telegramId): ?array
{
    try {
        $sqlite = getSQLiteManager();
        $user = $sqlite->getUserByTelegramId($telegramId);
        
        if (!$user) {
            return null;
        }
        
        return [
            'telegram_id' => $user['telegram_id'],
            'balance' => (float)$user['balance'],
            'total_earned' => (float)$user['total_earned'],
            'referrer_id' => $user['referrer_id'],
            'referral_earnings' => (float)$user['referral_earnings'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'username' => $user['username'],
            'language' => $user['language'],
            'registered_at' => $user['registered_at'],
            'last_activity' => $user['last_activity'],
            'suspicious_activity_count' => $user['suspicious_activity_count'],
            'withdrawals_count' => $user['withdrawals_count'],
            'blocked' => (bool)$user['blocked'],
            'blocked_at' => $user['blocked_at'],
            'referrals_count' => $user['referrals_count'],
            'joined' => $user['joined'],
            'suspicious_activity' => $user['suspicious_activity']
        ];
        
    } catch (Exception $e) {
        error_log("Ошибка получения пользователя {$telegramId} из SQLite: " . $e->getMessage());
        return null;
    }
}

/**
 * Логирование просмотра рекламы
 */
function logAdView(int $userId, string $adType, float $reward): bool
{
    try {
        $sqlite = getSQLiteManager();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        
        return $sqlite->addAdView($userId, $adType, $reward, $ipAddress);
        
    } catch (Exception $e) {
        error_log("Ошибка логирования просмотра рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Логирование клика по рекламе
 */
function logAdClick(int $userId, string $adType, string $clickType, string $reason = ''): bool
{
    try {
        $sqlite = getSQLiteManager();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;

        return $sqlite->addAdClick($userId, $adType, $clickType, $reason, $ipAddress);

    } catch (Exception $e) {
        error_log("Ошибка логирования клика по рекламе: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение лимитов рекламы пользователя
 */
function getUserAdLimits(int $userId): array
{
    try {
        $sqlite = getSQLiteManager();
        
        if ($sqlite->getDatabaseType() === 'emulated_sqlite') {
            $tables = $sqlite->getEmulatorTablesReference();
            $userLimits = [];
            
            foreach ($tables['ad_limits'] as $limit) {
                if ($limit['user_id'] === $userId) {
                    $userLimits[$limit['ad_type']] = $limit['daily_count'];
                }
            }
            
            return $userLimits;
        } else {
            $sql = "SELECT ad_type, daily_count FROM ad_limits WHERE user_id = ?";
            $results = $sqlite->query($sql, [$userId]);
            
            $userLimits = [];
            foreach ($results as $row) {
                $userLimits[$row['ad_type']] = $row['daily_count'];
            }
            
            return $userLimits;
        }
        
    } catch (Exception $e) {
        error_log("Ошибка получения лимитов рекламы: " . $e->getMessage());
        return [];
    }
}

/**
 * Обновление лимита рекламы пользователя
 */
function updateUserAdLimit(int $userId, string $adType, int $count): bool
{
    try {
        $sqlite = getSQLiteManager();
        
        if ($sqlite->getDatabaseType() === 'emulated_sqlite') {
            $tables = $sqlite->getEmulatorTablesReference();
            
            // Ищем существующий лимит
            $limitFound = false;
            foreach ($tables['ad_limits'] as &$limit) {
                if ($limit['user_id'] === $userId && $limit['ad_type'] === $adType) {
                    $limit['daily_count'] = $count;
                    $limit['last_reset_date'] = date('Y-m-d');
                    $limitFound = true;
                    break;
                }
            }
            
            // Создаем новый лимит если не найден
            if (!$limitFound) {
                $tables['ad_limits'][] = [
                    'user_id' => $userId,
                    'ad_type' => $adType,
                    'daily_count' => $count,
                    'last_reset_date' => date('Y-m-d')
                ];
            }
            
            $sqlite->saveEmulatorTablesReference($tables);
            return true;
        } else {
            $sql = "INSERT OR REPLACE INTO ad_limits (user_id, ad_type, daily_count, last_reset_date) 
                    VALUES (?, ?, ?, ?)";
            $params = [$userId, $adType, $count, date('Y-m-d')];
            $sqlite->query($sql, $params);
            return true;
        }
        
    } catch (Exception $e) {
        error_log("Ошибка обновления лимита рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Создание токена рекламы
 */
function createAdToken(string $token, int $userId, string $adType, int $expiresIn = 3600): bool
{
    try {
        $sqlite = getSQLiteManager();
        
        if ($sqlite->getDatabaseType() === 'emulated_sqlite') {
            $tables = $sqlite->getEmulatorTablesReference();
            
            $tables['ad_tokens'][] = [
                'token' => $token,
                'user_id' => $userId,
                'ad_type' => $adType,
                'used' => false,
                'created_at' => time(),
                'expires_at' => time() + $expiresIn,
                'used_at' => null
            ];
            
            $sqlite->saveEmulatorTablesReference($tables);
            return true;
        } else {
            $sql = "INSERT INTO ad_tokens (token, user_id, ad_type, used, created_at, expires_at) 
                    VALUES (?, ?, ?, 0, ?, ?)";
            $params = [$token, $userId, $adType, time(), time() + $expiresIn];
            $sqlite->query($sql, $params);
            return true;
        }
        
    } catch (Exception $e) {
        error_log("Ошибка создания токена рекламы: " . $e->getMessage());
        return false;
    }
}

/**
 * Использование токена рекламы
 */
function useAdToken(string $token): ?array
{
    try {
        $sqlite = getSQLiteManager();
        
        if ($sqlite->getDatabaseType() === 'emulated_sqlite') {
            $tables = $sqlite->getEmulatorTablesReference();
            
            foreach ($tables['ad_tokens'] as &$adToken) {
                if ($adToken['token'] === $token && !$adToken['used'] && $adToken['expires_at'] > time()) {
                    $adToken['used'] = true;
                    $adToken['used_at'] = time();
                    
                    $sqlite->saveEmulatorTablesReference($tables);
                    return $adToken;
                }
            }
            
            return null;
        } else {
            $sql = "UPDATE ad_tokens SET used = 1, used_at = ? WHERE token = ? AND used = 0 AND expires_at > ?";
            $params = [time(), $token, time()];
            $sqlite->query($sql, $params);
            
            $sql = "SELECT * FROM ad_tokens WHERE token = ?";
            $result = $sqlite->query($sql, [$token]);
            return $result[0] ?? null;
        }
        
    } catch (Exception $e) {
        error_log("Ошибка использования токена рекламы: " . $e->getMessage());
        return null;
    }
}

/**
 * Получение статистики базы данных
 */
function getDatabaseStats(): array
{
    try {
        $sqlite = getSQLiteManager();
        return $sqlite->getDatabaseStats();
        
    } catch (Exception $e) {
        error_log("Ошибка получения статистики базы данных: " . $e->getMessage());
        return [
            'users_count' => 0,
            'ad_views_count' => 0,
            'ad_clicks_count' => 0,
            'ad_tokens_count' => 0,
            'support_messages_count' => 0,
            'file_size' => 0,
            'last_updated' => date('Y-m-d H:i:s'),
            'database_type' => 'error',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Тестирование подключения к базе данных
 */
function testDatabaseConnection(): bool
{
    try {
        $sqlite = getSQLiteManager();
        return $sqlite->testConnection();
        
    } catch (Exception $e) {
        error_log("Ошибка тестирования подключения к базе данных: " . $e->getMessage());
        return false;
    }
}

/**
 * Обновление баланса пользователя
 */
function updateUserBalance(int $telegramId, float $newBalance): bool
{
    try {
        $sqlite = getSQLiteManager();
        return $sqlite->updateUserBalance($telegramId, $newBalance);
        
    } catch (Exception $e) {
        error_log("Ошибка обновления баланса пользователя: " . $e->getMessage());
        return false;
    }
}

/**
 * Создание нового пользователя
 */
function createUser(int $telegramId, array $userData): bool
{
    try {
        $sqlite = getSQLiteManager();
        $userData['telegram_id'] = $telegramId;
        return $sqlite->createUser($userData);
        
    } catch (Exception $e) {
        error_log("Ошибка создания пользователя: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение типа базы данных
 */
function getDatabaseType(): string
{
    try {
        $sqlite = getSQLiteManager();
        return $sqlite->getDatabaseType();
    } catch (Exception $e) {
        return 'error';
    }
}

/**
 * Выполнение SQL запроса (для продвинутых операций)
 */
function executeSQLQuery(string $sql, array $params = []): array
{
    try {
        $sqlite = getSQLiteManager();
        return $sqlite->query($sql, $params);
        
    } catch (Exception $e) {
        error_log("Ошибка выполнения SQL запроса: " . $e->getMessage());
        return [];
    }
}
?>
