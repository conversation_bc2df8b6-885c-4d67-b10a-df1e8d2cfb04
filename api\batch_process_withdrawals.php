<?php
/**
 * api/batch_process_withdrawals.php
 * API для массовой обработки выводов (для админки)
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Проверяем обязательные поля
    $requiredFields = ['withdrawals', 'admin_key'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    $withdrawals = $input['withdrawals'];
    $adminKey = $input['admin_key'];
    
    // Простая проверка админского ключа (в реальном проекте использовать более надежную аутентификацию)
    if ($adminKey !== 'admin_secret_key_2024') {
        throw new Exception('Invalid admin key');
    }
    
    if (!is_array($withdrawals) || empty($withdrawals)) {
        throw new Exception('Withdrawals array is required');
    }
    
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    $results = [];
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($withdrawals as $withdrawalData) {
        $withdrawalId = (int)($withdrawalData['id'] ?? 0);
        $action = strtolower(trim($withdrawalData['action'] ?? ''));
        $transactionHash = $withdrawalData['transaction_hash'] ?? null;
        $adminNotes = $withdrawalData['admin_notes'] ?? '';
        $errorMessage = $withdrawalData['error_message'] ?? '';
        
        if (!$withdrawalId || !$action) {
            $results[] = [
                'withdrawal_id' => $withdrawalId,
                'success' => false,
                'error' => 'Missing withdrawal ID or action'
            ];
            $errorCount++;
            continue;
        }
        
        try {
            // Получаем информацию о выводе
            $withdrawalInfo = $db->query(
                "SELECT * FROM user_withdrawals WHERE id = ?",
                [$withdrawalId]
            );
            
            if (empty($withdrawalInfo)) {
                throw new Exception('Withdrawal not found');
            }
            
            $withdrawal = $withdrawalInfo[0];
            $userId = $withdrawal['user_id'];
            $amount = (float)$withdrawal['amount'];
            $currentStatus = $withdrawal['status'];
            
            // Проверяем, можно ли обработать этот вывод
            if (!in_array($currentStatus, ['pending', 'processing', 'waiting', 'confirming'])) {
                throw new Exception("Cannot process withdrawal with status: {$currentStatus}");
            }
            
            $db->beginTransaction();
            
            $processSuccess = false;
            
            switch ($action) {
                case 'complete':
                    $processSuccess = processWithdrawalCompleteBatch($withdrawalId, $withdrawal, $coinsManager, $db, $transactionHash, $adminNotes);
                    break;
                    
                case 'cancel':
                    $processSuccess = processWithdrawalCancelBatch($withdrawalId, $withdrawal, $coinsManager, $db, $adminNotes);
                    break;
                    
                case 'fail':
                    $processSuccess = processWithdrawalFailBatch($withdrawalId, $withdrawal, $coinsManager, $db, $errorMessage, $adminNotes);
                    break;
                    
                default:
                    throw new Exception('Invalid action');
            }
            
            if (!$processSuccess) {
                throw new Exception('Failed to process withdrawal');
            }
            
            $db->commit();
            
            $results[] = [
                'withdrawal_id' => $withdrawalId,
                'success' => true,
                'action' => $action,
                'old_status' => $currentStatus,
                'new_status' => $action === 'complete' ? 'completed' : ($action === 'cancel' ? 'cancelled' : 'failed'),
                'amount' => $amount,
                'user_id' => $userId
            ];
            
            $successCount++;
            
            error_log("batch_process_withdrawals INFO: Successfully processed withdrawal #{$withdrawalId} with action {$action}");
            
        } catch (Exception $e) {
            $db->rollback();
            
            $results[] = [
                'withdrawal_id' => $withdrawalId,
                'success' => false,
                'error' => $e->getMessage()
            ];
            
            $errorCount++;
            
            error_log("batch_process_withdrawals ERROR: Failed to process withdrawal #{$withdrawalId}: " . $e->getMessage());
        }
    }
    
    // Возвращаем результаты
    echo json_encode([
        'success' => true,
        'message' => "Processed {$successCount} withdrawals successfully, {$errorCount} errors",
        'summary' => [
            'total_processed' => count($withdrawals),
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'success_rate' => count($withdrawals) > 0 ? round(($successCount / count($withdrawals)) * 100, 1) : 0
        ],
        'results' => $results
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("batch_process_withdrawals ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'BATCH_PROCESS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Завершение вывода в пакетном режиме
 */
function processWithdrawalCompleteBatch(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, ?string $transactionHash, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Получаем текущий баланс
    $balance = $coinsManager->getUserBalance($userId);
    
    // Проверяем, что средства зарезервированы
    if ($balance['reserved_balance'] < $amount) {
        throw new Exception('Insufficient reserved balance');
    }
    
    // Списываем зарезервированные средства
    $newBalance = $balance['balance'] - $amount;
    $newReservedBalance = $balance['reserved_balance'] - $amount;
    
    // Обновляем баланс пользователя
    $db->query(
        "UPDATE users SET balance = ?, reserved_balance = ? WHERE telegram_id = ?",
        [$newBalance, $newReservedBalance, $userId]
    );
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'completed', 
         transaction_hash = ?, 
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP,
         completed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$transactionHash, $adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию списания
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_complete',
        $amount,
        'debit',
        $balance['balance'],
        $newBalance,
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} completed (batch)",
        [
            'withdrawal_id' => $withdrawalId,
            'transaction_hash' => $transactionHash,
            'currency' => $withdrawal['currency'],
            'wallet_address' => $withdrawal['wallet_address'],
            'batch_processed' => true
        ]
    );
    
    return true;
}

/**
 * Отмена вывода в пакетном режиме
 */
function processWithdrawalCancelBatch(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Снимаем резерв
    $unreserveSuccess = $coinsManager->unreserveCoins($userId, $amount, $withdrawalId);
    
    if (!$unreserveSuccess) {
        throw new Exception('Failed to unreserve coins');
    }
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'cancelled', 
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию отмены
    $balance = $coinsManager->getUserBalance($userId);
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_cancel',
        $amount,
        'credit',
        $balance['balance'],
        $balance['balance'],
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} cancelled (batch)",
        [
            'withdrawal_id' => $withdrawalId,
            'reason' => 'cancelled',
            'admin_notes' => $adminNotes,
            'batch_processed' => true
        ]
    );
    
    return true;
}

/**
 * Ошибка вывода в пакетном режиме
 */
function processWithdrawalFailBatch(int $withdrawalId, array $withdrawal, CoinsManager $coinsManager, RealSQLiteManager $db, string $errorMessage, string $adminNotes): bool {
    $userId = $withdrawal['user_id'];
    $amount = (float)$withdrawal['amount'];
    
    // Снимаем резерв
    $unreserveSuccess = $coinsManager->unreserveCoins($userId, $amount, $withdrawalId);
    
    if (!$unreserveSuccess) {
        throw new Exception('Failed to unreserve coins');
    }
    
    // Обновляем статус вывода
    $db->query(
        "UPDATE user_withdrawals SET 
         status = 'failed', 
         error_message = ?,
         admin_notes = ?, 
         processed_at = CURRENT_TIMESTAMP
         WHERE id = ?",
        [$errorMessage, $adminNotes, $withdrawalId]
    );
    
    // Записываем транзакцию ошибки
    $balance = $coinsManager->getUserBalance($userId);
    $coinsManager->recordTransaction(
        $userId,
        'withdraw_fail',
        $amount,
        'credit',
        $balance['balance'],
        $balance['balance'],
        'withdrawal',
        $withdrawalId,
        "Withdrawal #{$withdrawalId} failed (batch)",
        [
            'withdrawal_id' => $withdrawalId,
            'reason' => 'failed',
            'error_message' => $errorMessage,
            'admin_notes' => $adminNotes,
            'batch_processed' => true
        ]
    );
    
    return true;
}
?>
