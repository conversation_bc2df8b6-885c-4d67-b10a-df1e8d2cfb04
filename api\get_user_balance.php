<?php
/**
 * api/get_user_balance.php
 * API для получения баланса пользователя
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $userId = null;
    
    // Поддерживаем как GET, так и POST запросы
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Если есть initData, парсим его
        if (isset($input['initData'])) {
            require_once __DIR__ . '/../api/telegram_auth.php';
            $telegramAuth = new TelegramAuth();
            $userData = $telegramAuth->validateInitData($input['initData']);
            
            if (!$userData || !isset($userData['user']['id'])) {
                throw new Exception('Invalid Telegram data');
            }
            
            $userId = (string)$userData['user']['id'];
        } elseif (isset($input['user_id'])) {
            $userId = (string)$input['user_id'];
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Для GET запросов
        if (isset($_GET['user_id'])) {
            $userId = (string)$_GET['user_id'];
        }
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Инициализируем менеджер монет
    require_once __DIR__ . '/coins_manager.php';
    $coinsManager = new CoinsManager();
    
    // Получаем баланс пользователя
    $balance = $coinsManager->getUserBalance($userId);
    
    // Получаем дневную статистику
    $dailyStats = $coinsManager->checkDailyEarnLimit($userId);
    
    // Получаем лимиты рекламы
    require_once __DIR__ . '/ad_limits_sqlite.php';
    $limitsManager = new AdLimitsSQLite();
    $adLimits = $limitsManager->getUserLimits($userId);
    
    // Получаем последние транзакции
    $recentTransactions = $coinsManager->getUserTransactions($userId, 10);
    
    // Форматируем транзакции для фронтенда
    $formattedTransactions = [];
    foreach ($recentTransactions as $transaction) {
        $formattedTransactions[] = [
            'id' => $transaction['id'],
            'type' => $transaction['transaction_type'],
            'amount' => (float)$transaction['amount'],
            'operation' => $transaction['operation'],
            'description' => $transaction['description'],
            'source_type' => $transaction['source_type'],
            'created_at' => $transaction['created_at'],
            'balance_after' => (float)$transaction['balance_after']
        ];
    }
    
    // Получаем настройки системы
    $minWithdrawal = (float)$coinsManager->getSetting('min_withdrawal_coins', '1000');
    $coinRate = (float)$coinsManager->getSetting('coin_rate_usd', '0.001');
    
    // Возвращаем полную информацию о балансе
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'balance' => [
            'current' => $balance['balance'],
            'available' => $balance['available_balance'],
            'reserved' => $balance['reserved_balance'],
            'total_earned' => $balance['total_earned'],
            'total_withdrawn' => $balance['total_withdrawn'],
            'usd_value' => round($balance['available_balance'] * $coinRate, 4)
        ],
        'daily_stats' => [
            'limit' => $dailyStats['daily_limit'],
            'earned_today' => $dailyStats['earned_today'],
            'remaining' => $dailyStats['remaining'],
            'can_earn' => $dailyStats['can_earn'],
            'progress_percent' => round(($dailyStats['earned_today'] / $dailyStats['daily_limit']) * 100, 1)
        ],
        'ad_limits' => $adLimits,
        'withdrawal' => [
            'min_amount' => $minWithdrawal,
            'can_withdraw' => $balance['available_balance'] >= $minWithdrawal,
            'available_usd' => round($balance['available_balance'] * $coinRate, 4)
        ],
        'recent_transactions' => $formattedTransactions,
        'system_info' => [
            'coin_rate_usd' => $coinRate,
            'timestamp' => time(),
            'server_time' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'GET_BALANCE_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
