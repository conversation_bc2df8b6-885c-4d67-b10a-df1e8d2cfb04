<?php
/**
 * check_ip_addresses.php
 * Проверка IP адресов в базе данных
 */

declare(strict_types=1);

echo "🌐 ПРОВЕРКА IP АДРЕСОВ В БАЗЕ ДАННЫХ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ПРОВЕРКА IP В ТАБЛИЦЕ ad_clicks:\n";
    
    // Общая статистика
    $totalClicks = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks");
    echo "   Всего кликов: " . $totalClicks[0]['count'] . "\n";
    
    // Клики с IP
    $clicksWithIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ip_address IS NOT NULL AND ip_address != ''");
    echo "   Кликов с IP: " . $clicksWithIP[0]['count'] . "\n";
    
    // Клики без IP
    $clicksWithoutIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_clicks WHERE ip_address IS NULL OR ip_address = ''");
    echo "   Кликов без IP: " . $clicksWithoutIP[0]['count'] . "\n";
    
    // Примеры IP адресов
    echo "\n2. 🔍 ПРИМЕРЫ IP АДРЕСОВ:\n";
    $sampleIPs = $sqlite->query("SELECT ip_address, user_id, ad_type, timestamp FROM ad_clicks WHERE ip_address IS NOT NULL AND ip_address != '' LIMIT 10");
    
    if (empty($sampleIPs)) {
        echo "   ❌ НЕТ IP АДРЕСОВ В ТАБЛИЦЕ!\n";
    } else {
        foreach ($sampleIPs as $click) {
            echo "   - IP: {$click['ip_address']}, User: {$click['user_id']}, Type: {$click['ad_type']}, Time: {$click['timestamp']}\n";
        }
    }
    
    echo "\n3. 📊 СТАТИСТИКА ПО IP:\n";
    $ipStats = $sqlite->query("SELECT ip_address, COUNT(*) as count FROM ad_clicks WHERE ip_address IS NOT NULL AND ip_address != '' GROUP BY ip_address ORDER BY count DESC LIMIT 10");
    
    if (empty($ipStats)) {
        echo "   ❌ НЕТ ГРУППИРОВКИ ПО IP!\n";
    } else {
        foreach ($ipStats as $ip) {
            echo "   - {$ip['ip_address']}: {$ip['count']} кликов\n";
        }
    }
    
    echo "\n4. 📊 ПРОВЕРКА IP В ТАБЛИЦЕ ad_views:\n";
    
    $totalViews = $sqlite->query("SELECT COUNT(*) as count FROM ad_views");
    echo "   Всего просмотров: " . $totalViews[0]['count'] . "\n";
    
    $viewsWithIP = $sqlite->query("SELECT COUNT(*) as count FROM ad_views WHERE ip_address IS NOT NULL AND ip_address != ''");
    echo "   Просмотров с IP: " . $viewsWithIP[0]['count'] . "\n";
    
    // Примеры IP из просмотров
    $sampleViewIPs = $sqlite->query("SELECT ip_address, user_id, ad_type, timestamp FROM ad_views WHERE ip_address IS NOT NULL AND ip_address != '' LIMIT 5");
    
    echo "   Примеры IP из просмотров:\n";
    foreach ($sampleViewIPs as $view) {
        echo "     - IP: {$view['ip_address']}, User: {$view['user_id']}, Type: {$view['ad_type']}\n";
    }
    
    echo "\n5. 🔧 ПРОВЕРКА СТРУКТУРЫ ТАБЛИЦ:\n";
    
    // Структура таблицы ad_clicks
    $clicksSchema = $sqlite->query("PRAGMA table_info(ad_clicks)");
    echo "   Структура ad_clicks:\n";
    foreach ($clicksSchema as $column) {
        echo "     - {$column['name']}: {$column['type']}\n";
    }
    
    // Структура таблицы ad_views
    $viewsSchema = $sqlite->query("PRAGMA table_info(ad_views)");
    echo "   Структура ad_views:\n";
    foreach ($viewsSchema as $column) {
        echo "     - {$column['name']}: {$column['type']}\n";
    }
    
    echo "\n6. 🧪 ТЕСТ CTR РАСЧЕТА:\n";
    
    // Проверим CTR для каждого типа
    $ctrQuery = "
        SELECT 
            v.ad_type,
            COUNT(v.id) as views,
            COALESCE(c.clicks, 0) as clicks
        FROM ad_views v
        LEFT JOIN (
            SELECT ad_type, COUNT(*) as clicks 
            FROM ad_clicks 
            WHERE ad_type != 'test_banner'
            GROUP BY ad_type
        ) c ON v.ad_type = c.ad_type
        WHERE v.ad_type != 'test_banner'
        GROUP BY v.ad_type
    ";
    
    $ctrResults = $sqlite->query($ctrQuery);
    
    foreach ($ctrResults as $row) {
        $ctr = $row['views'] > 0 ? round(($row['clicks'] / $row['views']) * 100, 2) : 0;
        echo "   - {$row['ad_type']}: {$row['views']} просмотров, {$row['clicks']} кликов, CTR: {$ctr}%\n";
    }
    
    echo "\n✅ ДИАГНОСТИКА:\n";
    
    if ($clicksWithIP[0]['count'] == 0) {
        echo "   ❌ ПРОБЛЕМА: В таблице ad_clicks НЕТ IP АДРЕСОВ!\n";
        echo "   🔧 НУЖНО: Исправить миграцию или логирование кликов\n";
    } else {
        echo "   ✅ IP адреса в кликах есть: " . $clicksWithIP[0]['count'] . " записей\n";
    }
    
    if ($viewsWithIP[0]['count'] == 0) {
        echo "   ❌ ПРОБЛЕМА: В таблице ad_views НЕТ IP АДРЕСОВ!\n";
    } else {
        echo "   ✅ IP адреса в просмотрах есть: " . $viewsWithIP[0]['count'] . " записей\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
