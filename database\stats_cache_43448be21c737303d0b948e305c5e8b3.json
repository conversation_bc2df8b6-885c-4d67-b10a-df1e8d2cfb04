{"success": true, "stats_by_type": {"native_banner": {"views": 33, "clicks": 206, "rewards": 330, "ctr": 624.24}, "interstitial": {"views": 15, "clicks": 103, "rewards": 120, "ctr": 686.67}, "rewarded_video": {"views": 26, "clicks": 78, "rewards": 52, "ctr": 300}}, "stats_by_country": {"NG": 188, "CA": 67, "BD": 32, "PH": 16, "VE": 14}, "hourly_stats": [{"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 86, "views": 20}, {"clicks": 52, "views": 0}, {"clicks": 64, "views": 0}, {"clicks": 4, "views": 0}, {"clicks": 62, "views": 26}, {"clicks": 18, "views": 14}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 17, "views": 3}, {"clicks": 9, "views": 3}, {"clicks": 29, "views": 2}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 2, "views": 2}, {"clicks": 21, "views": 3}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 0, "views": 0}, {"clicks": 12, "views": 0}, {"clicks": 11, "views": 1}], "last_updated": "2025-07-17 03:29:13 UTC", "data_info": {"total_views": 74, "total_clicks": 387, "total_rewards": 502, "total_countries": 5, "filters_applied": {"date_from": "2025-07-16", "date_to": "2025-07-17", "ad_type": "all"}}}