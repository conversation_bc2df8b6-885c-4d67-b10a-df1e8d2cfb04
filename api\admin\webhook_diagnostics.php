<?php
/**
 * Страница диагностики webhook
 */

require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/../../includes/bot_config_loader.php';
defineBotConstants();

include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🔧 Диагностика Webhook</h1>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <strong>Основной бот</strong>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-primary" onclick="checkBot('main')">
                                🔍 Проверить основной бот
                            </button>
                            <div id="main-bot-results" class="mt-3"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <strong>Бот поддержки</strong>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-primary" onclick="checkBot('support')">
                                🔍 Проверить бот поддержки
                            </button>
                            <div id="support-bot-results" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <strong>Полная диагностика</strong>
                </div>
                <div class="card-body">
                    <button class="btn btn-success" onclick="checkBot('both')">
                        🚀 Проверить оба бота
                    </button>
                    <div id="both-bots-results" class="mt-3"></div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function checkBot(botType) {
    const resultDiv = document.getElementById(botType + '-bot-results');
    if (botType === 'both') {
        document.getElementById('both-bots-results').innerHTML = '<div class="alert alert-info">Проверяем оба бота...</div>';
    } else {
        resultDiv.innerHTML = '<div class="alert alert-info">Проверяем...</div>';
    }
    
    fetch(`debug_webhook.php?bot=${botType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                
                if (data.results.main_bot) {
                    html += formatBotResults('Основной бот', data.results.main_bot);
                }
                
                if (data.results.support_bot) {
                    html += formatBotResults('Бот поддержки', data.results.support_bot);
                }
                
                if (botType === 'both') {
                    document.getElementById('both-bots-results').innerHTML = html;
                } else {
                    resultDiv.innerHTML = html;
                }
            } else {
                const errorHtml = `<div class="alert alert-danger">❌ Ошибка: ${data.error}</div>`;
                if (botType === 'both') {
                    document.getElementById('both-bots-results').innerHTML = errorHtml;
                } else {
                    resultDiv.innerHTML = errorHtml;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const errorHtml = '<div class="alert alert-danger">❌ Ошибка при выполнении диагностики</div>';
            if (botType === 'both') {
                document.getElementById('both-bots-results').innerHTML = errorHtml;
            } else {
                resultDiv.innerHTML = errorHtml;
            }
        });
}

function formatBotResults(botName, results) {
    let html = `<h5>${botName}</h5>`;
    
    // Проверка токена
    if (results.token_check && results.token_check.ok) {
        const bot = results.token_check.result;
        html += `<p><span class="badge bg-success">✅ Токен валиден</span> @${bot.username} (${bot.first_name})</p>`;
    } else {
        html += `<p><span class="badge bg-danger">❌ Проблема с токеном</span></p>`;
    }
    
    // Проверка URL
    html += `<p><strong>Webhook URL:</strong> <code>${results.webhook_url}</code></p>`;
    
    if (results.url_accessibility.accessible) {
        html += `<p><span class="badge bg-success">✅ URL доступен</span> (HTTP ${results.url_accessibility.http_code})</p>`;
    } else {
        html += `<p><span class="badge bg-danger">❌ URL недоступен</span> (HTTP ${results.url_accessibility.http_code})`;
        if (results.url_accessibility.error) {
            html += `<br><small>Ошибка: ${results.url_accessibility.error}</small>`;
        }
        html += `</p>`;
    }
    
    // Текущий webhook
    if (results.current_webhook && results.current_webhook.ok) {
        const webhook = results.current_webhook.result;
        html += `<p><strong>Текущий webhook:</strong> ${webhook.url || 'Не установлен'}</p>`;
        if (webhook.pending_update_count > 0) {
            html += `<p><span class="badge bg-warning">⚠️ Ожидающих обновлений: ${webhook.pending_update_count}</span></p>`;
        }
        if (webhook.last_error_message) {
            html += `<p><span class="badge bg-danger">❌ Последняя ошибка:</span><br><small>${webhook.last_error_message}</small></p>`;
        }
    }
    
    // Тест установки webhook
    if (results.test_set_webhook) {
        const test = results.test_set_webhook;
        if (test.parsed && test.parsed.ok) {
            html += `<p><span class="badge bg-success">✅ Тест установки webhook успешен</span></p>`;
        } else {
            html += `<p><span class="badge bg-danger">❌ Ошибка установки webhook</span></p>`;
            if (test.parsed && test.parsed.description) {
                html += `<p><small>Описание: ${test.parsed.description}</small></p>`;
            }
            if (test.curl_error) {
                html += `<p><small>CURL ошибка: ${test.curl_error}</small></p>`;
            }
        }
    }
    
    html += '<hr>';
    return html;
}

// Автоматическая проверка при загрузке
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        checkBot('both');
    }, 1000);
});
</script>

<?php include 'templates/footer.php'; ?>
