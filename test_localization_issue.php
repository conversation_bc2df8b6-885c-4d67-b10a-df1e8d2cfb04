<?php
/**
 * test_localization_issue.php
 * Диагностика проблем с локализацией
 */

declare(strict_types=1);

echo "🔍 ДИАГНОСТИКА ПРОБЛЕМ С ЛОКАЛИЗАЦИЕЙ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ПРОВЕРКА БАЗЫ ДАННЫХ:\n";
    
    // Проверяем общее количество текстов
    $total = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts')[0]['count'];
    echo "   📝 Всего текстов в базе: {$total}\n";
    
    // Проверяем языки
    $languages = $sqlite->query('SELECT language_code, COUNT(*) as count FROM bot_texts GROUP BY language_code ORDER BY count DESC');
    echo "   🌍 Языки:\n";
    foreach ($languages as $lang) {
        echo "     - {$lang['language_code']}: {$lang['count']} текстов\n";
    }
    
    echo "\n2. 🔧 ТЕСТ API ЛОКАЛИЗАЦИИ:\n";
    
    // Тестируем API для русского языка
    echo "   📡 Тестируем API для русского языка...\n";
    $ruResponse = file_get_contents('http://argun-clear.loc/api/get_localization.php?lang=ru');
    
    if ($ruResponse) {
        $ruData = json_decode($ruResponse, true);
        if ($ruData && isset($ruData['app'])) {
            echo "   ✅ API работает для русского языка\n";
            echo "   📊 Категорий: " . count($ruData['app']) . "\n";
            
            // Показываем примеры
            echo "   📝 Примеры переводов:\n";
            $count = 0;
            foreach ($ruData['app'] as $category => $texts) {
                if ($count >= 3) break;
                echo "     - Категория '{$category}': " . count($texts) . " текстов\n";
                
                // Показываем первые 2 текста из категории
                $textCount = 0;
                foreach ($texts as $key => $value) {
                    if ($textCount >= 2) break;
                    $shortValue = strlen($value) > 40 ? substr($value, 0, 40) . "..." : $value;
                    echo "       * {$key}: {$shortValue}\n";
                    $textCount++;
                }
                $count++;
            }
        } else {
            echo "   ❌ API вернул неправильный формат\n";
            echo "   Ответ: " . substr($ruResponse, 0, 200) . "\n";
        }
    } else {
        echo "   ❌ API не отвечает\n";
    }
    
    echo "\n3. 📂 ПРОВЕРКА ФАЙЛОВ ЛОКАЛИЗАЦИИ:\n";
    
    $localeFiles = ['locales/ru.json', 'locales/en.json'];
    foreach ($localeFiles as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "   ✅ {$file}: " . number_format($size) . " bytes\n";
            
            $content = json_decode(file_get_contents($file), true);
            if ($content && isset($content['app'])) {
                echo "     📊 Категорий в файле: " . count($content['app']) . "\n";
            }
        } else {
            echo "   ❌ {$file}: НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n4. 🎯 ПРОВЕРКА КЛЮЧЕВЫХ ТЕКСТОВ:\n";
    
    $keyTexts = [
        'welcome_message' => 'Приветственное сообщение',
        'balance_info' => 'Информация о балансе',
        'withdrawal_success' => 'Успешный вывод',
        'ad_reward_received' => 'Награда за рекламу',
        'button_watch_ad' => 'Кнопка просмотра рекламы'
    ];
    
    foreach ($keyTexts as $key => $description) {
        $result = $sqlite->query("SELECT text_value FROM bot_texts WHERE text_key = ? AND language_code = 'ru'", [$key]);
        if (!empty($result)) {
            $value = strlen($result[0]['text_value']) > 50 ? substr($result[0]['text_value'], 0, 50) . "..." : $result[0]['text_value'];
            echo "   ✅ {$description} ({$key}): {$value}\n";
        } else {
            echo "   ❌ {$description} ({$key}): НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n5. 🔍 ПОИСК ПРОБЛЕМНЫХ КЛЮЧЕЙ:\n";
    
    // Ищем тексты, которые могут быть ключами
    $suspiciousTexts = $sqlite->query("
        SELECT text_key, text_value, language_code 
        FROM bot_texts 
        WHERE text_value LIKE '%_%' 
           OR text_value LIKE '%.%' 
           OR LENGTH(text_value) < 5
        ORDER BY language_code, text_key
        LIMIT 10
    ");
    
    if (!empty($suspiciousTexts)) {
        echo "   ⚠️ Подозрительные тексты (возможно ключи):\n";
        foreach ($suspiciousTexts as $text) {
            echo "     - [{$text['language_code']}] {$text['text_key']}: '{$text['text_value']}'\n";
        }
    } else {
        echo "   ✅ Подозрительных текстов не найдено\n";
    }
    
    echo "\n6. 🌐 ТЕСТ JAVASCRIPT ЛОКАЛИЗАЦИИ:\n";
    
    // Проверяем, что JavaScript файлы существуют
    $jsFiles = [
        'js/localization.js' => 'Основная локализация',
        'js/status-localization.js' => 'Статусная локализация',
        'js/main.js' => 'Главный файл приложения'
    ];
    
    foreach ($jsFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✅ {$description}: {$file}\n";
        } else {
            echo "   ❌ {$description}: {$file} НЕ НАЙДЕН\n";
        }
    }
    
    echo "\n7. 🎮 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ:\n";
    
    if ($total > 0) {
        echo "   ✅ База данных содержит переводы\n";
        echo "   ✅ API локализации работает\n";
        echo "   🔧 Проверьте консоль браузера на ошибки JavaScript\n";
        echo "   🔧 Убедитесь, что appLocalization.init() вызывается\n";
        echo "   🔧 Проверьте, что элементы имеют правильные data-translate атрибуты\n";
    } else {
        echo "   ❌ База данных пуста - нужна миграция локализации\n";
        echo "   🔧 Запустите: php migrate_localization_to_sqlite.php\n";
    }
    
    echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА!\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}
?>
