<?php
/**
 * api/get_transactions.php
 * API для получения истории транзакций пользователя
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    $userId = null;
    $limit = 50;
    $offset = 0;
    $transactionType = null;
    $dateFrom = null;
    $dateTo = null;
    
    // Поддерживаем как GET, так и POST запросы
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Если есть initData, парсим его
        if (isset($input['initData'])) {
            require_once __DIR__ . '/../api/telegram_auth.php';
            $telegramAuth = new TelegramAuth();
            $userData = $telegramAuth->validateInitData($input['initData']);
            
            if (!$userData || !isset($userData['user']['id'])) {
                throw new Exception('Invalid Telegram data');
            }
            
            $userId = (string)$userData['user']['id'];
        } elseif (isset($input['user_id'])) {
            $userId = (string)$input['user_id'];
        }
        
        // Дополнительные параметры
        $limit = $input['limit'] ?? 50;
        $offset = $input['offset'] ?? 0;
        $transactionType = $input['transaction_type'] ?? null;
        $dateFrom = $input['date_from'] ?? null;
        $dateTo = $input['date_to'] ?? null;
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Для GET запросов
        $userId = $_GET['user_id'] ?? null;
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = (int)($_GET['offset'] ?? 0);
        $transactionType = $_GET['transaction_type'] ?? null;
        $dateFrom = $_GET['date_from'] ?? null;
        $dateTo = $_GET['date_to'] ?? null;
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Валидация параметров
    $limit = max(1, min(100, $limit)); // От 1 до 100
    $offset = max(0, $offset);
    
    // Инициализируем менеджер монет
    require_once __DIR__ . '/coins_manager.php';
    $coinsManager = new CoinsManager();
    
    // Получаем транзакции с фильтрами
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    $db = new RealSQLiteManager();
    
    // Строим SQL запрос с фильтрами
    $whereConditions = ['user_id = ?'];
    $params = [$userId];
    
    if ($transactionType) {
        $whereConditions[] = 'transaction_type = ?';
        $params[] = $transactionType;
    }
    
    if ($dateFrom) {
        $whereConditions[] = 'date(created_at) >= ?';
        $params[] = $dateFrom;
    }
    
    if ($dateTo) {
        $whereConditions[] = 'date(created_at) <= ?';
        $params[] = $dateTo;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Получаем общее количество транзакций
    $totalQuery = "SELECT COUNT(*) as total FROM coin_transactions WHERE {$whereClause}";
    $totalResult = $db->query($totalQuery, $params);
    $total = $totalResult[0]['total'];
    
    // Получаем транзакции с пагинацией
    $transactionsQuery = "
        SELECT * FROM coin_transactions 
        WHERE {$whereClause}
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ";
    $params[] = $limit;
    $params[] = $offset;
    
    $transactions = $db->query($transactionsQuery, $params);
    
    // Форматируем транзакции для фронтенда
    $formattedTransactions = [];
    foreach ($transactions as $transaction) {
        $formattedTransactions[] = [
            'id' => (int)$transaction['id'],
            'type' => $transaction['transaction_type'],
            'amount' => (float)$transaction['amount'],
            'operation' => $transaction['operation'],
            'balance_before' => (float)$transaction['balance_before'],
            'balance_after' => (float)$transaction['balance_after'],
            'source_type' => $transaction['source_type'],
            'source_id' => $transaction['source_id'] ? (int)$transaction['source_id'] : null,
            'description' => $transaction['description'],
            'metadata' => $transaction['metadata'] ? json_decode($transaction['metadata'], true) : null,
            'created_at' => $transaction['created_at'],
            'formatted_date' => date('d.m.Y H:i', strtotime($transaction['created_at'])),
            'amount_display' => ($transaction['operation'] === 'credit' ? '+' : '-') . $transaction['amount']
        ];
    }
    
    // Получаем статистику по типам транзакций
    $statsQuery = "
        SELECT 
            transaction_type,
            operation,
            COUNT(*) as count,
            SUM(amount) as total_amount
        FROM coin_transactions 
        WHERE user_id = ?
        GROUP BY transaction_type, operation
        ORDER BY transaction_type, operation
    ";
    $statsResult = $db->query($statsQuery, [$userId]);
    
    $stats = [];
    foreach ($statsResult as $stat) {
        $key = $stat['transaction_type'] . '_' . $stat['operation'];
        $stats[$key] = [
            'type' => $stat['transaction_type'],
            'operation' => $stat['operation'],
            'count' => (int)$stat['count'],
            'total_amount' => (float)$stat['total_amount']
        ];
    }
    
    // Получаем статистику по дням (последние 30 дней)
    $dailyStatsQuery = "
        SELECT 
            date(created_at) as date,
            COUNT(*) as transactions_count,
            SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END) as earned,
            SUM(CASE WHEN operation = 'debit' THEN amount ELSE 0 END) as spent
        FROM coin_transactions 
        WHERE user_id = ? AND date(created_at) >= date('now', '-30 days')
        GROUP BY date(created_at)
        ORDER BY date(created_at) DESC
    ";
    $dailyStats = $db->query($dailyStatsQuery, [$userId]);
    
    // Форматируем дневную статистику
    $formattedDailyStats = [];
    foreach ($dailyStats as $dayStat) {
        $formattedDailyStats[] = [
            'date' => $dayStat['date'],
            'formatted_date' => date('d.m.Y', strtotime($dayStat['date'])),
            'transactions_count' => (int)$dayStat['transactions_count'],
            'earned' => (float)$dayStat['earned'],
            'spent' => (float)$dayStat['spent'],
            'net' => (float)$dayStat['earned'] - (float)$dayStat['spent']
        ];
    }
    
    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'pagination' => [
            'total' => (int)$total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total,
            'pages' => ceil($total / $limit),
            'current_page' => floor($offset / $limit) + 1
        ],
        'filters' => [
            'transaction_type' => $transactionType,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ],
        'transactions' => $formattedTransactions,
        'statistics' => [
            'by_type' => $stats,
            'daily' => $formattedDailyStats
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'GET_TRANSACTIONS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
