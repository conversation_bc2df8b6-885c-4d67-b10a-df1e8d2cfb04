-- UniQPaid Database Schema
-- Единая база данных для всех компонентов приложения

-- Таблица пользователей
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0,
    total_earned DECIMAL(10,2) DEFAULT 0,
    referrer_id BIGINT NULL,
    referral_earnings DECIMAL(10,2) DEFAULT 0,
    first_name TEXT,
    last_name TEXT,
    username TEXT,
    language TEXT DEFAULT 'ru',
    registered_at INTEGER NOT NULL,
    last_activity INTEGER NOT NULL,
    suspicious_activity_count INTEGER DEFAULT 0,
    withdrawals_count INTEGER DEFAULT 0,
    blocked BOOLEAN DEFAULT 0,
    blocked_at INTEGER NULL,
    referrals_count INTEGER DEFAULT 0,
    joined INTEGER NOT NULL,
    suspicious_activity INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Таблица лимитов рекламы
CREATE TABLE IF NOT EXISTS ad_limits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    ad_type TEXT NOT NULL,
    daily_count INTEGER DEFAULT 0,
    last_reset_date DATE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id),
    UNIQUE(user_id, ad_type, last_reset_date)
);

-- Таблица логов мошенничества
CREATE TABLE IF NOT EXISTS fraud_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    user_id TEXT NOT NULL,
    violation_type TEXT NOT NULL,
    risk_level TEXT NOT NULL,
    risk_score INTEGER NOT NULL,
    action TEXT NOT NULL,
    details TEXT,
    fingerprint TEXT,
    ip_address TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Таблица просмотров рекламы
CREATE TABLE IF NOT EXISTS ad_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    ad_type TEXT NOT NULL,
    reward DECIMAL(10,2) NOT NULL,
    timestamp DATETIME NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id)
);

-- Таблица кликов по рекламе
CREATE TABLE IF NOT EXISTS ad_clicks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    ad_type TEXT NOT NULL,
    click_type TEXT NOT NULL,
    reason TEXT,
    timestamp DATETIME NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id)
);

-- Таблица токенов рекламы
CREATE TABLE IF NOT EXISTS ad_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token TEXT UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    ad_type TEXT NOT NULL,
    used BOOLEAN DEFAULT 0,
    created_at INTEGER NOT NULL,
    expires_at INTEGER NOT NULL,
    used_at INTEGER NULL,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id)
);

-- Таблица отпечатков устройств
CREATE TABLE IF NOT EXISTS device_fingerprints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    fingerprint_hash TEXT NOT NULL,
    device_info TEXT,
    first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id),
    UNIQUE(user_id, fingerprint_hash)
);

-- Таблица анализа мошенничества
CREATE TABLE IF NOT EXISTS fraud_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,
    user_id TEXT NOT NULL,
    ip_address TEXT,
    fingerprint TEXT,
    violations_count INTEGER DEFAULT 0,
    risk_score INTEGER DEFAULT 0,
    should_block BOOLEAN DEFAULT 0,
    violations TEXT, -- JSON array of violations
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Таблица заблокированных устройств
CREATE TABLE IF NOT EXISTS blocked_devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id TEXT UNIQUE NOT NULL,
    fingerprint TEXT,
    reason TEXT,
    blocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    blocked_by TEXT,
    is_active BOOLEAN DEFAULT 1
);

-- Таблица блокировок VPN
CREATE TABLE IF NOT EXISTS vpn_blocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address TEXT NOT NULL,
    country_code TEXT,
    provider TEXT,
    confidence_score DECIMAL(3,2),
    blocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    UNIQUE(ip_address)
);

-- Таблица кэша стран по IP
CREATE TABLE IF NOT EXISTS ip_country_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address TEXT UNIQUE NOT NULL,
    country_code TEXT NOT NULL,
    cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL
);

-- Таблица выводов средств
CREATE TABLE IF NOT EXISTS user_withdrawals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT NOT NULL,
    wallet_address TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    transaction_hash TEXT,
    requested_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME NULL,
    error_message TEXT,
    FOREIGN KEY (user_id) REFERENCES users(telegram_id)
);

-- Таблица рефералов
CREATE TABLE IF NOT EXISTS user_referrals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    referrer_id BIGINT NOT NULL,
    referred_id BIGINT NOT NULL,
    earnings DECIMAL(10,2) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(telegram_id),
    FOREIGN KEY (referred_id) REFERENCES users(telegram_id),
    UNIQUE(referrer_id, referred_id)
);

-- Таблица текстов бота
CREATE TABLE IF NOT EXISTS bot_texts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    language_code TEXT NOT NULL,
    text_key TEXT NOT NULL,
    text_value TEXT NOT NULL,
    category TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(language_code, text_key)
);

-- Таблица настроек бота
CREATE TABLE IF NOT EXISTS bot_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Таблица настроек антифрода
CREATE TABLE IF NOT EXISTS antifraud_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_name TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    data_type TEXT DEFAULT 'string', -- string, integer, boolean, json
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Таблица сообщений поддержки
CREATE TABLE IF NOT EXISTS support_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chat_id TEXT NOT NULL,
    message_id INTEGER NOT NULL,
    from_user BOOLEAN NOT NULL,
    text TEXT NOT NULL,
    created_at DATETIME NOT NULL
);

-- Индексы для оптимизации производительности
CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_users_referrer_id ON users(referrer_id);
CREATE INDEX IF NOT EXISTS idx_users_registered_at ON users(registered_at);

CREATE INDEX IF NOT EXISTS idx_ad_views_user_id ON ad_views(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_views_timestamp ON ad_views(timestamp);
CREATE INDEX IF NOT EXISTS idx_ad_views_ad_type ON ad_views(ad_type);

CREATE INDEX IF NOT EXISTS idx_ad_clicks_user_id ON ad_clicks(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_timestamp ON ad_clicks(timestamp);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_ad_type ON ad_clicks(ad_type);

CREATE INDEX IF NOT EXISTS idx_ad_tokens_user_id ON ad_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_tokens_expires_at ON ad_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_ad_tokens_used ON ad_tokens(used);

CREATE INDEX IF NOT EXISTS idx_fraud_analysis_user_id ON fraud_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_fraud_analysis_timestamp ON fraud_analysis(timestamp);
CREATE INDEX IF NOT EXISTS idx_fraud_analysis_risk_score ON fraud_analysis(risk_score);

CREATE INDEX IF NOT EXISTS idx_device_fingerprints_user_id ON device_fingerprints(user_id);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_hash ON device_fingerprints(fingerprint_hash);

CREATE INDEX IF NOT EXISTS idx_support_messages_chat_id ON support_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_support_messages_created_at ON support_messages(created_at);

CREATE INDEX IF NOT EXISTS idx_user_withdrawals_user_id ON user_withdrawals(user_id);
CREATE INDEX IF NOT EXISTS idx_user_withdrawals_status ON user_withdrawals(status);
CREATE INDEX IF NOT EXISTS idx_user_withdrawals_requested_at ON user_withdrawals(requested_at);

CREATE INDEX IF NOT EXISTS idx_bot_texts_language ON bot_texts(language_code);
CREATE INDEX IF NOT EXISTS idx_bot_texts_key ON bot_texts(text_key);