<?php
/**
 * api/getUserBalance_simple.php
 * Упрощенный API для получения баланса пользователя (без резервирования)
 */

declare(strict_types=1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Получаем данные запроса
    $input = null;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
    } else {
        $input = $_GET;
    }
    
    if (!$input) {
        throw new Exception('No input data provided');
    }
    
    // Получаем ID пользователя
    $userId = null;
    
    if (isset($input['user_id'])) {
        $userId = (string)$input['user_id'];
    } elseif (isset($input['initData'])) {
        // Парсим initData из Telegram
        parse_str($input['initData'], $initDataParsed);
        if (isset($initDataParsed['user'])) {
            $userData = json_decode($initDataParsed['user'], true);
            if ($userData && isset($userData['id'])) {
                $userId = (string)$userData['id'];
            }
        }
    }
    
    if (!$userId) {
        throw new Exception('User ID not provided');
    }
    
    // Инициализируем менеджеры
    require_once __DIR__ . '/coins_manager.php';
    require_once __DIR__ . '/../database/real_sqlite_manager.php';
    
    $coinsManager = new CoinsManager();
    $db = new RealSQLiteManager();
    
    // Получаем баланс пользователя
    $balance = $coinsManager->getUserBalance($userId);
    
    // Получаем дневную статистику
    $dailyStats = $coinsManager->getDailyStats($userId);
    
    // Получаем настройки системы
    $settings = $coinsManager->getSystemSettings();
    
    // Получаем общую статистику пользователя
    $userStats = $db->query(
        "SELECT 
            COALESCE(SUM(CASE WHEN operation = 'credit' THEN amount ELSE 0 END), 0) as total_earned,
            COALESCE(SUM(CASE WHEN operation = 'debit' AND transaction_type LIKE '%withdraw%' THEN amount ELSE 0 END), 0) as total_withdrawn
         FROM coin_transactions 
         WHERE user_id = ?",
        [$userId]
    );
    
    $totalEarned = $userStats[0]['total_earned'] ?? 0;
    $totalWithdrawn = $userStats[0]['total_withdrawn'] ?? 0;
    
    // Получаем информацию о пользователе
    $userInfo = $db->query(
        "SELECT telegram_id, username, first_name, last_name, created_at 
         FROM users 
         WHERE telegram_id = ?",
        [$userId]
    );
    
    // Получаем последние транзакции
    $recentTransactions = $coinsManager->getUserTransactions($userId, 10);
    
    // Рассчитываем баланс в USD (простой расчет)
    $coinRateUSD = $settings['coin_rate_usd'] ?? 0.001;
    $balanceUSD = $balance['balance'] * $coinRateUSD;
    
    // Упрощенная схема: весь баланс доступен для вывода
    $availableBalance = $balance['balance'];
    $reservedBalance = 0; // Резервирование отключено
    
    // Проверяем возможность вывода
    $minWithdrawal = $settings['min_withdrawal'] ?? 1000;
    $canWithdraw = $availableBalance >= $minWithdrawal;
    
    // Формируем ответ
    $response = [
        'success' => true,
        'balance' => (float)$balance['balance'],
        'available_balance' => (float)$availableBalance,
        'reserved_balance' => (float)$reservedBalance,
        'total_earned' => (float)$totalEarned,
        'total_withdrawn' => (float)$totalWithdrawn,
        'balance_usd' => round($balanceUSD, 4),
        'daily_stats' => [
            'earned_today' => (float)($dailyStats['earned_today'] ?? 0),
            'limit' => (float)($dailyStats['limit'] ?? 200),
            'remaining' => (float)(($dailyStats['limit'] ?? 200) - ($dailyStats['earned_today'] ?? 0)),
            'can_earn_more' => ($dailyStats['earned_today'] ?? 0) < ($dailyStats['limit'] ?? 200)
        ],
        'withdrawal_settings' => [
            'min_withdrawal' => (float)$minWithdrawal,
            'coin_rate_usd' => (float)$coinRateUSD,
            'can_withdraw' => $canWithdraw,
            'min_balance_for_access' => (float)($settings['min_balance_for_access'] ?? 100)
        ],
        'recent_transactions' => array_map(function($txn) {
            return [
                'id' => $txn['id'],
                'type' => $txn['transaction_type'],
                'amount' => (float)$txn['amount'],
                'operation' => $txn['operation'],
                'description' => $txn['description'],
                'created_at' => $txn['created_at']
            ];
        }, $recentTransactions),
        'user_info' => !empty($userInfo) ? [
            'telegram_id' => $userInfo[0]['telegram_id'],
            'username' => $userInfo[0]['username'],
            'first_name' => $userInfo[0]['first_name'],
            'last_name' => $userInfo[0]['last_name'],
            'member_since' => $userInfo[0]['created_at']
        ] : null,
        'system_info' => [
            'api_version' => 'simple_v1.0',
            'reservation_enabled' => false,
            'sqlite_backend' => true,
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("getUserBalance_simple ERROR: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => 'BALANCE_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
