<?php
/**
 * Быстрое исправление проблем с webhook
 * Автоматически определяет и исправляет типичные проблемы
 */

require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/../../includes/bot_config_loader.php';
defineBotConstants();

header('Content-Type: application/json');

/**
 * Проверяет и исправляет webhook
 */
function quickFixWebhook($botToken, $webhookUrl, $botName) {
    $issues = [];
    $fixes = [];
    
    // 1. Проверяем токен
    $getMeUrl = "https://api.telegram.org/bot{$botToken}/getMe";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $getMeUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200 || !$result) {
        $issues[] = "Неверный токен бота";
        return ['issues' => $issues, 'fixes' => $fixes, 'success' => false];
    }
    
    $response = json_decode($result, true);
    if (!$response || !$response['ok']) {
        $issues[] = "Токен бота не работает";
        return ['issues' => $issues, 'fixes' => $fixes, 'success' => false];
    }
    
    // 2. Получаем текущую информацию о webhook
    $webhookInfoUrl = "https://api.telegram.org/bot{$botToken}/getWebhookInfo";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookInfoUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    $currentWebhook = '';
    $hasErrors = false;
    
    if ($result) {
        $webhookInfo = json_decode($result, true);
        if ($webhookInfo && $webhookInfo['ok']) {
            $info = $webhookInfo['result'];
            $currentWebhook = $info['url'] ?? '';
            
            if (!empty($info['last_error_message'])) {
                $issues[] = "Последняя ошибка webhook: " . $info['last_error_message'];
                $hasErrors = true;
            }
            
            if ($info['pending_update_count'] > 10) {
                $issues[] = "Много ожидающих обновлений: " . $info['pending_update_count'];
                $hasErrors = true;
            }
        }
    }
    
    // 3. Проверяем, нужно ли обновление
    $needsUpdate = false;
    
    if ($currentWebhook !== $webhookUrl) {
        $issues[] = "Webhook URL не соответствует настройкам";
        $needsUpdate = true;
    }
    
    if ($hasErrors) {
        $needsUpdate = true;
    }
    
    if (empty($currentWebhook)) {
        $issues[] = "Webhook не установлен";
        $needsUpdate = true;
    }
    
    // 4. Исправляем проблемы
    if ($needsUpdate) {
        // Сначала удаляем старый webhook
        $deleteUrl = "https://api.telegram.org/bot{$botToken}/deleteWebhook";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $deleteUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'drop_pending_updates=true');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        $fixes[] = "Удален старый webhook";
        
        // Ждем немного
        sleep(2);
        
        // Устанавливаем новый webhook
        $setWebhookUrl = "https://api.telegram.org/bot{$botToken}/setWebhook";
        $data = [
            'url' => $webhookUrl,
            'allowed_updates' => ['message', 'callback_query'],
            'drop_pending_updates' => true
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $setWebhookUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($result && $httpCode === 200) {
            $response = json_decode($result, true);
            if ($response && $response['ok']) {
                $fixes[] = "Установлен новый webhook: " . $webhookUrl;
                return ['issues' => $issues, 'fixes' => $fixes, 'success' => true];
            } else {
                $issues[] = "Ошибка установки webhook: " . ($response['description'] ?? 'Неизвестная ошибка');
                return ['issues' => $issues, 'fixes' => $fixes, 'success' => false];
            }
        } else {
            $issues[] = "HTTP ошибка при установке webhook: " . $httpCode;
            return ['issues' => $issues, 'fixes' => $fixes, 'success' => false];
        }
    }
    
    if (empty($issues)) {
        $fixes[] = "Webhook работает корректно";
    }
    
    return ['issues' => $issues, 'fixes' => $fixes, 'success' => true];
}

try {
    $botType = $_GET['bot'] ?? 'both';
    $results = [];
    
    if ($botType === 'main' || $botType === 'both') {
        $results['main_bot'] = quickFixWebhook(
            TELEGRAM_BOT_TOKEN, 
            WEBHOOK_URL, 
            'Основной бот'
        );
    }
    
    if ($botType === 'support' || $botType === 'both') {
        $results['support_bot'] = quickFixWebhook(
            SUPPORT_BOT_TOKEN, 
            SUPPORT_WEBHOOK_URL, 
            'Бот поддержки'
        );
    }
    
    $allSuccess = true;
    foreach ($results as $result) {
        if (!$result['success']) {
            $allSuccess = false;
            break;
        }
    }
    
    $response = [
        'success' => $allSuccess,
        'results' => $results,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
