<?php
/**
 * check_dates_migration.php
 * Проверка миграции дат и времени
 */

declare(strict_types=1);

echo "📅 ПРОВЕРКА МИГРАЦИИ ДАТ И ВРЕМЕНИ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    require_once 'database/real_sqlite_manager.php';
    $sqlite = new RealSQLiteManager();
    
    echo "1. 📊 ДАТЫ В ПРОСМОТРАХ:\n";
    
    $viewDates = $sqlite->query("
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as count,
            MIN(timestamp) as first_time,
            MAX(timestamp) as last_time
        FROM ad_views 
        GROUP BY DATE(timestamp) 
        ORDER BY date DESC 
        LIMIT 10
    ");
    
    foreach ($viewDates as $date) {
        echo "   - {$date['date']}: {$date['count']} просмотров (с {$date['first_time']} до {$date['last_time']})\n";
    }
    
    echo "\n2. 📊 ДАТЫ В КЛИКАХ:\n";
    
    $clickDates = $sqlite->query("
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as count,
            MIN(timestamp) as first_time,
            MAX(timestamp) as last_time
        FROM ad_clicks 
        GROUP BY DATE(timestamp) 
        ORDER BY date DESC 
        LIMIT 10
    ");
    
    foreach ($clickDates as $date) {
        echo "   - {$date['date']}: {$date['count']} кликов (с {$date['first_time']} до {$date['last_time']})\n";
    }
    
    echo "\n3. 🔍 ПОСЛЕДНИЕ ЗАПИСИ:\n";
    
    echo "   👁️ Последние просмотры:\n";
    $recentViews = $sqlite->query("
        SELECT user_id, ad_type, timestamp, ip_address
        FROM ad_views 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    foreach ($recentViews as $view) {
        $ip = $view['ip_address'] ? substr($view['ip_address'], 0, 12) . '...' : 'N/A';
        echo "     - {$view['timestamp']}: User {$view['user_id']}, {$view['ad_type']}, IP: {$ip}\n";
    }
    
    echo "\n   🖱️ Последние клики:\n";
    $recentClicks = $sqlite->query("
        SELECT user_id, ad_type, timestamp, ip_address
        FROM ad_clicks 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    foreach ($recentClicks as $click) {
        $ip = $click['ip_address'] ? substr($click['ip_address'], 0, 12) . '...' : 'N/A';
        echo "     - {$click['timestamp']}: User {$click['user_id']}, {$click['ad_type']}, IP: {$ip}\n";
    }
    
    echo "\n4. 📅 ТЕСТ ФИЛЬТРАЦИИ ПО ДАТЕ:\n";
    
    // Тестируем фильтрацию за последние 7 дней
    echo "   📊 Последние 7 дней:\n";
    $last7Days = $sqlite->query("
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as clicks
        FROM ad_clicks
        WHERE DATE(timestamp) >= DATE('now', '-7 days')
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
    ");
    
    foreach ($last7Days as $day) {
        echo "     - {$day['date']}: {$day['clicks']} кликов\n";
    }
    
    // Тестируем фильтрацию за конкретную дату
    echo "\n   📊 За 2025-07-16:\n";
    $specificDate = $sqlite->query("
        SELECT 
            ad_type,
            COUNT(*) as clicks
        FROM ad_clicks
        WHERE DATE(timestamp) = '2025-07-16'
        GROUP BY ad_type
        ORDER BY clicks DESC
    ");
    
    foreach ($specificDate as $type) {
        echo "     - {$type['ad_type']}: {$type['clicks']} кликов\n";
    }
    
    // Тестируем диапазон дат
    echo "\n   📊 С 2025-07-15 по 2025-07-17:\n";
    $dateRange = $sqlite->query("
        SELECT 
            DATE(timestamp) as date,
            ad_type,
            COUNT(*) as clicks
        FROM ad_clicks
        WHERE DATE(timestamp) BETWEEN '2025-07-15' AND '2025-07-17'
        GROUP BY DATE(timestamp), ad_type
        ORDER BY date DESC, clicks DESC
    ");
    
    foreach ($dateRange as $range) {
        echo "     - {$range['date']} {$range['ad_type']}: {$range['clicks']} кликов\n";
    }
    
    echo "\n5. ⏰ ПОЧАСОВАЯ СТАТИСТИКА:\n";
    
    $hourlyStats = $sqlite->query("
        SELECT 
            CAST(strftime('%H', timestamp) AS INTEGER) as hour,
            COUNT(*) as clicks
        FROM ad_clicks
        WHERE DATE(timestamp) >= DATE('now', '-1 days')
        GROUP BY hour
        ORDER BY hour
    ");
    
    echo "   📊 Клики по часам (за последние сутки):\n";
    foreach ($hourlyStats as $hour) {
        $hourFormatted = sprintf('%02d:00', $hour['hour']);
        echo "     - {$hourFormatted}: {$hour['clicks']} кликов\n";
    }
    
    echo "\n6. 🔍 СРАВНЕНИЕ С JSON:\n";
    
    // Проверяем несколько записей из JSON
    echo "   📊 Проверяем соответствие с исходными JSON данными:\n";
    
    // Читаем первые записи из JSON
    $jsonClicks = json_decode(file_get_contents('database/ad_clicks.json'), true);
    $jsonViews = json_decode(file_get_contents('database/ad_views.json'), true);
    
    echo "   🖱️ Первые клики из JSON:\n";
    for ($i = 0; $i < min(3, count($jsonClicks)); $i++) {
        $click = $jsonClicks[$i];
        echo "     - JSON: {$click['timestamp']}, User: {$click['user_id']}, Type: {$click['ad_type']}\n";
        
        // Ищем эту запись в SQLite
        $sqliteClick = $sqlite->query("
            SELECT timestamp, user_id, ad_type 
            FROM ad_clicks 
            WHERE user_id = ? AND ad_type = ? AND timestamp = ?
            LIMIT 1
        ", [$click['user_id'], $click['ad_type'], $click['timestamp']]);
        
        if (!empty($sqliteClick)) {
            echo "     - SQLite: ✅ Найдено - {$sqliteClick[0]['timestamp']}\n";
        } else {
            echo "     - SQLite: ❌ НЕ найдено!\n";
        }
    }
    
    echo "\n   👁️ Первые просмотры из JSON:\n";
    for ($i = 0; $i < min(3, count($jsonViews)); $i++) {
        $view = $jsonViews[$i];
        echo "     - JSON: {$view['timestamp']}, User: {$view['user_id']}, Type: {$view['ad_type']}\n";
        
        // Ищем эту запись в SQLite
        $sqliteView = $sqlite->query("
            SELECT timestamp, user_id, ad_type 
            FROM ad_views 
            WHERE user_id = ? AND ad_type = ? AND timestamp = ?
            LIMIT 1
        ", [$view['user_id'], $view['ad_type'], $view['timestamp']]);
        
        if (!empty($sqliteView)) {
            echo "     - SQLite: ✅ Найдено - {$sqliteView[0]['timestamp']}\n";
        } else {
            echo "     - SQLite: ❌ НЕ найдено!\n";
        }
    }
    
    echo "\n✅ РЕЗУЛЬТАТ ПРОВЕРКИ:\n";
    
    $totalDaysWithClicks = count($clickDates);
    $totalDaysWithViews = count($viewDates);
    
    echo "   📅 Дней с кликами: {$totalDaysWithClicks}\n";
    echo "   📅 Дней с просмотрами: {$totalDaysWithViews}\n";
    
    if ($totalDaysWithClicks > 1) {
        echo "   ✅ Даты в кликах мигрированы правильно\n";
    } else {
        echo "   ❌ Проблема с датами в кликах\n";
    }
    
    if ($totalDaysWithViews > 1) {
        echo "   ✅ Даты в просмотрах мигрированы правильно\n";
    } else {
        echo "   ❌ Проблема с датами в просмотрах\n";
    }
    
    echo "   ✅ Фильтрация по датам должна работать\n";
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
