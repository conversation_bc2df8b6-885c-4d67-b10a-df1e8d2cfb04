<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
/**
 * api/admin/monitor.php
 * Страница мониторинга выплат в реальном времени
 */

// Отключаем вывод ошибок в браузер
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Проверяем аутентификацию
session_start();
require_once __DIR__ . '/auth.php';
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">        <?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */ include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">📊 Мониторинг выплат в реальном времени</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button onclick="refreshMonitor()" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button onclick="toggleAutoRefresh()" id="auto-refresh-btn" class="btn btn-sm btn-outline-success">
                            <i class="bi bi-play"></i> Авто-обновление
                        </button>
                    </div>
                </div>
            </div>

            <!-- Статус подключения -->
            <div class="alert alert-info" role="alert" id="connection-status">
                <i class="bi bi-wifi"></i> Подключение к мониторингу...
            </div>

            <!-- Сводка -->
            <div class="row" id="summary-cards">
                <!-- Карточки будут загружены через JavaScript -->
            </div>

            <!-- Табы с выплатами -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="withdrawal-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                                <i class="bi bi-clock"></i> Ожидающие <span class="badge bg-warning" id="pending-count">0</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
                                <i class="bi bi-check-circle"></i> Завершенные <span class="badge bg-success" id="completed-count">0</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="failed-tab" data-bs-toggle="tab" data-bs-target="#failed" type="button" role="tab">
                                <i class="bi bi-x-circle"></i> Неудачные <span class="badge bg-danger" id="failed-count">0</span>
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="withdrawal-tab-content">
                        <div class="tab-pane fade show active" id="pending" role="tabpanel">
                            <div id="pending-withdrawals">Загрузка...</div>
                        </div>
                        <div class="tab-pane fade" id="completed" role="tabpanel">
                            <div id="completed-withdrawals">Загрузка...</div>
                        </div>
                        <div class="tab-pane fade" id="failed" role="tabpanel">
                            <div id="failed-withdrawals">Загрузка...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Последнее обновление -->
            <div class="text-center text-muted">
                <small>Последнее обновление: <span id="last-update">-</span></small>
            </div>

        </main>
    </div>
</div>

<script>
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

// Функция загрузки данных мониторинга
async function loadMonitorData() {
    try {
        const response = await fetch('monitor_withdrawals.php');
        const data = await response.json();
        
        if (data.success) {
            updateSummaryCards(data.summary);
            updateWithdrawalTabs(data.withdrawals);
            updateConnectionStatus(true);
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        } else {
            throw new Error(data.error || 'Ошибка загрузки данных');
        }
    } catch (error) {
        console.error('Ошибка мониторинга:', error);
        updateConnectionStatus(false, error.message);
    }
}

// Обновление карточек сводки
function updateSummaryCards(summary) {
    const summaryHtml = `
        <div class="col-md-3 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Всего выплат</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${summary.total_withdrawals}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Общая сумма</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">$${summary.total_amount_usd.toFixed(2)}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">В обработке</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${summary.pending_count}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Баланс</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">$${summary.balance_usd.toFixed(2)}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-wallet2 fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('summary-cards').innerHTML = summaryHtml;
    
    // Обновляем счетчики в табах
    document.getElementById('pending-count').textContent = summary.pending_count;
    document.getElementById('completed-count').textContent = summary.completed_count;
    document.getElementById('failed-count').textContent = summary.failed_count;
}

// Обновление табов с выплатами
function updateWithdrawalTabs(withdrawals) {
    updateWithdrawalList('pending-withdrawals', withdrawals.pending, 'warning');
    updateWithdrawalList('completed-withdrawals', withdrawals.completed, 'success');
    updateWithdrawalList('failed-withdrawals', withdrawals.failed, 'danger');
}

// Обновление списка выплат
function updateWithdrawalList(elementId, withdrawals, statusClass) {
    const element = document.getElementById(elementId);
    
    if (!withdrawals || withdrawals.length === 0) {
        element.innerHTML = '<div class="text-center text-muted py-4">Нет выплат</div>';
        return;
    }
    
    const html = withdrawals.map(w => `
        <div class="card mb-2 border-${statusClass}">
            <div class="card-body p-3">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <strong>${w.user_name}</strong><br>
                        <small class="text-muted">ID: ${w.user_id}</small>
                    </div>
                    <div class="col-md-2">
                        <strong>${w.amount} монет</strong><br>
                        <small class="text-muted">$${w.usd_amount.toFixed(2)}</small>
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-${statusClass}">${w.currency.toUpperCase()}</span><br>
                        <small class="text-muted">${w.status}</small>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">${w.address.substring(0, 20)}...</small>
                    </div>
                    <div class="col-md-2 text-end">
                        <small class="text-muted">${new Date(w.created_at).toLocaleString()}</small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    element.innerHTML = html;
}

// Обновление статуса подключения
function updateConnectionStatus(isConnected, errorMessage = '') {
    const statusElement = document.getElementById('connection-status');
    
    if (isConnected) {
        statusElement.className = 'alert alert-success';
        statusElement.innerHTML = '<i class="bi bi-wifi"></i> Подключение активно - данные обновляются';
    } else {
        statusElement.className = 'alert alert-danger';
        statusElement.innerHTML = `<i class="bi bi-wifi-off"></i> Ошибка подключения: ${errorMessage}`;
    }
}

// Функция обновления
function refreshMonitor() {
    loadMonitorData();
}

// Переключение автообновления
function toggleAutoRefresh() {
    const btn = document.getElementById('auto-refresh-btn');
    
    if (isAutoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        isAutoRefreshEnabled = false;
        btn.innerHTML = '<i class="bi bi-play"></i> Авто-обновление';
        btn.className = 'btn btn-sm btn-outline-success';
    } else {
        autoRefreshInterval = setInterval(loadMonitorData, 30000); // Каждые 30 секунд
        isAutoRefreshEnabled = true;
        btn.innerHTML = '<i class="bi bi-pause"></i> Остановить';
        btn.className = 'btn btn-sm btn-outline-danger';
    }
}

// Инициализация
document.addEventListener('DOMContentLoaded', function() {
    loadMonitorData();
    
    // Включаем автообновление по умолчанию
    toggleAutoRefresh();
});
</script>

<?php
/**
 * ОБНОВЛЕНО: Использует SQLite базу данных
 * Дата обновления: 2025-07-17 03:49:46
 */
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
