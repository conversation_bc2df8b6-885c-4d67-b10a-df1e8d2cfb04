<?php
/**
 * check_withdrawals_data.php
 * Проверка данных выводов и переводов в SQLite
 */

declare(strict_types=1);

require_once 'database/real_sqlite_manager.php';

echo "🔍 ПРОВЕРКА ДАННЫХ ВЫВОДОВ И ПЕРЕВОДОВ\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $sqlite = new RealSQLiteManager();
    
    // Проверяем таблицу выводов
    echo "📊 ТАБЛИЦА ВЫВОДОВ (user_withdrawals):\n";
    $withdrawals = $sqlite->query('SELECT COUNT(*) as count FROM user_withdrawals');
    echo "  Количество записей: " . $withdrawals[0]['count'] . "\n";
    
    if ($withdrawals[0]['count'] > 0) {
        $recent = $sqlite->query('SELECT * FROM user_withdrawals ORDER BY requested_at DESC LIMIT 5');
        echo "\n  📋 Последние 5 выводов:\n";
        foreach ($recent as $w) {
            echo "    - ID: {$w['id']}, User: {$w['user_id']}, Amount: {$w['amount']} {$w['currency']}, Status: {$w['status']}\n";
        }
    } else {
        echo "  ⚠️ ТАБЛИЦА ПУСТАЯ!\n";
    }
    
    // Проверяем таблицу текстов бота
    echo "\n📝 ТАБЛИЦА ТЕКСТОВ БОТА (bot_texts):\n";
    $texts = $sqlite->query('SELECT COUNT(*) as count FROM bot_texts');
    echo "  Количество записей: " . $texts[0]['count'] . "\n";
    
    if ($texts[0]['count'] > 0) {
        $sample = $sqlite->query('SELECT language_code, text_key, text_value FROM bot_texts LIMIT 5');
        echo "\n  📋 Примеры текстов:\n";
        foreach ($sample as $t) {
            $value = strlen($t['text_value']) > 50 ? substr($t['text_value'], 0, 50) . "..." : $t['text_value'];
            echo "    - [{$t['language_code']}] {$t['text_key']}: {$value}\n";
        }
    } else {
        echo "  ⚠️ ТАБЛИЦА ПУСТАЯ!\n";
    }
    
    // Проверяем таблицу пользователей
    echo "\n👥 ТАБЛИЦА ПОЛЬЗОВАТЕЛЕЙ (users):\n";
    $users = $sqlite->query('SELECT COUNT(*) as count FROM users');
    echo "  Количество записей: " . $users[0]['count'] . "\n";
    
    // Проверяем таблицу просмотров рекламы
    echo "\n📺 ТАБЛИЦА ПРОСМОТРОВ РЕКЛАМЫ (ad_views):\n";
    $views = $sqlite->query('SELECT COUNT(*) as count FROM ad_views');
    echo "  Количество записей: " . $views[0]['count'] . "\n";
    
    // Проверяем все таблицы
    echo "\n📋 ВСЕ ТАБЛИЦЫ В БАЗЕ:\n";
    $tables = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
    foreach ($tables as $table) {
        $count = $sqlite->query("SELECT COUNT(*) as count FROM {$table['name']}");
        echo "  - {$table['name']}: " . $count[0]['count'] . " записей\n";
    }
    
} catch (Exception $e) {
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!\n";
?>
